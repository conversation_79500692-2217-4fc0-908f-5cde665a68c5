{"name": "composer/composer", "type": "library", "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "keywords": ["package", "dependency", "autoload"], "homepage": "https://getcomposer.org/", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "require": {"php": "^7.2.5 || ^8.0", "composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^5.3", "psr/log": "^1.0 || ^2.0 || ^3.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3", "react/promise": "^3.2", "composer/pcre": "^2.2 || ^3.2", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "seld/signal-handler": "^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^6.4.3 || ^7.0.1", "phpstan/phpstan": "^1.11.8", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "config": {"platform": {"php": "7.2.5"}, "platform-check": false}, "extra": {"branch-alias": {"dev-main": "2.8-dev"}, "phpstan": {"includes": ["phpstan/rules.neon"]}}, "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "autoload-dev": {"psr-4": {"Composer\\Test\\": "tests/Composer/Test/"}, "exclude-from-classmap": ["tests/Composer/Test/Fixtures/", "tests/Composer/Test/Autoload/Fixtures", "tests/Composer/Test/Autoload/MinimumVersionSupport", "tests/Composer/Test/Plugin/Fixtures"]}, "bin": ["bin/composer"], "scripts": {"compile": "@php -dphar.readonly=0 bin/compile", "test": "@php simple-phpunit", "phpstan": "@php vendor/bin/phpstan analyse --configuration=phpstan/config.neon"}, "scripts-descriptions": {"compile": "Compile composer.phar", "test": "Run all tests", "phpstan": "Runs PHPStan"}, "support": {"issues": "https://github.com/composer/composer/issues", "irc": "ircs://irc.libera.chat:6697/composer", "security": "https://github.com/composer/composer/security/policy"}}
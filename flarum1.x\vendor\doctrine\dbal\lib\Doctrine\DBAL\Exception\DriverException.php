<?php

namespace Doctrine\DBAL\Exception;

use Doctrine\DBAL\Driver\DriverException as DeprecatedDriverException;
use Doctrine\DBAL\Exception;

/**
 * Base class for all errors detected in the driver.
 *
 * @psalm-immutable
 */
class DriverException extends Exception
{
    /**
     * The previous DBAL driver exception.
     *
     * @var DeprecatedDriverException
     */
    private $driverException;

    /**
     * @param string                    $message         The exception message.
     * @param DeprecatedDriverException $driverException The DBAL driver exception to chain.
     */
    public function __construct($message, DeprecatedDriverException $driverException)
    {
        parent::__construct($message, 0, $driverException);

        $this->driverException = $driverException;
    }

    /**
     * Returns the driver specific error code if given.
     *
     * Returns null if no error code was given by the driver.
     *
     * @return int|string|null
     */
    public function getErrorCode()
    {
        return $this->driverException->getErrorCode();
    }

    /**
     * Returns the SQLSTATE the driver was in at the time the error occurred, if given.
     *
     * Returns null if no SQLSTATE was given by the driver.
     *
     * @return string|null
     */
    public function getSQLState()
    {
        return $this->driverException->getSQLState();
    }
}

<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'CURLStringFile' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'IntlException' => $vendorDir . '/symfony/polyfill-intl-messageformatter/Resources/stubs/IntlException.php',
    'JsonException' => $vendorDir . '/symfony/polyfill-php73/Resources/stubs/JsonException.php',
    'MessageFormatter' => $vendorDir . '/symfony/polyfill-intl-messageformatter/Resources/stubs/MessageFormatter.php',
    'Mobile_Detect' => $vendorDir . '/mobiledetect/mobiledetectlib/Mobile_Detect.php',
    'Nette\\ArgumentOutOfRangeException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\DeprecatedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\DirectoryNotFoundException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\FileNotFoundException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\HtmlStringable' => $vendorDir . '/nette/utils/src/HtmlStringable.php',
    'Nette\\IOException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\InvalidArgumentException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\InvalidStateException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Iterators\\CachingIterator' => $vendorDir . '/nette/utils/src/Iterators/CachingIterator.php',
    'Nette\\Iterators\\Mapper' => $vendorDir . '/nette/utils/src/Iterators/Mapper.php',
    'Nette\\Localization\\ITranslator' => $vendorDir . '/nette/utils/src/compatibility.php',
    'Nette\\Localization\\Translator' => $vendorDir . '/nette/utils/src/Translator.php',
    'Nette\\MemberAccessException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\NotImplementedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\NotSupportedException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\OutOfRangeException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Schema\\Context' => $vendorDir . '/nette/schema/src/Schema/Context.php',
    'Nette\\Schema\\DynamicParameter' => $vendorDir . '/nette/schema/src/Schema/DynamicParameter.php',
    'Nette\\Schema\\Elements\\AnyOf' => $vendorDir . '/nette/schema/src/Schema/Elements/AnyOf.php',
    'Nette\\Schema\\Elements\\Base' => $vendorDir . '/nette/schema/src/Schema/Elements/Base.php',
    'Nette\\Schema\\Elements\\Structure' => $vendorDir . '/nette/schema/src/Schema/Elements/Structure.php',
    'Nette\\Schema\\Elements\\Type' => $vendorDir . '/nette/schema/src/Schema/Elements/Type.php',
    'Nette\\Schema\\Expect' => $vendorDir . '/nette/schema/src/Schema/Expect.php',
    'Nette\\Schema\\Helpers' => $vendorDir . '/nette/schema/src/Schema/Helpers.php',
    'Nette\\Schema\\Message' => $vendorDir . '/nette/schema/src/Schema/Message.php',
    'Nette\\Schema\\Processor' => $vendorDir . '/nette/schema/src/Schema/Processor.php',
    'Nette\\Schema\\Schema' => $vendorDir . '/nette/schema/src/Schema/Schema.php',
    'Nette\\Schema\\ValidationException' => $vendorDir . '/nette/schema/src/Schema/ValidationException.php',
    'Nette\\SmartObject' => $vendorDir . '/nette/utils/src/SmartObject.php',
    'Nette\\StaticClass' => $vendorDir . '/nette/utils/src/StaticClass.php',
    'Nette\\UnexpectedValueException' => $vendorDir . '/nette/utils/src/exceptions.php',
    'Nette\\Utils\\ArrayHash' => $vendorDir . '/nette/utils/src/Utils/ArrayHash.php',
    'Nette\\Utils\\ArrayList' => $vendorDir . '/nette/utils/src/Utils/ArrayList.php',
    'Nette\\Utils\\Arrays' => $vendorDir . '/nette/utils/src/Utils/Arrays.php',
    'Nette\\Utils\\AssertionException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Callback' => $vendorDir . '/nette/utils/src/Utils/Callback.php',
    'Nette\\Utils\\DateTime' => $vendorDir . '/nette/utils/src/Utils/DateTime.php',
    'Nette\\Utils\\FileInfo' => $vendorDir . '/nette/utils/src/Utils/FileInfo.php',
    'Nette\\Utils\\FileSystem' => $vendorDir . '/nette/utils/src/Utils/FileSystem.php',
    'Nette\\Utils\\Finder' => $vendorDir . '/nette/utils/src/Utils/Finder.php',
    'Nette\\Utils\\Floats' => $vendorDir . '/nette/utils/src/Utils/Floats.php',
    'Nette\\Utils\\Helpers' => $vendorDir . '/nette/utils/src/Utils/Helpers.php',
    'Nette\\Utils\\Html' => $vendorDir . '/nette/utils/src/Utils/Html.php',
    'Nette\\Utils\\IHtmlString' => $vendorDir . '/nette/utils/src/compatibility.php',
    'Nette\\Utils\\Image' => $vendorDir . '/nette/utils/src/Utils/Image.php',
    'Nette\\Utils\\ImageColor' => $vendorDir . '/nette/utils/src/Utils/ImageColor.php',
    'Nette\\Utils\\ImageException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\ImageType' => $vendorDir . '/nette/utils/src/Utils/ImageType.php',
    'Nette\\Utils\\Iterables' => $vendorDir . '/nette/utils/src/Utils/Iterables.php',
    'Nette\\Utils\\Json' => $vendorDir . '/nette/utils/src/Utils/Json.php',
    'Nette\\Utils\\JsonException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\ObjectHelpers' => $vendorDir . '/nette/utils/src/Utils/ObjectHelpers.php',
    'Nette\\Utils\\Paginator' => $vendorDir . '/nette/utils/src/Utils/Paginator.php',
    'Nette\\Utils\\Random' => $vendorDir . '/nette/utils/src/Utils/Random.php',
    'Nette\\Utils\\Reflection' => $vendorDir . '/nette/utils/src/Utils/Reflection.php',
    'Nette\\Utils\\ReflectionMethod' => $vendorDir . '/nette/utils/src/Utils/ReflectionMethod.php',
    'Nette\\Utils\\RegexpException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Strings' => $vendorDir . '/nette/utils/src/Utils/Strings.php',
    'Nette\\Utils\\Type' => $vendorDir . '/nette/utils/src/Utils/Type.php',
    'Nette\\Utils\\UnknownImageFileException' => $vendorDir . '/nette/utils/src/Utils/exceptions.php',
    'Nette\\Utils\\Validators' => $vendorDir . '/nette/utils/src/Utils/Validators.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Pusher' => $vendorDir . '/pusher/pusher-php-server/lib/Pusher.php',
    'PusherException' => $vendorDir . '/pusher/pusher-php-server/lib/Pusher.php',
    'PusherInstance' => $vendorDir . '/pusher/pusher-php-server/lib/Pusher.php',
    'ReturnTypeWillChange' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    'lessc' => $vendorDir . '/wikimedia/less.php/lessc.inc.php',
);

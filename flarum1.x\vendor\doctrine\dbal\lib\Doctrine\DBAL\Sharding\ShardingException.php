<?php

namespace Doctrine\DBAL\Sharding;

use Doctrine\DBAL\Exception;

/**
 * Sharding related Exceptions
 *
 * @deprecated
 *
 * @psalm-immutable
 */
class ShardingException extends Exception
{
    /**
     * @return ShardingException
     */
    public static function notImplemented()
    {
        return new self('This functionality is not implemented with this sharding provider.', **********);
    }

    /**
     * @return ShardingException
     */
    public static function missingDefaultFederationName()
    {
        return new self('SQLAzure requires a federation name to be set during sharding configuration.', **********);
    }

    /**
     * @return ShardingException
     */
    public static function missingDefaultDistributionKey()
    {
        return new self('SQLAzure requires a distribution key to be set during sharding configuration.', **********);
    }

    /**
     * @return ShardingException
     */
    public static function activeTransaction()
    {
        return new self('Cannot switch shard during an active transaction.', **********);
    }

    /**
     * @return ShardingException
     */
    public static function noShardDistributionValue()
    {
        return new self('You have to specify a string or integer as shard distribution value.', **********);
    }

    /**
     * @return ShardingException
     */
    public static function missingDistributionType()
    {
        return new self("You have to specify a sharding distribution type such as 'integer', 'string', 'guid'.");
    }
}

<?xml version="1.0"?>
<ruleset
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
    name="CodeReviewsIo"
>
    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="80"/>
    <arg name="colors"/>

    <!-- Ignore warnings and show progress of the run -->
    <arg value="np"/>

    <file>./src</file>
    <file>./tests</file>

    <rule ref="Doctrine">
        <exclude name="PSR1.Methods.CamelCapsMethodName"/>
        <exclude name="SlevomatCodingStandard.Classes.DisallowLateStaticBindingForConstants.DisallowedLateStaticBindingForConstant"/>
        <exclude name="SlevomatCodingStandard.PHP.RequireExplicitAssertion"/>
        <exclude name="SlevomatCodingStandard.TypeHints.PropertyTypeHint.MissingNativeTypeHint"/>
        <exclude name="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator.RequiredNullCoalesceEqualOperator"/>
    </rule>
    <rule ref="PSR2">
        <exclude name="Generic.Files.LineLength"/>
    </rule>
</ruleset>
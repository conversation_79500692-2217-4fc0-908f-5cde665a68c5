/*! For license information please see forum.js.LICENSE.txt */
(()=>{var t={8492:()=>{},1859:()=>{Array.prototype.flat||(Array.prototype.flat=function t(e){return(null!=e?e:1)>0?Array.prototype.reduce.call(this,(function(n,r){return n.concat(Array.isArray(r)?t.call(r,e-1):r)}),[]):[].concat(this)})},9043:()=>{!function(t){"use strict";var e=function e(n,r){this.options=t.extend({},e.DEFAULTS,r);var i=this.options.target===e.DEFAULTS.target?t(this.options.target):t(document).find(this.options.target);this.$target=i.on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(n),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};function n(n){return this.each((function(){var r=t(this),i=r.data("bs.affix"),o="object"==typeof n&&n;i||r.data("bs.affix",i=new e(this,o)),"string"==typeof n&&i[n]()}))}e.VERSION="3.4.1",e.RESET="affix affix-top affix-bottom",e.DEFAULTS={offset:0,target:window},e.prototype.getState=function(t,e,n,r){var i=this.$target.scrollTop(),o=this.$element.offset(),s=this.$target.height();if(null!=n&&"top"==this.affixed)return i<n&&"top";if("bottom"==this.affixed)return null!=n?!(i+this.unpin<=o.top)&&"bottom":!(i+s<=t-r)&&"bottom";var a=null==this.affixed,u=a?i:o.top;return null!=n&&i<=n?"top":null!=r&&u+(a?s:e)>=t-r&&"bottom"},e.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(e.RESET).addClass("affix");var t=this.$target.scrollTop(),n=this.$element.offset();return this.pinnedOffset=n.top-t},e.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},e.prototype.checkPosition=function(){if(this.$element.is(":visible")){var n=this.$element.height(),r=this.options.offset,i=r.top,o=r.bottom,s=Math.max(t(document).height(),t(document.body).height());"object"!=typeof r&&(o=i=r),"function"==typeof i&&(i=r.top(this.$element)),"function"==typeof o&&(o=r.bottom(this.$element));var a=this.getState(s,n,i,o);if(this.affixed!=a){null!=this.unpin&&this.$element.css("top","");var u="affix"+(a?"-"+a:""),l=t.Event(u+".bs.affix");if(this.$element.trigger(l),l.isDefaultPrevented())return;this.affixed=a,this.unpin="bottom"==a?this.getPinnedOffset():null,this.$element.removeClass(e.RESET).addClass(u).trigger(u.replace("affix","affixed")+".bs.affix")}"bottom"==a&&this.$element.offset({top:s-n-o})}};var r=t.fn.affix;t.fn.affix=n,t.fn.affix.Constructor=e,t.fn.affix.noConflict=function(){return t.fn.affix=r,this},t(window).on("load",(function(){t('[data-spy="affix"]').each((function(){var e=t(this),r=e.data();r.offset=r.offset||{},null!=r.offsetBottom&&(r.offset.bottom=r.offsetBottom),null!=r.offsetTop&&(r.offset.top=r.offsetTop),n.call(e,r)}))}))}(jQuery)},6199:()=>{!function(t){"use strict";var e='[data-toggle="dropdown"]',n=function(e){t(e).on("click.bs.dropdown",this.toggle)};function r(e){var n=e.attr("data-target");n||(n=(n=e.attr("href"))&&/#[A-Za-z]/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,""));var r="#"!==n?t(document).find(n):null;return r&&r.length?r:e.parent()}function i(n){n&&3===n.which||(t(".dropdown-backdrop").remove(),t(e).each((function(){var e=t(this),i=r(e),o={relatedTarget:this};i.hasClass("open")&&(n&&"click"==n.type&&/input|textarea/i.test(n.target.tagName)&&t.contains(i[0],n.target)||(i.trigger(n=t.Event("hide.bs.dropdown",o)),n.isDefaultPrevented()||(e.attr("aria-expanded","false"),i.removeClass("open").trigger(t.Event("hidden.bs.dropdown",o)))))})))}n.VERSION="3.4.1",n.prototype.toggle=function(e){var n=t(this);if(!n.is(".disabled, :disabled")){var o=r(n),s=o.hasClass("open");if(i(),!s){"ontouchstart"in document.documentElement&&!o.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",i);var a={relatedTarget:this};if(o.trigger(e=t.Event("show.bs.dropdown",a)),e.isDefaultPrevented())return;n.trigger("focus").attr("aria-expanded","true"),o.toggleClass("open").trigger(t.Event("shown.bs.dropdown",a))}return!1}},n.prototype.keydown=function(n){if(/(38|40|27|32)/.test(n.which)&&!/input|textarea/i.test(n.target.tagName)){var i=t(this);if(n.preventDefault(),n.stopPropagation(),!i.is(".disabled, :disabled")){var o=r(i),s=o.hasClass("open");if(!s&&27!=n.which||s&&27==n.which)return 27==n.which&&o.find(e).trigger("focus"),i.trigger("click");var a=o.find(".dropdown-menu li:not(.disabled):visible a");if(a.length){var u=a.index(n.target);38==n.which&&u>0&&u--,40==n.which&&u<a.length-1&&u++,~u||(u=0),a.eq(u).trigger("focus")}}}};var o=t.fn.dropdown;t.fn.dropdown=function(e){return this.each((function(){var r=t(this),i=r.data("bs.dropdown");i||r.data("bs.dropdown",i=new n(this)),"string"==typeof e&&i[e].call(r)}))},t.fn.dropdown.Constructor=n,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=o,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",(function(t){t.stopPropagation()})).on("click.bs.dropdown.data-api",e,n.prototype.toggle).on("keydown.bs.dropdown.data-api",e,n.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",n.prototype.keydown)}(jQuery)},7865:()=>{!function(t){"use strict";var e=["sanitize","whiteList","sanitizeFn"],n=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],r=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,i=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function o(e,o){var s=e.nodeName.toLowerCase();if(-1!==t.inArray(s,o))return-1===t.inArray(s,n)||Boolean(e.nodeValue.match(r)||e.nodeValue.match(i));for(var a=t(o).filter((function(t,e){return e instanceof RegExp})),u=0,l=a.length;u<l;u++)if(s.match(a[u]))return!0;return!1}function s(e,n,r){if(0===e.length)return e;if(r&&"function"==typeof r)return r(e);if(!document.implementation||!document.implementation.createHTMLDocument)return e;var i=document.implementation.createHTMLDocument("sanitization");i.body.innerHTML=e;for(var s=t.map(n,(function(t,e){return e})),a=t(i.body).find("*"),u=0,l=a.length;u<l;u++){var c=a[u],d=c.nodeName.toLowerCase();if(-1!==t.inArray(d,s))for(var h=t.map(c.attributes,(function(t){return t})),f=[].concat(n["*"]||[],n[d]||[]),p=0,m=h.length;p<m;p++)o(h[p],f)||c.removeAttribute(h[p].nodeName);else c.parentNode.removeChild(c)}return i.body.innerHTML}var a=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};a.VERSION="3.4.1",a.TRANSITION_DURATION=150,a.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]}},a.prototype.init=function(e,n,r){if(this.enabled=!0,this.type=e,this.$element=t(n),this.options=this.getOptions(r),this.$viewport=this.options.viewport&&t(document).find(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var i=this.options.trigger.split(" "),o=i.length;o--;){var s=i[o];if("click"==s)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=s){var a="hover"==s?"mouseenter":"focusin",u="hover"==s?"mouseleave":"focusout";this.$element.on(a+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(u+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},a.prototype.getDefaults=function(){return a.DEFAULTS},a.prototype.getOptions=function(n){var r=this.$element.data();for(var i in r)r.hasOwnProperty(i)&&-1!==t.inArray(i,e)&&delete r[i];return(n=t.extend({},this.getDefaults(),r,n)).delay&&"number"==typeof n.delay&&(n.delay={show:n.delay,hide:n.delay}),n.sanitize&&(n.template=s(n.template,n.whiteList,n.sanitizeFn)),n},a.prototype.getDelegateOptions=function(){var e={},n=this.getDefaults();return this._options&&t.each(this._options,(function(t,r){n[t]!=r&&(e[t]=r)})),e},a.prototype.enter=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),e instanceof t.Event&&(n.inState["focusin"==e.type?"focus":"hover"]=!0),n.tip().hasClass("in")||"in"==n.hoverState)n.hoverState="in";else{if(clearTimeout(n.timeout),n.hoverState="in",!n.options.delay||!n.options.delay.show)return n.show();n.timeout=setTimeout((function(){"in"==n.hoverState&&n.show()}),n.options.delay.show)}},a.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},a.prototype.leave=function(e){var n=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(n||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n)),e instanceof t.Event&&(n.inState["focusout"==e.type?"focus":"hover"]=!1),!n.isInStateTrue()){if(clearTimeout(n.timeout),n.hoverState="out",!n.options.delay||!n.options.delay.hide)return n.hide();n.timeout=setTimeout((function(){"out"==n.hoverState&&n.hide()}),n.options.delay.hide)}},a.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var n=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!n)return;var r=this,i=this.tip(),o=this.getUID(this.type);this.setContent(),i.attr("id",o),this.$element.attr("aria-describedby",o),this.options.animation&&i.addClass("fade");var s="function"==typeof this.options.placement?this.options.placement.call(this,i[0],this.$element[0]):this.options.placement,u=/\s?auto?\s?/i,l=u.test(s);l&&(s=s.replace(u,"")||"top"),i.detach().css({top:0,left:0,display:"block"}).addClass(s).data("bs."+this.type,this),this.options.container?i.appendTo(t(document).find(this.options.container)):i.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var c=this.getPosition(),d=i[0].offsetWidth,h=i[0].offsetHeight;if(l){var f=s,p=this.getPosition(this.$viewport);s="bottom"==s&&c.bottom+h>p.bottom?"top":"top"==s&&c.top-h<p.top?"bottom":"right"==s&&c.right+d>p.width?"left":"left"==s&&c.left-d<p.left?"right":s,i.removeClass(f).addClass(s)}var m=this.getCalculatedOffset(s,c,d,h);this.applyPlacement(m,s);var v=function(){var t=r.hoverState;r.$element.trigger("shown.bs."+r.type),r.hoverState=null,"out"==t&&r.leave(r)};t.support.transition&&this.$tip.hasClass("fade")?i.one("bsTransitionEnd",v).emulateTransitionEnd(a.TRANSITION_DURATION):v()}},a.prototype.applyPlacement=function(e,n){var r=this.tip(),i=r[0].offsetWidth,o=r[0].offsetHeight,s=parseInt(r.css("margin-top"),10),a=parseInt(r.css("margin-left"),10);isNaN(s)&&(s=0),isNaN(a)&&(a=0),e.top+=s,e.left+=a,t.offset.setOffset(r[0],t.extend({using:function(t){r.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),r.addClass("in");var u=r[0].offsetWidth,l=r[0].offsetHeight;"top"==n&&l!=o&&(e.top=e.top+o-l);var c=this.getViewportAdjustedDelta(n,e,u,l);c.left?e.left+=c.left:e.top+=c.top;var d=/top|bottom/.test(n),h=d?2*c.left-i+u:2*c.top-o+l,f=d?"offsetWidth":"offsetHeight";r.offset(e),this.replaceArrow(h,r[0][f],d)},a.prototype.replaceArrow=function(t,e,n){this.arrow().css(n?"left":"top",50*(1-t/e)+"%").css(n?"top":"left","")},a.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();this.options.html?(this.options.sanitize&&(e=s(e,this.options.whiteList,this.options.sanitizeFn)),t.find(".tooltip-inner").html(e)):t.find(".tooltip-inner").text(e),t.removeClass("fade in top bottom left right")},a.prototype.hide=function(e){var n=this,r=t(this.$tip),i=t.Event("hide.bs."+this.type);function o(){"in"!=n.hoverState&&r.detach(),n.$element&&n.$element.removeAttr("aria-describedby").trigger("hidden.bs."+n.type),e&&e()}if(this.$element.trigger(i),!i.isDefaultPrevented())return r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",o).emulateTransitionEnd(a.TRANSITION_DURATION):o(),this.hoverState=null,this},a.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},a.prototype.hasContent=function(){return this.getTitle()},a.prototype.getPosition=function(e){var n=(e=e||this.$element)[0],r="BODY"==n.tagName,i=n.getBoundingClientRect();null==i.width&&(i=t.extend({},i,{width:i.right-i.left,height:i.bottom-i.top}));var o=window.SVGElement&&n instanceof window.SVGElement,s=r?{top:0,left:0}:o?null:e.offset(),a={scroll:r?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},u=r?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},i,a,u,s)},a.prototype.getCalculatedOffset=function(t,e,n,r){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-n/2}:"top"==t?{top:e.top-r,left:e.left+e.width/2-n/2}:"left"==t?{top:e.top+e.height/2-r/2,left:e.left-n}:{top:e.top+e.height/2-r/2,left:e.left+e.width}},a.prototype.getViewportAdjustedDelta=function(t,e,n,r){var i={top:0,left:0};if(!this.$viewport)return i;var o=this.options.viewport&&this.options.viewport.padding||0,s=this.getPosition(this.$viewport);if(/right|left/.test(t)){var a=e.top-o-s.scroll,u=e.top+o-s.scroll+r;a<s.top?i.top=s.top-a:u>s.top+s.height&&(i.top=s.top+s.height-u)}else{var l=e.left-o,c=e.left+o+n;l<s.left?i.left=s.left-l:c>s.right&&(i.left=s.left+s.width-c)}return i},a.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},a.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},a.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},a.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},a.prototype.enable=function(){this.enabled=!0},a.prototype.disable=function(){this.enabled=!1},a.prototype.toggleEnabled=function(){this.enabled=!this.enabled},a.prototype.toggle=function(e){var n=this;e&&((n=t(e.currentTarget).data("bs."+this.type))||(n=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,n))),e?(n.inState.click=!n.inState.click,n.isInStateTrue()?n.enter(n):n.leave(n)):n.tip().hasClass("in")?n.leave(n):n.enter(n)},a.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide((function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null}))},a.prototype.sanitizeHtml=function(t){return s(t,this.options.whiteList,this.options.sanitizeFn)};var u=t.fn.tooltip;t.fn.tooltip=function(e){return this.each((function(){var n=t(this),r=n.data("bs.tooltip"),i="object"==typeof e&&e;!r&&/destroy|hide/.test(e)||(r||n.data("bs.tooltip",r=new a(this,i)),"string"==typeof e&&r[e]())}))},t.fn.tooltip.Constructor=a,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=u,this}}(jQuery)},6935:()=>{!function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var n=!1,r=this;return t(this).one("bsTransitionEnd",(function(){n=!0})),setTimeout((function(){n||t(r).trigger(t.support.transition.end)}),e),this},t((function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var n in e)if(void 0!==t.style[n])return{end:e[n]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})}))}(jQuery)},2898:t=>{var e=function(t){this.canvas=document.createElement("canvas"),this.context=this.canvas.getContext("2d"),document.body.appendChild(this.canvas),this.width=this.canvas.width=t.width,this.height=this.canvas.height=t.height,this.context.drawImage(t,0,0,this.width,this.height)};e.prototype.clear=function(){this.context.clearRect(0,0,this.width,this.height)},e.prototype.update=function(t){this.context.putImageData(t,0,0)},e.prototype.getPixelCount=function(){return this.width*this.height},e.prototype.getImageData=function(){return this.context.getImageData(0,0,this.width,this.height)},e.prototype.removeCanvas=function(){this.canvas.parentNode.removeChild(this.canvas)};var n=function(){};if(n.prototype.getColor=function(t,e){return this.getPalette(t,5,e)[0]},n.prototype.getPalette=function(t,n,r){void 0===n&&(n=10),(void 0===r||1>r)&&(r=10);for(var o,s,a,u,l=new e(t),c=l.getImageData().data,d=l.getPixelCount(),h=[],f=0;d>f;f+=r)s=c[0+(o=4*f)],a=c[o+1],u=c[o+2],c[o+3]>=125&&(s>250&&a>250&&u>250||h.push([s,a,u]));var p=i.quantize(h,n),m=p?p.palette():null;return l.removeCanvas(),m},!r)var r={map:function(t,e){var n={};return e?t.map((function(t,r){return n.index=r,e.call(n,t)})):t.slice()},naturalOrder:function(t,e){return e>t?-1:t>e?1:0},sum:function(t,e){var n={};return t.reduce(e?function(t,r,i){return n.index=i,t+e.call(n,r)}:function(t,e){return t+e},0)},max:function(t,e){return Math.max.apply(null,e?r.map(t,e):t)}};var i=function(){function t(t,e,n){return(t<<2*u)+(e<<u)+n}function e(t){function e(){n.sort(t),r=!0}var n=[],r=!1;return{push:function(t){n.push(t),r=!1},peek:function(t){return r||e(),void 0===t&&(t=n.length-1),n[t]},pop:function(){return r||e(),n.pop()},size:function(){return n.length},map:function(t){return n.map(t)},debug:function(){return r||e(),n}}}function n(t,e,n,r,i,o,s){var a=this;a.r1=t,a.r2=e,a.g1=n,a.g2=r,a.b1=i,a.b2=o,a.histo=s}function i(){this.vboxes=new e((function(t,e){return r.naturalOrder(t.vbox.count()*t.vbox.volume(),e.vbox.count()*e.vbox.volume())}))}function o(e){var n,r,i,o,s=new Array(1<<3*u);return e.forEach((function(e){r=e[0]>>l,i=e[1]>>l,o=e[2]>>l,n=t(r,i,o),s[n]=(s[n]||0)+1})),s}function s(t,e){var r,i,o,s=1e6,a=0,u=1e6,c=0,d=1e6,h=0;return t.forEach((function(t){r=t[0]>>l,i=t[1]>>l,o=t[2]>>l,s>r?s=r:r>a&&(a=r),u>i?u=i:i>c&&(c=i),d>o?d=o:o>h&&(h=o)})),new n(s,a,u,c,d,h,e)}function a(e,n){if(n.count()){var i=n.r2-n.r1+1,o=n.g2-n.g1+1,s=n.b2-n.b1+1,a=r.max([i,o,s]);if(1==n.count())return[n.copy()];var u,l,c,d,h=0,f=[],p=[];if(a==i)for(u=n.r1;u<=n.r2;u++){for(d=0,l=n.g1;l<=n.g2;l++)for(c=n.b1;c<=n.b2;c++)d+=e[t(u,l,c)]||0;h+=d,f[u]=h}else if(a==o)for(u=n.g1;u<=n.g2;u++){for(d=0,l=n.r1;l<=n.r2;l++)for(c=n.b1;c<=n.b2;c++)d+=e[t(l,u,c)]||0;h+=d,f[u]=h}else for(u=n.b1;u<=n.b2;u++){for(d=0,l=n.r1;l<=n.r2;l++)for(c=n.g1;c<=n.g2;c++)d+=e[t(l,c,u)]||0;h+=d,f[u]=h}return f.forEach((function(t,e){p[e]=h-t})),function(t){var e,r,i,o,s,a=t+"1",l=t+"2",c=0;for(u=n[a];u<=n[l];u++)if(f[u]>h/2){for(i=n.copy(),o=n.copy(),e=u-n[a],s=(r=n[l]-u)>=e?Math.min(n[l]-1,~~(u+r/2)):Math.max(n[a],~~(u-1-e/2));!f[s];)s++;for(c=p[s];!c&&f[s-1];)c=p[--s];return i[l]=s,o[a]=i[l]+1,[i,o]}}(a==i?"r":a==o?"g":"b")}}var u=5,l=8-u;return n.prototype={volume:function(t){var e=this;return(!e._volume||t)&&(e._volume=(e.r2-e.r1+1)*(e.g2-e.g1+1)*(e.b2-e.b1+1)),e._volume},count:function(e){var n=this,r=n.histo;if(!n._count_set||e){var i,o,s,a=0;for(i=n.r1;i<=n.r2;i++)for(o=n.g1;o<=n.g2;o++)for(s=n.b1;s<=n.b2;s++)index=t(i,o,s),a+=r[index]||0;n._count=a,n._count_set=!0}return n._count},copy:function(){var t=this;return new n(t.r1,t.r2,t.g1,t.g2,t.b1,t.b2,t.histo)},avg:function(e){var n=this,r=n.histo;if(!n._avg||e){var i,o,s,a,l=0,c=1<<8-u,d=0,h=0,f=0;for(o=n.r1;o<=n.r2;o++)for(s=n.g1;s<=n.g2;s++)for(a=n.b1;a<=n.b2;a++)l+=i=r[t(o,s,a)]||0,d+=i*(o+.5)*c,h+=i*(s+.5)*c,f+=i*(a+.5)*c;n._avg=l?[~~(d/l),~~(h/l),~~(f/l)]:[~~(c*(n.r1+n.r2+1)/2),~~(c*(n.g1+n.g2+1)/2),~~(c*(n.b1+n.b2+1)/2)]}return n._avg},contains:function(t){var e=this,n=t[0]>>l;return gval=t[1]>>l,bval=t[2]>>l,n>=e.r1&&n<=e.r2&&gval>=e.g1&&gval<=e.g2&&bval>=e.b1&&bval<=e.b2}},i.prototype={push:function(t){this.vboxes.push({vbox:t,color:t.avg()})},palette:function(){return this.vboxes.map((function(t){return t.color}))},size:function(){return this.vboxes.size()},map:function(t){for(var e=this.vboxes,n=0;n<e.size();n++)if(e.peek(n).vbox.contains(t))return e.peek(n).color;return this.nearest(t)},nearest:function(t){for(var e,n,r,i=this.vboxes,o=0;o<i.size();o++)(e>(n=Math.sqrt(Math.pow(t[0]-i.peek(o).color[0],2)+Math.pow(t[1]-i.peek(o).color[1],2)+Math.pow(t[2]-i.peek(o).color[2],2)))||void 0===e)&&(e=n,r=i.peek(o).color);return r},forcebw:function(){var t=this.vboxes;t.sort((function(t,e){return r.naturalOrder(r.sum(t.color),r.sum(e.color))}));var e=t[0].color;e[0]<5&&e[1]<5&&e[2]<5&&(t[0].color=[0,0,0]);var n=t.length-1,i=t[n].color;i[0]>251&&i[1]>251&&i[2]>251&&(t[n].color=[255,255,255])}},{quantize:function(t,n){function u(t,e){for(var n,r=1,i=0;1e3>i;)if((n=t.pop()).count()){var o=a(l,n),s=o[0],u=o[1];if(!s)return;if(t.push(s),u&&(t.push(u),r++),r>=e)return;if(i++>1e3)return}else t.push(n),i++}if(!t.length||2>n||n>256)return!1;var l=o(t);l.forEach((function(){}));var c=s(t,l),d=new e((function(t,e){return r.naturalOrder(t.count(),e.count())}));d.push(c),u(d,.75*n);for(var h=new e((function(t,e){return r.naturalOrder(t.count()*t.volume(),e.count()*e.volume())}));d.size();)h.push(d.pop());u(h,n-h.size());for(var f=new i;h.size();)f.push(h.pop());return f}}}();t.exports=n},4757:function(t){t.exports=function(){"use strict";var t=6e4,e=36e5,n="millisecond",r="second",i="minute",o="hour",s="day",a="week",u="month",l="quarter",c="year",d="date",h="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,p=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var e=["th","st","nd","rd"],n=t%100;return"["+t+(e[(n-20)%10]||e[n]||e[0])+"]"}},v=function(t,e,n){var r=String(t);return!r||r.length>=e?t:""+Array(e+1-r.length).join(n)+t},g={s:v,z:function(t){var e=-t.utcOffset(),n=Math.abs(e),r=Math.floor(n/60),i=n%60;return(e<=0?"+":"-")+v(r,2,"0")+":"+v(i,2,"0")},m:function t(e,n){if(e.date()<n.date())return-t(n,e);var r=12*(n.year()-e.year())+(n.month()-e.month()),i=e.clone().add(r,u),o=n-i<0,s=e.clone().add(r+(o?-1:1),u);return+(-(r+(n-i)/(o?i-s:s-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(t){return{M:u,y:c,w:a,d:s,D:d,h:o,m:i,s:r,ms:n,Q:l}[t]||String(t||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},y="en",b={};b[y]=m;var w=function(t){return t instanceof A},D=function t(e,n,r){var i;if(!e)return y;if("string"==typeof e){var o=e.toLowerCase();b[o]&&(i=o),n&&(b[o]=n,i=o);var s=e.split("-");if(!i&&s.length>1)return t(s[0])}else{var a=e.name;b[a]=e,i=a}return!r&&i&&(y=i),i||!r&&y},x=function(t,e){if(w(t))return t.clone();var n="object"==typeof e?e:{};return n.date=t,n.args=arguments,new A(n)},C=g;C.l=D,C.i=w,C.w=function(t,e){return x(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var A=function(){function m(t){this.$L=D(t.locale,null,!0),this.parse(t)}var v=m.prototype;return v.parse=function(t){this.$d=function(t){var e=t.date,n=t.utc;if(null===e)return new Date(NaN);if(C.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var r=e.match(f);if(r){var i=r[2]-1||0,o=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,o)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},v.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},v.$utils=function(){return C},v.isValid=function(){return!(this.$d.toString()===h)},v.isSame=function(t,e){var n=x(t);return this.startOf(e)<=n&&n<=this.endOf(e)},v.isAfter=function(t,e){return x(t)<this.startOf(e)},v.isBefore=function(t,e){return this.endOf(e)<x(t)},v.$g=function(t,e,n){return C.u(t)?this[e]:this.set(n,t)},v.unix=function(){return Math.floor(this.valueOf()/1e3)},v.valueOf=function(){return this.$d.getTime()},v.startOf=function(t,e){var n=this,l=!!C.u(e)||e,h=C.p(t),f=function(t,e){var r=C.w(n.$u?Date.UTC(n.$y,e,t):new Date(n.$y,e,t),n);return l?r:r.endOf(s)},p=function(t,e){return C.w(n.toDate()[t].apply(n.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(e)),n)},m=this.$W,v=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(h){case c:return l?f(1,0):f(31,11);case u:return l?f(1,v):f(0,v+1);case a:var b=this.$locale().weekStart||0,w=(m<b?m+7:m)-b;return f(l?g-w:g+(6-w),v);case s:case d:return p(y+"Hours",0);case o:return p(y+"Minutes",1);case i:return p(y+"Seconds",2);case r:return p(y+"Milliseconds",3);default:return this.clone()}},v.endOf=function(t){return this.startOf(t,!1)},v.$set=function(t,e){var a,l=C.p(t),h="set"+(this.$u?"UTC":""),f=(a={},a[s]=h+"Date",a[d]=h+"Date",a[u]=h+"Month",a[c]=h+"FullYear",a[o]=h+"Hours",a[i]=h+"Minutes",a[r]=h+"Seconds",a[n]=h+"Milliseconds",a)[l],p=l===s?this.$D+(e-this.$W):e;if(l===u||l===c){var m=this.clone().set(d,1);m.$d[f](p),m.init(),this.$d=m.set(d,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},v.set=function(t,e){return this.clone().$set(t,e)},v.get=function(t){return this[C.p(t)]()},v.add=function(n,l){var d,h=this;n=Number(n);var f=C.p(l),p=function(t){var e=x(h);return C.w(e.date(e.date()+Math.round(t*n)),h)};if(f===u)return this.set(u,this.$M+n);if(f===c)return this.set(c,this.$y+n);if(f===s)return p(1);if(f===a)return p(7);var m=(d={},d[i]=t,d[o]=e,d[r]=1e3,d)[f]||1,v=this.$d.getTime()+n*m;return C.w(v,this)},v.subtract=function(t,e){return this.add(-1*t,e)},v.format=function(t){var e=this,n=this.$locale();if(!this.isValid())return n.invalidDate||h;var r=t||"YYYY-MM-DDTHH:mm:ssZ",i=C.z(this),o=this.$H,s=this.$m,a=this.$M,u=n.weekdays,l=n.months,c=function(t,n,i,o){return t&&(t[n]||t(e,r))||i[n].slice(0,o)},d=function(t){return C.s(o%12||12,t,"0")},f=n.meridiem||function(t,e,n){var r=t<12?"AM":"PM";return n?r.toLowerCase():r},m={YY:String(this.$y).slice(-2),YYYY:this.$y,M:a+1,MM:C.s(a+1,2,"0"),MMM:c(n.monthsShort,a,l,3),MMMM:c(l,a),D:this.$D,DD:C.s(this.$D,2,"0"),d:String(this.$W),dd:c(n.weekdaysMin,this.$W,u,2),ddd:c(n.weekdaysShort,this.$W,u,3),dddd:u[this.$W],H:String(o),HH:C.s(o,2,"0"),h:d(1),hh:d(2),a:f(o,s,!0),A:f(o,s,!1),m:String(s),mm:C.s(s,2,"0"),s:String(this.$s),ss:C.s(this.$s,2,"0"),SSS:C.s(this.$ms,3,"0"),Z:i};return r.replace(p,(function(t,e){return e||m[t]||i.replace(":","")}))},v.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},v.diff=function(n,d,h){var f,p=C.p(d),m=x(n),v=(m.utcOffset()-this.utcOffset())*t,g=this-m,y=C.m(this,m);return y=(f={},f[c]=y/12,f[u]=y,f[l]=y/3,f[a]=(g-v)/6048e5,f[s]=(g-v)/864e5,f[o]=g/e,f[i]=g/t,f[r]=g/1e3,f)[p]||g,h?y:C.a(y)},v.daysInMonth=function(){return this.endOf(u).$D},v.$locale=function(){return b[this.$L]},v.locale=function(t,e){if(!t)return this.$L;var n=this.clone(),r=D(t,e,!0);return r&&(n.$L=r),n},v.clone=function(){return C.w(this.$d,this)},v.toDate=function(){return new Date(this.valueOf())},v.toJSON=function(){return this.isValid()?this.toISOString():null},v.toISOString=function(){return this.$d.toISOString()},v.toString=function(){return this.$d.toUTCString()},m}(),E=A.prototype;return x.prototype=E,[["$ms",n],["$s",r],["$m",i],["$H",o],["$W",s],["$M",u],["$y",c],["$D",d]].forEach((function(t){E[t[1]]=function(e){return this.$g(e,t[0],t[1])}})),x.extend=function(t,e){return t.$i||(t(e,A,x),t.$i=!0),x},x.locale=D,x.isDayjs=w,x.unix=function(t){return x(1e3*t)},x.en=b[y],x.Ls=b,x.p={},x}()},6982:function(t){t.exports=function(){"use strict";var t={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(e,n,r){var i=n.prototype,o=i.format;r.en.formats=t,i.format=function(e){void 0===e&&(e="YYYY-MM-DDTHH:mm:ssZ");var n=this.$locale().formats,r=function(e,n){return e.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(e,r,i){var o=i&&i.toUpperCase();return r||n[i]||t[i]||n[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(t,e,n){return e||n.slice(1)}))}))}(e,void 0===n?{}:n);return o.call(this,r)}}}()},5635:function(t){t.exports=function(){"use strict";return function(t,e,n){t=t||{};var r=e.prototype,i={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function o(t,e,n,i){return r.fromToBase(t,e,n,i)}n.en.relativeTime=i,r.fromToBase=function(e,r,o,s,a){for(var u,l,c,d=o.$locale().relativeTime||i,h=t.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],f=h.length,p=0;p<f;p+=1){var m=h[p];m.d&&(u=s?n(e).diff(o,m.d,!0):o.diff(e,m.d,!0));var v=(t.rounding||Math.round)(Math.abs(u));if(c=u>0,v<=m.r||!m.r){v<=1&&p>0&&(m=h[p-1]);var g=d[m.l];a&&(v=a(""+v)),l="string"==typeof g?g.replace("%d",v):g(v,r,m.l,c);break}}if(r)return l;var y=c?d.future:d.past;return"function"==typeof y?y(l):y.replace("%s",l)},r.to=function(t,e){return o(t,e,this,!0)},r.from=function(t,e){return o(t,e,this)};var s=function(t){return t.$u?n.utc():n()};r.toNow=function(t){return this.to(s(this),t)},r.fromNow=function(t){return this.from(s(this),t)}}}()},356:(t,e,n)=>{"use strict";t.exports=function(){if("object"==typeof globalThis)return globalThis;var t;try{t=this||new Function("return this")()}catch(t){if("object"==typeof window)return window;if("object"==typeof self)return self;if(void 0!==n.g)return n.g}return t}()},9411:function(){!function(t){function e(e){if("string"==typeof e.data&&(e.data={keys:e.data}),e.data&&e.data.keys&&"string"==typeof e.data.keys){var n=e.handler,r=e.data.keys.toLowerCase().split(" ");e.handler=function(e){if(this===e.target||!(t.hotkeys.options.filterInputAcceptingElements&&t.hotkeys.textInputTypes.test(e.target.nodeName)||t.hotkeys.options.filterContentEditable&&t(e.target).attr("contenteditable")||t.hotkeys.options.filterTextInputs&&t.inArray(e.target.type,t.hotkeys.textAcceptingInputTypes)>-1)){var i="keypress"!==e.type&&t.hotkeys.specialKeys[e.which],o=String.fromCharCode(e.which).toLowerCase(),s="",a={};t.each(["alt","ctrl","shift"],(function(t,n){e[n+"Key"]&&i!==n&&(s+=n+"+")})),e.metaKey&&!e.ctrlKey&&"meta"!==i&&(s+="meta+"),e.metaKey&&"meta"!==i&&s.indexOf("alt+ctrl+shift+")>-1&&(s=s.replace("alt+ctrl+shift+","hyper+")),i?a[s+i]=!0:(a[s+o]=!0,a[s+t.hotkeys.shiftNums[o]]=!0,"shift+"===s&&(a[t.hotkeys.shiftNums[o]]=!0));for(var u=0,l=r.length;u<l;u++)if(a[r[u]])return n.apply(this,arguments)}}}}t.hotkeys={version:"0.2.0",specialKeys:{8:"backspace",9:"tab",10:"return",13:"return",16:"shift",17:"ctrl",18:"alt",19:"pause",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"insert",46:"del",59:";",61:"=",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"f1",113:"f2",114:"f3",115:"f4",116:"f5",117:"f6",118:"f7",119:"f8",120:"f9",121:"f10",122:"f11",123:"f12",144:"numlock",145:"scroll",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},shiftNums:{"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+",";":": ","'":'"',",":"<",".":">","/":"?","\\":"|"},textAcceptingInputTypes:["text","password","number","email","url","range","date","month","week","time","datetime","datetime-local","search","color","tel"],textInputTypes:/textarea|input|select/i,options:{filterInputAcceptingElements:!0,filterTextInputs:!0,filterContentEditable:!0}},t.each(["keydown","keyup","keypress"],(function(){t.event.special[this]={add:e}}))}(jQuery||this.jQuery||window.jQuery)},9152:function(t,e){var n;!function(e,n){"use strict";"object"==typeof t.exports?t.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)}("undefined"!=typeof window?window:this,(function(r,i){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,u=o.flat?function(t){return o.flat.call(t)}:function(t){return o.concat.apply([],t)},l=o.push,c=o.indexOf,d={},h=d.toString,f=d.hasOwnProperty,p=f.toString,m=p.call(Object),v={},g=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},y=function(t){return null!=t&&t===t.window},b=r.document,w={type:!0,src:!0,nonce:!0,noModule:!0};function D(t,e,n){var r,i,o=(n=n||b).createElement("script");if(o.text=t,e)for(r in w)(i=e[r]||e.getAttribute&&e.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function x(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?d[h.call(t)]||"object":typeof t}var C="3.6.1",A=function t(e,n){return new t.fn.init(e,n)};function E(t){var e=!!t&&"length"in t&&t.length,n=x(t);return!g(t)&&!y(t)&&("array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t)}A.fn=A.prototype={jquery:C,constructor:A,length:0,toArray:function(){return a.call(this)},get:function(t){return null==t?a.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=A.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return A.each(this,t)},map:function(t){return this.pushStack(A.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(A.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(A.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:o.sort,splice:o.splice},A.extend=A.fn.extend=function(){var t,e,n,r,i,o,s=arguments[0]||{},a=1,u=arguments.length,l=!1;for("boolean"==typeof s&&(l=s,s=arguments[a]||{},a++),"object"==typeof s||g(s)||(s={}),a===u&&(s=this,a--);a<u;a++)if(null!=(t=arguments[a]))for(e in t)r=t[e],"__proto__"!==e&&s!==r&&(l&&r&&(A.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[e],o=i&&!Array.isArray(n)?[]:i||A.isPlainObject(n)?n:{},i=!1,s[e]=A.extend(l,o,r)):void 0!==r&&(s[e]=r));return s},A.extend({expando:"jQuery"+(C+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==h.call(t)||(e=s(t))&&("function"!=typeof(n=f.call(e,"constructor")&&e.constructor)||p.call(n)!==m))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){D(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,r=0;if(E(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},makeArray:function(t,e){var n=e||[];return null!=t&&(E(Object(t))?A.merge(n,"string"==typeof t?[t]:t):l.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:c.call(e,t,n)},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!==s&&r.push(t[i]);return r},map:function(t,e,n){var r,i,o=0,s=[];if(E(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&s.push(i);else for(o in t)null!=(i=e(t[o],o,n))&&s.push(i);return u(s)},guid:1,support:v}),"function"==typeof Symbol&&(A.fn[Symbol.iterator]=o[Symbol.iterator]),A.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){d["[object "+e+"]"]=e.toLowerCase()}));var N=function(t){var e,n,r,i,o,s,a,u,l,c,d,h,f,p,m,v,g,y,b,w="sizzle"+1*new Date,D=t.document,x=0,C=0,A=ut(),E=ut(),N=ut(),k=ut(),S=function(t,e){return t===e&&(d=!0),0},T={}.hasOwnProperty,F=[],_=F.pop,P=F.push,B=F.push,I=F.slice,O=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},L="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",$="(?:\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",j="\\[[\\x20\\t\\r\\n\\f]*("+$+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$+"))|)"+M+"*\\]",R=":("+$+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+j+")*)|.*)\\)|)",H=new RegExp(M+"+","g"),U=new RegExp("^[\\x20\\t\\r\\n\\f]+|((?:^|[^\\\\])(?:\\\\.)*)[\\x20\\t\\r\\n\\f]+$","g"),q=new RegExp("^[\\x20\\t\\r\\n\\f]*,[\\x20\\t\\r\\n\\f]*"),z=new RegExp("^[\\x20\\t\\r\\n\\f]*([>+~]|[\\x20\\t\\r\\n\\f])[\\x20\\t\\r\\n\\f]*"),V=new RegExp(M+"|>"),W=new RegExp(R),G=new RegExp("^"+$+"$"),Y={ID:new RegExp("^#("+$+")"),CLASS:new RegExp("^\\.("+$+")"),TAG:new RegExp("^("+$+"|[*])"),ATTR:new RegExp("^"+j),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\([\\x20\\t\\r\\n\\f]*(even|odd|(([+-]|)(\\d*)n|)[\\x20\\t\\r\\n\\f]*(?:([+-]|)[\\x20\\t\\r\\n\\f]*(\\d+)|))[\\x20\\t\\r\\n\\f]*\\)|)","i"),bool:new RegExp("^(?:"+L+")$","i"),needsContext:new RegExp("^[\\x20\\t\\r\\n\\f]*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\([\\x20\\t\\r\\n\\f]*((?:-\\d)?\\d*)[\\x20\\t\\r\\n\\f]*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,X=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,Q=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=new RegExp("\\\\[\\da-fA-F]{1,6}[\\x20\\t\\r\\n\\f]?|\\\\([^\\r\\n\\f])","g"),nt=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},rt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,it=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},ot=function(){h()},st=wt((function(t){return!0===t.disabled&&"fieldset"===t.nodeName.toLowerCase()}),{dir:"parentNode",next:"legend"});try{B.apply(F=I.call(D.childNodes),D.childNodes),F[D.childNodes.length].nodeType}catch(t){B={apply:F.length?function(t,e){P.apply(t,I.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function at(t,e,r,i){var o,a,l,c,d,p,g,y=e&&e.ownerDocument,D=e?e.nodeType:9;if(r=r||[],"string"!=typeof t||!t||1!==D&&9!==D&&11!==D)return r;if(!i&&(h(e),e=e||f,m)){if(11!==D&&(d=Z.exec(t)))if(o=d[1]){if(9===D){if(!(l=e.getElementById(o)))return r;if(l.id===o)return r.push(l),r}else if(y&&(l=y.getElementById(o))&&b(e,l)&&l.id===o)return r.push(l),r}else{if(d[2])return B.apply(r,e.getElementsByTagName(t)),r;if((o=d[3])&&n.getElementsByClassName&&e.getElementsByClassName)return B.apply(r,e.getElementsByClassName(o)),r}if(n.qsa&&!k[t+" "]&&(!v||!v.test(t))&&(1!==D||"object"!==e.nodeName.toLowerCase())){if(g=t,y=e,1===D&&(V.test(t)||z.test(t))){for((y=tt.test(t)&&gt(e.parentNode)||e)===e&&n.scope||((c=e.getAttribute("id"))?c=c.replace(rt,it):e.setAttribute("id",c=w)),a=(p=s(t)).length;a--;)p[a]=(c?"#"+c:":scope")+" "+bt(p[a]);g=p.join(",")}try{return B.apply(r,y.querySelectorAll(g)),r}catch(e){k(t,!0)}finally{c===w&&e.removeAttribute("id")}}}return u(t.replace(U,"$1"),e,r,i)}function ut(){var t=[];return function e(n,i){return t.push(n+" ")>r.cacheLength&&delete e[t.shift()],e[n+" "]=i}}function lt(t){return t[w]=!0,t}function ct(t){var e=f.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function dt(t,e){for(var n=t.split("|"),i=n.length;i--;)r.attrHandle[n[i]]=e}function ht(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function ft(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function pt(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function mt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&st(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function vt(t){return lt((function(e){return e=+e,lt((function(n,r){for(var i,o=t([],n.length,e),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))}))}))}function gt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in n=at.support={},o=at.isXML=function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!K.test(e||n&&n.nodeName||"HTML")},h=at.setDocument=function(t){var e,i,s=t?t.ownerDocument||t:D;return s!=f&&9===s.nodeType&&s.documentElement?(p=(f=s).documentElement,m=!o(f),D!=f&&(i=f.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",ot,!1):i.attachEvent&&i.attachEvent("onunload",ot)),n.scope=ct((function(t){return p.appendChild(t).appendChild(f.createElement("div")),void 0!==t.querySelectorAll&&!t.querySelectorAll(":scope fieldset div").length})),n.attributes=ct((function(t){return t.className="i",!t.getAttribute("className")})),n.getElementsByTagName=ct((function(t){return t.appendChild(f.createComment("")),!t.getElementsByTagName("*").length})),n.getElementsByClassName=Q.test(f.getElementsByClassName),n.getById=ct((function(t){return p.appendChild(t).id=w,!f.getElementsByName||!f.getElementsByName(w).length})),n.getById?(r.filter.ID=function(t){var e=t.replace(et,nt);return function(t){return t.getAttribute("id")===e}},r.find.ID=function(t,e){if(void 0!==e.getElementById&&m){var n=e.getElementById(t);return n?[n]:[]}}):(r.filter.ID=function(t){var e=t.replace(et,nt);return function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},r.find.ID=function(t,e){if(void 0!==e.getElementById&&m){var n,r,i,o=e.getElementById(t);if(o){if((n=o.getAttributeNode("id"))&&n.value===t)return[o];for(i=e.getElementsByName(t),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===t)return[o]}return[]}}),r.find.TAG=n.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):n.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"===t){for(;n=o[i++];)1===n.nodeType&&r.push(n);return r}return o},r.find.CLASS=n.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&m)return e.getElementsByClassName(t)},g=[],v=[],(n.qsa=Q.test(f.querySelectorAll))&&(ct((function(t){var e;p.appendChild(t).innerHTML="<a id='"+w+"'></a><select id='"+w+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]=[\\x20\\t\\r\\n\\f]*(?:''|\"\")"),t.querySelectorAll("[selected]").length||v.push("\\[[\\x20\\t\\r\\n\\f]*(?:value|"+L+")"),t.querySelectorAll("[id~="+w+"-]").length||v.push("~="),(e=f.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||v.push("\\[[\\x20\\t\\r\\n\\f]*name[\\x20\\t\\r\\n\\f]*=[\\x20\\t\\r\\n\\f]*(?:''|\"\")"),t.querySelectorAll(":checked").length||v.push(":checked"),t.querySelectorAll("a#"+w+"+*").length||v.push(".#.+[+~]"),t.querySelectorAll("\\\f"),v.push("[\\r\\n\\f]")})),ct((function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=f.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&v.push("name[\\x20\\t\\r\\n\\f]*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&v.push(":enabled",":disabled"),p.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&v.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),v.push(",.*:")}))),(n.matchesSelector=Q.test(y=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ct((function(t){n.disconnectedMatch=y.call(t,"*"),y.call(t,"[s!='']:x"),g.push("!=",R)})),v=v.length&&new RegExp(v.join("|")),g=g.length&&new RegExp(g.join("|")),e=Q.test(p.compareDocumentPosition),b=e||Q.test(p.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,r=e&&e.parentNode;return t===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):t.compareDocumentPosition&&16&t.compareDocumentPosition(r)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},S=e?function(t,e){if(t===e)return d=!0,0;var r=!t.compareDocumentPosition-!e.compareDocumentPosition;return r||(1&(r=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!n.sortDetached&&e.compareDocumentPosition(t)===r?t==f||t.ownerDocument==D&&b(D,t)?-1:e==f||e.ownerDocument==D&&b(D,e)?1:c?O(c,t)-O(c,e):0:4&r?-1:1)}:function(t,e){if(t===e)return d=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,s=[t],a=[e];if(!i||!o)return t==f?-1:e==f?1:i?-1:o?1:c?O(c,t)-O(c,e):0;if(i===o)return ht(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?ht(s[r],a[r]):s[r]==D?-1:a[r]==D?1:0},f):f},at.matches=function(t,e){return at(t,null,null,e)},at.matchesSelector=function(t,e){if(h(t),n.matchesSelector&&m&&!k[e+" "]&&(!g||!g.test(e))&&(!v||!v.test(e)))try{var r=y.call(t,e);if(r||n.disconnectedMatch||t.document&&11!==t.document.nodeType)return r}catch(t){k(e,!0)}return at(e,f,null,[t]).length>0},at.contains=function(t,e){return(t.ownerDocument||t)!=f&&h(t),b(t,e)},at.attr=function(t,e){(t.ownerDocument||t)!=f&&h(t);var i=r.attrHandle[e.toLowerCase()],o=i&&T.call(r.attrHandle,e.toLowerCase())?i(t,e,!m):void 0;return void 0!==o?o:n.attributes||!m?t.getAttribute(e):(o=t.getAttributeNode(e))&&o.specified?o.value:null},at.escape=function(t){return(t+"").replace(rt,it)},at.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},at.uniqueSort=function(t){var e,r=[],i=0,o=0;if(d=!n.detectDuplicates,c=!n.sortStable&&t.slice(0),t.sort(S),d){for(;e=t[o++];)e===t[o]&&(i=r.push(o));for(;i--;)t.splice(r[i],1)}return c=null,t},i=at.getText=function(t){var e,n="",r=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=i(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[r++];)n+=i(e);return n},r=at.selectors={cacheLength:50,createPseudo:lt,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(et,nt),t[3]=(t[3]||t[4]||t[5]||"").replace(et,nt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||at.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&at.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Y.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&W.test(n)&&(e=s(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(et,nt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=A[t+" "];return e||(e=new RegExp("(^|[\\x20\\t\\r\\n\\f])"+t+"("+M+"|$)"))&&A(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(r){var i=at.attr(r,t);return null==i?"!="===e:!e||(i+="","="===e?i===n:"!="===e?i!==n:"^="===e?n&&0===i.indexOf(n):"*="===e?n&&i.indexOf(n)>-1:"$="===e?n&&i.slice(-n.length)===n:"~="===e?(" "+i.replace(H," ")+" ").indexOf(n)>-1:"|="===e&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,r,i){var o="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===r&&0===i?function(t){return!!t.parentNode}:function(e,n,u){var l,c,d,h,f,p,m=o!==s?"nextSibling":"previousSibling",v=e.parentNode,g=a&&e.nodeName.toLowerCase(),y=!u&&!a,b=!1;if(v){if(o){for(;m;){for(h=e;h=h[m];)if(a?h.nodeName.toLowerCase()===g:1===h.nodeType)return!1;p=m="only"===t&&!p&&"nextSibling"}return!0}if(p=[s?v.firstChild:v.lastChild],s&&y){for(b=(f=(l=(c=(d=(h=v)[w]||(h[w]={}))[h.uniqueID]||(d[h.uniqueID]={}))[t]||[])[0]===x&&l[1])&&l[2],h=f&&v.childNodes[f];h=++f&&h&&h[m]||(b=f=0)||p.pop();)if(1===h.nodeType&&++b&&h===e){c[t]=[x,f,b];break}}else if(y&&(b=f=(l=(c=(d=(h=e)[w]||(h[w]={}))[h.uniqueID]||(d[h.uniqueID]={}))[t]||[])[0]===x&&l[1]),!1===b)for(;(h=++f&&h&&h[m]||(b=f=0)||p.pop())&&((a?h.nodeName.toLowerCase()!==g:1!==h.nodeType)||!++b||(y&&((c=(d=h[w]||(h[w]={}))[h.uniqueID]||(d[h.uniqueID]={}))[t]=[x,b]),h!==e)););return(b-=i)===r||b%r==0&&b/r>=0}}},PSEUDO:function(t,e){var n,i=r.pseudos[t]||r.setFilters[t.toLowerCase()]||at.error("unsupported pseudo: "+t);return i[w]?i(e):i.length>1?(n=[t,t,"",e],r.setFilters.hasOwnProperty(t.toLowerCase())?lt((function(t,n){for(var r,o=i(t,e),s=o.length;s--;)t[r=O(t,o[s])]=!(n[r]=o[s])})):function(t){return i(t,0,n)}):i}},pseudos:{not:lt((function(t){var e=[],n=[],r=a(t.replace(U,"$1"));return r[w]?lt((function(t,e,n,i){for(var o,s=r(t,null,i,[]),a=t.length;a--;)(o=s[a])&&(t[a]=!(e[a]=o))})):function(t,i,o){return e[0]=t,r(e,null,o,n),e[0]=null,!n.pop()}})),has:lt((function(t){return function(e){return at(t,e).length>0}})),contains:lt((function(t){return t=t.replace(et,nt),function(e){return(e.textContent||i(e)).indexOf(t)>-1}})),lang:lt((function(t){return G.test(t||"")||at.error("unsupported lang: "+t),t=t.replace(et,nt).toLowerCase(),function(e){var n;do{if(n=m?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===p},focus:function(t){return t===f.activeElement&&(!f.hasFocus||f.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:mt(!1),disabled:mt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!r.pseudos.empty(t)},header:function(t){return J.test(t.nodeName)},input:function(t){return X.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:vt((function(){return[0]})),last:vt((function(t,e){return[e-1]})),eq:vt((function(t,e,n){return[n<0?n+e:n]})),even:vt((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:vt((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:vt((function(t,e,n){for(var r=n<0?n+e:n>e?e:n;--r>=0;)t.push(r);return t})),gt:vt((function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t}))}},r.pseudos.nth=r.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})r.pseudos[e]=ft(e);for(e in{submit:!0,reset:!0})r.pseudos[e]=pt(e);function yt(){}function bt(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function wt(t,e,n){var r=e.dir,i=e.next,o=i||r,s=n&&"parentNode"===o,a=C++;return e.first?function(e,n,i){for(;e=e[r];)if(1===e.nodeType||s)return t(e,n,i);return!1}:function(e,n,u){var l,c,d,h=[x,a];if(u){for(;e=e[r];)if((1===e.nodeType||s)&&t(e,n,u))return!0}else for(;e=e[r];)if(1===e.nodeType||s)if(c=(d=e[w]||(e[w]={}))[e.uniqueID]||(d[e.uniqueID]={}),i&&i===e.nodeName.toLowerCase())e=e[r]||e;else{if((l=c[o])&&l[0]===x&&l[1]===a)return h[2]=l[2];if(c[o]=h,h[2]=t(e,n,u))return!0}return!1}}function Dt(t){return t.length>1?function(e,n,r){for(var i=t.length;i--;)if(!t[i](e,n,r))return!1;return!0}:t[0]}function xt(t,e,n,r,i){for(var o,s=[],a=0,u=t.length,l=null!=e;a<u;a++)(o=t[a])&&(n&&!n(o,r,i)||(s.push(o),l&&e.push(a)));return s}function Ct(t,e,n,r,i,o){return r&&!r[w]&&(r=Ct(r)),i&&!i[w]&&(i=Ct(i,o)),lt((function(o,s,a,u){var l,c,d,h=[],f=[],p=s.length,m=o||function(t,e,n){for(var r=0,i=e.length;r<i;r++)at(t,e[r],n);return n}(e||"*",a.nodeType?[a]:a,[]),v=!t||!o&&e?m:xt(m,h,t,a,u),g=n?i||(o?t:p||r)?[]:s:v;if(n&&n(v,g,a,u),r)for(l=xt(g,f),r(l,[],a,u),c=l.length;c--;)(d=l[c])&&(g[f[c]]=!(v[f[c]]=d));if(o){if(i||t){if(i){for(l=[],c=g.length;c--;)(d=g[c])&&l.push(v[c]=d);i(null,g=[],l,u)}for(c=g.length;c--;)(d=g[c])&&(l=i?O(o,d):h[c])>-1&&(o[l]=!(s[l]=d))}}else g=xt(g===s?g.splice(p,g.length):g),i?i(null,s,g,u):B.apply(s,g)}))}function At(t){for(var e,n,i,o=t.length,s=r.relative[t[0].type],a=s||r.relative[" "],u=s?1:0,c=wt((function(t){return t===e}),a,!0),d=wt((function(t){return O(e,t)>-1}),a,!0),h=[function(t,n,r){var i=!s&&(r||n!==l)||((e=n).nodeType?c(t,n,r):d(t,n,r));return e=null,i}];u<o;u++)if(n=r.relative[t[u].type])h=[wt(Dt(h),n)];else{if((n=r.filter[t[u].type].apply(null,t[u].matches))[w]){for(i=++u;i<o&&!r.relative[t[i].type];i++);return Ct(u>1&&Dt(h),u>1&&bt(t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(U,"$1"),n,u<i&&At(t.slice(u,i)),i<o&&At(t=t.slice(i)),i<o&&bt(t))}h.push(n)}return Dt(h)}return yt.prototype=r.filters=r.pseudos,r.setFilters=new yt,s=at.tokenize=function(t,e){var n,i,o,s,a,u,l,c=E[t+" "];if(c)return e?0:c.slice(0);for(a=t,u=[],l=r.preFilter;a;){for(s in n&&!(i=q.exec(a))||(i&&(a=a.slice(i[0].length)||a),u.push(o=[])),n=!1,(i=z.exec(a))&&(n=i.shift(),o.push({value:n,type:i[0].replace(U," ")}),a=a.slice(n.length)),r.filter)!(i=Y[s].exec(a))||l[s]&&!(i=l[s](i))||(n=i.shift(),o.push({value:n,type:s,matches:i}),a=a.slice(n.length));if(!n)break}return e?a.length:a?at.error(t):E(t,u).slice(0)},a=at.compile=function(t,e){var n,i=[],o=[],a=N[t+" "];if(!a){for(e||(e=s(t)),n=e.length;n--;)(a=At(e[n]))[w]?i.push(a):o.push(a);a=N(t,function(t,e){var n=e.length>0,i=t.length>0,o=function(o,s,a,u,c){var d,p,v,g=0,y="0",b=o&&[],w=[],D=l,C=o||i&&r.find.TAG("*",c),A=x+=null==D?1:Math.random()||.1,E=C.length;for(c&&(l=s==f||s||c);y!==E&&null!=(d=C[y]);y++){if(i&&d){for(p=0,s||d.ownerDocument==f||(h(d),a=!m);v=t[p++];)if(v(d,s||f,a)){u.push(d);break}c&&(x=A)}n&&((d=!v&&d)&&g--,o&&b.push(d))}if(g+=y,n&&y!==g){for(p=0;v=e[p++];)v(b,w,s,a);if(o){if(g>0)for(;y--;)b[y]||w[y]||(w[y]=_.call(u));w=xt(w)}B.apply(u,w),c&&!o&&w.length>0&&g+e.length>1&&at.uniqueSort(u)}return c&&(x=A,l=D),b};return n?lt(o):o}(o,i)),a.selector=t}return a},u=at.select=function(t,e,n,i){var o,u,l,c,d,h="function"==typeof t&&t,f=!i&&s(t=h.selector||t);if(n=n||[],1===f.length){if((u=f[0]=f[0].slice(0)).length>2&&"ID"===(l=u[0]).type&&9===e.nodeType&&m&&r.relative[u[1].type]){if(!(e=(r.find.ID(l.matches[0].replace(et,nt),e)||[])[0]))return n;h&&(e=e.parentNode),t=t.slice(u.shift().value.length)}for(o=Y.needsContext.test(t)?0:u.length;o--&&(l=u[o],!r.relative[c=l.type]);)if((d=r.find[c])&&(i=d(l.matches[0].replace(et,nt),tt.test(u[0].type)&&gt(e.parentNode)||e))){if(u.splice(o,1),!(t=i.length&&bt(u)))return B.apply(n,i),n;break}}return(h||a(t,f))(i,e,!m,n,!e||tt.test(t)&&gt(e.parentNode)||e),n},n.sortStable=w.split("").sort(S).join("")===w,n.detectDuplicates=!!d,h(),n.sortDetached=ct((function(t){return 1&t.compareDocumentPosition(f.createElement("fieldset"))})),ct((function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")}))||dt("type|href|height|width",(function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)})),n.attributes&&ct((function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")}))||dt("value",(function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue})),ct((function(t){return null==t.getAttribute("disabled")}))||dt(L,(function(t,e,n){var r;if(!n)return!0===t[e]?e.toLowerCase():(r=t.getAttributeNode(e))&&r.specified?r.value:null})),at}(r);A.find=N,(A.expr=N.selectors)[":"]=A.expr.pseudos,A.uniqueSort=A.unique=N.uniqueSort,A.text=N.getText,A.isXMLDoc=N.isXML,A.contains=N.contains,A.escapeSelector=N.escape;var k=function(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&A(t).is(n))break;r.push(t)}return r},S=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},T=A.expr.match.needsContext;function F(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var _=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function P(t,e,n){return g(e)?A.grep(t,(function(t,r){return!!e.call(t,r,t)!==n})):e.nodeType?A.grep(t,(function(t){return t===e!==n})):"string"!=typeof e?A.grep(t,(function(t){return c.call(e,t)>-1!==n})):A.filter(e,t,n)}A.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?A.find.matchesSelector(r,t)?[r]:[]:A.find.matches(t,A.grep(e,(function(t){return 1===t.nodeType})))},A.fn.extend({find:function(t){var e,n,r=this.length,i=this;if("string"!=typeof t)return this.pushStack(A(t).filter((function(){for(e=0;e<r;e++)if(A.contains(i[e],this))return!0})));for(n=this.pushStack([]),e=0;e<r;e++)A.find(t,i[e],n);return r>1?A.uniqueSort(n):n},filter:function(t){return this.pushStack(P(this,t||[],!1))},not:function(t){return this.pushStack(P(this,t||[],!0))},is:function(t){return!!P(this,"string"==typeof t&&T.test(t)?A(t):t||[],!1).length}});var B,I=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(A.fn.init=function(t,e,n){var r,i;if(!t)return this;if(n=n||B,"string"==typeof t){if(!(r="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:I.exec(t))||!r[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(r[1]){if(e=e instanceof A?e[0]:e,A.merge(this,A.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:b,!0)),_.test(r[1])&&A.isPlainObject(e))for(r in e)g(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):g(t)?void 0!==n.ready?n.ready(t):t(A):A.makeArray(t,this)}).prototype=A.fn,B=A(b);var O=/^(?:parents|prev(?:Until|All))/,L={children:!0,contents:!0,next:!0,prev:!0};function M(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}A.fn.extend({has:function(t){var e=A(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(A.contains(this,e[t]))return!0}))},closest:function(t,e){var n,r=0,i=this.length,o=[],s="string"!=typeof t&&A(t);if(!T.test(t))for(;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&A.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(o.length>1?A.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?c.call(A(t),this[0]):c.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(A.uniqueSort(A.merge(this.get(),A(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),A.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return k(t,"parentNode")},parentsUntil:function(t,e,n){return k(t,"parentNode",n)},next:function(t){return M(t,"nextSibling")},prev:function(t){return M(t,"previousSibling")},nextAll:function(t){return k(t,"nextSibling")},prevAll:function(t){return k(t,"previousSibling")},nextUntil:function(t,e,n){return k(t,"nextSibling",n)},prevUntil:function(t,e,n){return k(t,"previousSibling",n)},siblings:function(t){return S((t.parentNode||{}).firstChild,t)},children:function(t){return S(t.firstChild)},contents:function(t){return null!=t.contentDocument&&s(t.contentDocument)?t.contentDocument:(F(t,"template")&&(t=t.content||t),A.merge([],t.childNodes))}},(function(t,e){A.fn[t]=function(n,r){var i=A.map(this,e,n);return"Until"!==t.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=A.filter(r,i)),this.length>1&&(L[t]||A.uniqueSort(i),O.test(t)&&i.reverse()),this.pushStack(i)}}));var $=/[^\x20\t\r\n\f]+/g;function j(t){return t}function R(t){throw t}function H(t,e,n,r){var i;try{t&&g(i=t.promise)?i.call(t).done(e).fail(n):t&&g(i=t.then)?i.call(t,e,n):e.apply(void 0,[t].slice(r))}catch(t){n.apply(void 0,[t])}}A.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return A.each(t.match($)||[],(function(t,n){e[n]=!0})),e}(t):A.extend({},t);var e,n,r,i,o=[],s=[],a=-1,u=function(){for(i=i||t.once,r=e=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&t.stopOnFalse&&(a=o.length,n=!1);t.memory||(n=!1),e=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!e&&(a=o.length-1,s.push(n)),function e(n){A.each(n,(function(n,r){g(r)?t.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==x(r)&&e(r)}))}(arguments),n&&!e&&u()),this},remove:function(){return A.each(arguments,(function(t,e){for(var n;(n=A.inArray(e,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(t){return t?A.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||e||(o=n=""),this},locked:function(){return!!i},fireWith:function(t,n){return i||(n=[t,(n=n||[]).slice?n.slice():n],s.push(n),e||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},A.extend({Deferred:function(t){var e=[["notify","progress",A.Callbacks("memory"),A.Callbacks("memory"),2],["resolve","done",A.Callbacks("once memory"),A.Callbacks("once memory"),0,"resolved"],["reject","fail",A.Callbacks("once memory"),A.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return i.then(null,t)},pipe:function(){var t=arguments;return A.Deferred((function(n){A.each(e,(function(e,r){var i=g(t[r[4]])&&t[r[4]];o[r[1]]((function(){var t=i&&i.apply(this,arguments);t&&g(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[t]:arguments)}))})),t=null})).promise()},then:function(t,n,i){var o=0;function s(t,e,n,i){return function(){var a=this,u=arguments,l=function(){var r,l;if(!(t<o)){if((r=n.apply(a,u))===e.promise())throw new TypeError("Thenable self-resolution");l=r&&("object"==typeof r||"function"==typeof r)&&r.then,g(l)?i?l.call(r,s(o,e,j,i),s(o,e,R,i)):(o++,l.call(r,s(o,e,j,i),s(o,e,R,i),s(o,e,j,e.notifyWith))):(n!==j&&(a=void 0,u=[r]),(i||e.resolveWith)(a,u))}},c=i?l:function(){try{l()}catch(r){A.Deferred.exceptionHook&&A.Deferred.exceptionHook(r,c.stackTrace),t+1>=o&&(n!==R&&(a=void 0,u=[r]),e.rejectWith(a,u))}};t?c():(A.Deferred.getStackHook&&(c.stackTrace=A.Deferred.getStackHook()),r.setTimeout(c))}}return A.Deferred((function(r){e[0][3].add(s(0,r,g(i)?i:j,r.notifyWith)),e[1][3].add(s(0,r,g(t)?t:j)),e[2][3].add(s(0,r,g(n)?n:R))})).promise()},promise:function(t){return null!=t?A.extend(t,i):i}},o={};return A.each(e,(function(t,r){var s=r[2],a=r[5];i[r[1]]=s.add,a&&s.add((function(){n=a}),e[3-t][2].disable,e[3-t][3].disable,e[0][2].lock,e[0][3].lock),s.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=s.fireWith})),i.promise(o),t&&t.call(o,o),o},when:function(t){var e=arguments.length,n=e,r=Array(n),i=a.call(arguments),o=A.Deferred(),s=function(t){return function(n){r[t]=this,i[t]=arguments.length>1?a.call(arguments):n,--e||o.resolveWith(r,i)}};if(e<=1&&(H(t,o.done(s(n)).resolve,o.reject,!e),"pending"===o.state()||g(i[n]&&i[n].then)))return o.then();for(;n--;)H(i[n],s(n),o.reject);return o.promise()}});var U=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;A.Deferred.exceptionHook=function(t,e){r.console&&r.console.warn&&t&&U.test(t.name)&&r.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},A.readyException=function(t){r.setTimeout((function(){throw t}))};var q=A.Deferred();function z(){b.removeEventListener("DOMContentLoaded",z),r.removeEventListener("load",z),A.ready()}A.fn.ready=function(t){return q.then(t).catch((function(t){A.readyException(t)})),this},A.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--A.readyWait:A.isReady)||(A.isReady=!0,!0!==t&&--A.readyWait>0||q.resolveWith(b,[A]))}}),A.ready.then=q.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(A.ready):(b.addEventListener("DOMContentLoaded",z),r.addEventListener("load",z));var V=function t(e,n,r,i,o,s,a){var u=0,l=e.length,c=null==r;if("object"===x(r))for(u in o=!0,r)t(e,n,u,r[u],!0,s,a);else if(void 0!==i&&(o=!0,g(i)||(a=!0),c&&(a?(n.call(e,i),n=null):(c=n,n=function(t,e,n){return c.call(A(t),n)})),n))for(;u<l;u++)n(e[u],r,a?i:i.call(e[u],u,n(e[u],r)));return o?e:c?n.call(e):l?n(e[0],r):s},W=/^-ms-/,G=/-([a-z])/g;function Y(t,e){return e.toUpperCase()}function K(t){return t.replace(W,"ms-").replace(G,Y)}var X=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function J(){this.expando=A.expando+J.uid++}J.uid=1,J.prototype={cache:function(t){var e=t[this.expando];return e||(e={},X(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[K(e)]=n;else for(r in e)i[K(r)]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][K(e)]},access:function(t,e,n){return void 0===e||e&&"string"==typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r=t[this.expando];if(void 0!==r){if(void 0!==e){n=(e=Array.isArray(e)?e.map(K):(e=K(e))in r?[e]:e.match($)||[]).length;for(;n--;)delete r[e[n]]}(void 0===e||A.isEmptyObject(r))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!A.isEmptyObject(e)}};var Q=new J,Z=new J,tt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,et=/[A-Z]/g;function nt(t,e,n){var r;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(et,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:tt.test(t)?JSON.parse(t):t)}(n)}catch(t){}Z.set(t,e,n)}else n=void 0;return n}A.extend({hasData:function(t){return Z.hasData(t)||Q.hasData(t)},data:function(t,e,n){return Z.access(t,e,n)},removeData:function(t,e){Z.remove(t,e)},_data:function(t,e,n){return Q.access(t,e,n)},_removeData:function(t,e){Q.remove(t,e)}}),A.fn.extend({data:function(t,e){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===t){if(this.length&&(i=Z.get(o),1===o.nodeType&&!Q.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=K(r.slice(5)),nt(o,r,i[r]));Q.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof t?this.each((function(){Z.set(this,t)})):V(this,(function(e){var n;if(o&&void 0===e)return void 0!==(n=Z.get(o,t))||void 0!==(n=nt(o,t))?n:void 0;this.each((function(){Z.set(this,t,e)}))}),null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each((function(){Z.remove(this,t)}))}}),A.extend({queue:function(t,e,n){var r;if(t)return e=(e||"fx")+"queue",r=Q.get(t,e),n&&(!r||Array.isArray(n)?r=Q.access(t,e,A.makeArray(n)):r.push(n)),r||[]},dequeue:function(t,e){var n=A.queue(t,e=e||"fx"),r=n.length,i=n.shift(),o=A._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,(function(){A.dequeue(t,e)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return Q.get(t,n)||Q.access(t,n,{empty:A.Callbacks("once memory").add((function(){Q.remove(t,[e+"queue",n])}))})}}),A.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?A.queue(this[0],t):void 0===e?this:this.each((function(){var n=A.queue(this,t,e);A._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&A.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){A.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,r=1,i=A.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";s--;)(n=Q.get(o[s],t+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(e)}});var rt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,it=new RegExp("^(?:([+-])=|)("+rt+")([a-z%]*)$","i"),ot=["Top","Right","Bottom","Left"],st=b.documentElement,at=function(t){return A.contains(t.ownerDocument,t)},ut={composed:!0};st.getRootNode&&(at=function(t){return A.contains(t.ownerDocument,t)||t.getRootNode(ut)===t.ownerDocument});var lt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&at(t)&&"none"===A.css(t,"display")};function ct(t,e,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return A.css(t,e,"")},u=a(),l=n&&n[3]||(A.cssNumber[e]?"":"px"),c=t.nodeType&&(A.cssNumber[e]||"px"!==l&&+u)&&it.exec(A.css(t,e));if(c&&c[3]!==l){for(u/=2,l=l||c[3],c=+u||1;s--;)A.style(t,e,c+l),(1-o)*(1-(o=a()/u||.5))<=0&&(s=0),c/=o;A.style(t,e,(c*=2)+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var dt={};function ht(t){var e,n=t.ownerDocument,r=t.nodeName,i=dt[r];return i||(e=n.body.appendChild(n.createElement(r)),i=A.css(e,"display"),e.parentNode.removeChild(e),"none"===i&&(i="block"),dt[r]=i,i)}function ft(t,e){for(var n,r,i=[],o=0,s=t.length;o<s;o++)(r=t[o]).style&&(n=r.style.display,e?("none"===n&&(i[o]=Q.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&lt(r)&&(i[o]=ht(r))):"none"!==n&&(i[o]="none",Q.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(t[o].style.display=i[o]);return t}A.fn.extend({show:function(){return ft(this,!0)},hide:function(){return ft(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){lt(this)?A(this).show():A(this).hide()}))}});var pt,mt,vt=/^(?:checkbox|radio)$/i,gt=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,yt=/^$|^module$|\/(?:java|ecma)script/i;pt=b.createDocumentFragment().appendChild(b.createElement("div")),(mt=b.createElement("input")).setAttribute("type","radio"),mt.setAttribute("checked","checked"),mt.setAttribute("name","t"),pt.appendChild(mt),v.checkClone=pt.cloneNode(!0).cloneNode(!0).lastChild.checked,pt.innerHTML="<textarea>x</textarea>",v.noCloneChecked=!!pt.cloneNode(!0).lastChild.defaultValue,pt.innerHTML="<option></option>",v.option=!!pt.lastChild;var bt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function wt(t,e){var n;return n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&F(t,e)?A.merge([t],n):n}function Dt(t,e){for(var n=0,r=t.length;n<r;n++)Q.set(t[n],"globalEval",!e||Q.get(e[n],"globalEval"))}bt.tbody=bt.tfoot=bt.colgroup=bt.caption=bt.thead,bt.th=bt.td,v.option||(bt.optgroup=bt.option=[1,"<select multiple='multiple'>","</select>"]);var xt=/<|&#?\w+;/;function Ct(t,e,n,r,i){for(var o,s,a,u,l,c,d=e.createDocumentFragment(),h=[],f=0,p=t.length;f<p;f++)if((o=t[f])||0===o)if("object"===x(o))A.merge(h,o.nodeType?[o]:o);else if(xt.test(o)){for(s=s||d.appendChild(e.createElement("div")),a=(gt.exec(o)||["",""])[1].toLowerCase(),u=bt[a]||bt._default,s.innerHTML=u[1]+A.htmlPrefilter(o)+u[2],c=u[0];c--;)s=s.lastChild;A.merge(h,s.childNodes),(s=d.firstChild).textContent=""}else h.push(e.createTextNode(o));for(d.textContent="",f=0;o=h[f++];)if(r&&A.inArray(o,r)>-1)i&&i.push(o);else if(l=at(o),s=wt(d.appendChild(o),"script"),l&&Dt(s),n)for(c=0;o=s[c++];)yt.test(o.type||"")&&n.push(o);return d}var At=/^([^.]*)(?:\.(.+)|)/;function Et(){return!0}function Nt(){return!1}function kt(t,e){return t===function(){try{return b.activeElement}catch(t){}}()==("focus"===e)}function St(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)St(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Nt;else if(!i)return t;return 1===o&&(s=i,i=function(t){return A().off(t),s.apply(this,arguments)},i.guid=s.guid||(s.guid=A.guid++)),t.each((function(){A.event.add(this,e,i,r,n)}))}function Tt(t,e,n){n?(Q.set(t,e,!1),A.event.add(t,e,{namespace:!1,handler:function(t){var r,i,o=Q.get(this,e);if(1&t.isTrigger&&this[e]){if(o.length)(A.event.special[e]||{}).delegateType&&t.stopPropagation();else if(o=a.call(arguments),Q.set(this,e,o),r=n(this,e),this[e](),o!==(i=Q.get(this,e))||r?Q.set(this,e,!1):i={},o!==i)return t.stopImmediatePropagation(),t.preventDefault(),i&&i.value}else o.length&&(Q.set(this,e,{value:A.event.trigger(A.extend(o[0],A.Event.prototype),o.slice(1),this)}),t.stopImmediatePropagation())}})):void 0===Q.get(t,e)&&A.event.add(t,e,Et)}A.event={global:{},add:function(t,e,n,r,i){var o,s,a,u,l,c,d,h,f,p,m,v=Q.get(t);if(X(t))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&A.find.matchesSelector(st,i),n.guid||(n.guid=A.guid++),(u=v.events)||(u=v.events=Object.create(null)),(s=v.handle)||(s=v.handle=function(e){return A.event.triggered!==e.type?A.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match($)||[""]).length;l--;)f=m=(a=At.exec(e[l])||[])[1],p=(a[2]||"").split(".").sort(),f&&(d=A.event.special[f]||{},f=(i?d.delegateType:d.bindType)||f,d=A.event.special[f]||{},c=A.extend({type:f,origType:m,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&A.expr.match.needsContext.test(i),namespace:p.join(".")},o),(h=u[f])||((h=u[f]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(t,r,p,s)||t.addEventListener&&t.addEventListener(f,s)),d.add&&(d.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),i?h.splice(h.delegateCount++,0,c):h.push(c),A.event.global[f]=!0)},remove:function(t,e,n,r,i){var o,s,a,u,l,c,d,h,f,p,m,v=Q.hasData(t)&&Q.get(t);if(v&&(u=v.events)){for(l=(e=(e||"").match($)||[""]).length;l--;)if(f=m=(a=At.exec(e[l])||[])[1],p=(a[2]||"").split(".").sort(),f){for(d=A.event.special[f]||{},h=u[f=(r?d.delegateType:d.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=h.length;o--;)c=h[o],!i&&m!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(h.splice(o,1),c.selector&&h.delegateCount--,d.remove&&d.remove.call(t,c));s&&!h.length&&(d.teardown&&!1!==d.teardown.call(t,p,v.handle)||A.removeEvent(t,f,v.handle),delete u[f])}else for(f in u)A.event.remove(t,f+e[l],n,r,!0);A.isEmptyObject(u)&&Q.remove(t,"handle events")}},dispatch:function(t){var e,n,r,i,o,s,a=new Array(arguments.length),u=A.event.fix(t),l=(Q.get(this,"events")||Object.create(null))[u.type]||[],c=A.event.special[u.type]||{};for(a[0]=u,e=1;e<arguments.length;e++)a[e]=arguments[e];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){for(s=A.event.handlers.call(this,u,l),e=0;(i=s[e++])&&!u.isPropagationStopped();)for(u.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!u.isImmediatePropagationStopped();)u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,void 0!==(r=((A.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(t,e){var n,r,i,o,s,a=[],u=e.delegateCount,l=t.target;if(u&&l.nodeType&&!("click"===t.type&&t.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==t.type||!0!==l.disabled)){for(o=[],s={},n=0;n<u;n++)void 0===s[i=(r=e[n]).selector+" "]&&(s[i]=r.needsContext?A(i,this).index(l)>-1:A.find(i,this,null,[l]).length),s[i]&&o.push(r);o.length&&a.push({elem:l,handlers:o})}return l=this,u<e.length&&a.push({elem:l,handlers:e.slice(u)}),a},addProp:function(t,e){Object.defineProperty(A.Event.prototype,t,{enumerable:!0,configurable:!0,get:g(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[A.expando]?t:new A.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return vt.test(e.type)&&e.click&&F(e,"input")&&Tt(e,"click",Et),!1},trigger:function(t){var e=this||t;return vt.test(e.type)&&e.click&&F(e,"input")&&Tt(e,"click"),!0},_default:function(t){var e=t.target;return vt.test(e.type)&&e.click&&F(e,"input")&&Q.get(e,"click")||F(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},A.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},(A.Event=function(t,e){if(!(this instanceof A.Event))return new A.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Et:Nt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&A.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[A.expando]=!0}).prototype={constructor:A.Event,isDefaultPrevented:Nt,isPropagationStopped:Nt,isImmediatePropagationStopped:Nt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Et,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Et,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Et,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},A.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},A.event.addProp),A.each({focus:"focusin",blur:"focusout"},(function(t,e){A.event.special[t]={setup:function(){return Tt(this,t,kt),!1},trigger:function(){return Tt(this,t),!0},_default:function(e){return Q.get(e.target,t)},delegateType:e}})),A.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){A.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,r=this,i=t.relatedTarget,o=t.handleObj;return i&&(i===r||A.contains(r,i))||(t.type=o.origType,n=o.handler.apply(this,arguments),t.type=e),n}}})),A.fn.extend({on:function(t,e,n,r){return St(this,t,e,n,r)},one:function(t,e,n,r){return St(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,A(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof t){for(i in t)this.off(i,e,t[i]);return this}return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=Nt),this.each((function(){A.event.remove(this,t,n,e)}))}});var Ft=/<script|<style|<link/i,_t=/checked\s*(?:[^=]|=\s*.checked.)/i,Pt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Bt(t,e){return F(t,"table")&&F(11!==e.nodeType?e:e.firstChild,"tr")&&A(t).children("tbody")[0]||t}function It(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Ot(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Lt(t,e){var n,r,i,o,s,a;if(1===e.nodeType){if(Q.hasData(t)&&(a=Q.get(t).events))for(i in Q.remove(e,"handle events"),a)for(n=0,r=a[i].length;n<r;n++)A.event.add(e,i,a[i][n]);Z.hasData(t)&&(o=Z.access(t),s=A.extend({},o),Z.set(e,s))}}function Mt(t,e){var n=e.nodeName.toLowerCase();"input"===n&&vt.test(t.type)?e.checked=t.checked:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}function $t(t,e,n,r){e=u(e);var i,o,s,a,l,c,d=0,h=t.length,f=h-1,p=e[0],m=g(p);if(m||h>1&&"string"==typeof p&&!v.checkClone&&_t.test(p))return t.each((function(i){var o=t.eq(i);m&&(e[0]=p.call(this,i,o.html())),$t(o,e,n,r)}));if(h&&(o=(i=Ct(e,t[0].ownerDocument,!1,t,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=A.map(wt(i,"script"),It)).length;d<h;d++)l=i,d!==f&&(l=A.clone(l,!0,!0),a&&A.merge(s,wt(l,"script"))),n.call(t[d],l,d);if(a)for(c=s[s.length-1].ownerDocument,A.map(s,Ot),d=0;d<a;d++)l=s[d],yt.test(l.type||"")&&!Q.access(l,"globalEval")&&A.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?A._evalUrl&&!l.noModule&&A._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},c):D(l.textContent.replace(Pt,""),l,c))}return t}function jt(t,e,n){for(var r,i=e?A.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||A.cleanData(wt(r)),r.parentNode&&(n&&at(r)&&Dt(wt(r,"script")),r.parentNode.removeChild(r));return t}A.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var r,i,o,s,a=t.cloneNode(!0),u=at(t);if(!(v.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||A.isXMLDoc(t)))for(s=wt(a),r=0,i=(o=wt(t)).length;r<i;r++)Mt(o[r],s[r]);if(e)if(n)for(o=o||wt(t),s=s||wt(a),r=0,i=o.length;r<i;r++)Lt(o[r],s[r]);else Lt(t,a);return(s=wt(a,"script")).length>0&&Dt(s,!u&&wt(t,"script")),a},cleanData:function(t){for(var e,n,r,i=A.event.special,o=0;void 0!==(n=t[o]);o++)if(X(n)){if(e=n[Q.expando]){if(e.events)for(r in e.events)i[r]?A.event.remove(n,r):A.removeEvent(n,r,e.handle);n[Q.expando]=void 0}n[Z.expando]&&(n[Z.expando]=void 0)}}}),A.fn.extend({detach:function(t){return jt(this,t,!0)},remove:function(t){return jt(this,t)},text:function(t){return V(this,(function(t){return void 0===t?A.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return $t(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Bt(this,t).appendChild(t)}))},prepend:function(){return $t(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Bt(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return $t(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return $t(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(A.cleanData(wt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return A.clone(this,t,e)}))},html:function(t){return V(this,(function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!Ft.test(t)&&!bt[(gt.exec(t)||["",""])[1].toLowerCase()]){t=A.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(A.cleanData(wt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return $t(this,arguments,(function(e){var n=this.parentNode;A.inArray(this,t)<0&&(A.cleanData(wt(this)),n&&n.replaceChild(e,this))}),t)}}),A.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){A.fn[t]=function(t){for(var n,r=[],i=A(t),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),A(i[s])[e](n),l.apply(r,n.get());return this.pushStack(r)}}));var Rt=new RegExp("^("+rt+")(?!px)[a-z%]+$","i"),Ht=/^--/,Ut=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=r),e.getComputedStyle(t)},qt=function(t,e,n){var r,i,o={};for(i in e)o[i]=t.style[i],t.style[i]=e[i];for(i in r=n.call(t),e)t.style[i]=o[i];return r},zt=new RegExp(ot.join("|"),"i"),Vt=new RegExp("^[\\x20\\t\\r\\n\\f]+|((?:^|[^\\\\])(?:\\\\.)*)[\\x20\\t\\r\\n\\f]+$","g");function Wt(t,e,n){var r,i,o,s,a=Ht.test(e),u=t.style;return(n=n||Ut(t))&&(s=n.getPropertyValue(e)||n[e],a&&(s=s.replace(Vt,"$1")),""!==s||at(t)||(s=A.style(t,e)),!v.pixelBoxStyles()&&Rt.test(s)&&zt.test(e)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=s,s=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==s?s+"":s}function Gt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function t(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",st.appendChild(l).appendChild(c);var t=r.getComputedStyle(c);n="1%"!==t.top,u=12===e(t.marginLeft),c.style.right="60%",s=36===e(t.right),i=36===e(t.width),c.style.position="absolute",o=12===e(c.offsetWidth/3),st.removeChild(l),c=null}}function e(t){return Math.round(parseFloat(t))}var n,i,o,s,a,u,l=b.createElement("div"),c=b.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",v.clearCloneStyle="content-box"===c.style.backgroundClip,A.extend(v,{boxSizingReliable:function(){return t(),i},pixelBoxStyles:function(){return t(),s},pixelPosition:function(){return t(),n},reliableMarginLeft:function(){return t(),u},scrollboxSize:function(){return t(),o},reliableTrDimensions:function(){var t,e,n,i;return null==a&&(t=b.createElement("table"),e=b.createElement("tr"),n=b.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="border:1px solid",e.style.height="1px",n.style.height="9px",n.style.display="block",st.appendChild(t).appendChild(e).appendChild(n),i=r.getComputedStyle(e),a=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===e.offsetHeight,st.removeChild(t)),a}}))}();var Yt=["Webkit","Moz","ms"],Kt=b.createElement("div").style,Xt={};function Jt(t){return A.cssProps[t]||Xt[t]||(t in Kt?t:Xt[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),n=Yt.length;n--;)if((t=Yt[n]+e)in Kt)return t}(t)||t)}var Qt=/^(none|table(?!-c[ea]).+)/,Zt={position:"absolute",visibility:"hidden",display:"block"},te={letterSpacing:"0",fontWeight:"400"};function ee(t,e,n){var r=it.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function ne(t,e,n,r,i,o){var s="width"===e?1:0,a=0,u=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(u+=A.css(t,n+ot[s],!0,i)),r?("content"===n&&(u-=A.css(t,"padding"+ot[s],!0,i)),"margin"!==n&&(u-=A.css(t,"border"+ot[s]+"Width",!0,i))):(u+=A.css(t,"padding"+ot[s],!0,i),"padding"!==n?u+=A.css(t,"border"+ot[s]+"Width",!0,i):a+=A.css(t,"border"+ot[s]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-u-a-.5))||0),u}function re(t,e,n){var r=Ut(t),i=(!v.boxSizingReliable()||n)&&"border-box"===A.css(t,"boxSizing",!1,r),o=i,s=Wt(t,e,r),a="offset"+e[0].toUpperCase()+e.slice(1);if(Rt.test(s)){if(!n)return s;s="auto"}return(!v.boxSizingReliable()&&i||!v.reliableTrDimensions()&&F(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===A.css(t,"display",!1,r))&&t.getClientRects().length&&(i="border-box"===A.css(t,"boxSizing",!1,r),(o=a in t)&&(s=t[a])),(s=parseFloat(s)||0)+ne(t,e,n||(i?"border":"content"),o,r,s)+"px"}function ie(t,e,n,r,i){return new ie.prototype.init(t,e,n,r,i)}A.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=Wt(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,o,s,a=K(e),u=Ht.test(e),l=t.style;if(u||(e=Jt(a)),s=A.cssHooks[e]||A.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:l[e];"string"==(o=typeof n)&&(i=it.exec(n))&&i[1]&&(n=ct(t,e,i),o="number"),null!=n&&n==n&&("number"!==o||u||(n+=i&&i[3]||(A.cssNumber[a]?"":"px")),v.clearCloneStyle||""!==n||0!==e.indexOf("background")||(l[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,r))||(u?l.setProperty(e,n):l[e]=n))}},css:function(t,e,n,r){var i,o,s,a=K(e);return Ht.test(e)||(e=Jt(a)),(s=A.cssHooks[e]||A.cssHooks[a])&&"get"in s&&(i=s.get(t,!0,n)),void 0===i&&(i=Wt(t,e,r)),"normal"===i&&e in te&&(i=te[e]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),A.each(["height","width"],(function(t,e){A.cssHooks[e]={get:function(t,n,r){if(n)return!Qt.test(A.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?re(t,e,r):qt(t,Zt,(function(){return re(t,e,r)}))},set:function(t,n,r){var i,o=Ut(t),s=!v.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===A.css(t,"boxSizing",!1,o),u=r?ne(t,e,r,a,o):0;return a&&s&&(u-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-ne(t,e,"border",!1,o)-.5)),u&&(i=it.exec(n))&&"px"!==(i[3]||"px")&&(t.style[e]=n,n=A.css(t,e)),ee(0,n,u)}}})),A.cssHooks.marginLeft=Gt(v.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(Wt(t,"marginLeft"))||t.getBoundingClientRect().left-qt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),A.each({margin:"",padding:"",border:"Width"},(function(t,e){A.cssHooks[t+e]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[t+ot[r]+e]=o[r]||o[r-2]||o[0];return i}},"margin"!==t&&(A.cssHooks[t+e].set=ee)})),A.fn.extend({css:function(t,e){return V(this,(function(t,e,n){var r,i,o={},s=0;if(Array.isArray(e)){for(r=Ut(t),i=e.length;s<i;s++)o[e[s]]=A.css(t,e[s],!1,r);return o}return void 0!==n?A.style(t,e,n):A.css(t,e)}),t,e,arguments.length>1)}}),A.Tween=ie,ie.prototype={constructor:ie,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||A.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(A.cssNumber[n]?"":"px")},cur:function(){var t=ie.propHooks[this.prop];return t&&t.get?t.get(this):ie.propHooks._default.get(this)},run:function(t){var e,n=ie.propHooks[this.prop];return this.options.duration?this.pos=e=A.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ie.propHooks._default.set(this),this}},ie.prototype.init.prototype=ie.prototype,ie.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=A.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){A.fx.step[t.prop]?A.fx.step[t.prop](t):1!==t.elem.nodeType||!A.cssHooks[t.prop]&&null==t.elem.style[Jt(t.prop)]?t.elem[t.prop]=t.now:A.style(t.elem,t.prop,t.now+t.unit)}}},ie.propHooks.scrollTop=ie.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},A.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},(A.fx=ie.prototype.init).step={};var oe,se,ae=/^(?:toggle|show|hide)$/,ue=/queueHooks$/;function le(){se&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(le):r.setTimeout(le,A.fx.interval),A.fx.tick())}function ce(){return r.setTimeout((function(){oe=void 0})),oe=Date.now()}function de(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=ot[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function he(t,e,n){for(var r,i=(fe.tweeners[e]||[]).concat(fe.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function fe(t,e,n){var r,i,o=0,s=fe.prefilters.length,a=A.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var e=oe||ce(),n=Math.max(0,l.startTime+l.duration-e),r=1-(n/l.duration||0),o=0,s=l.tweens.length;o<s;o++)l.tweens[o].run(r);return a.notifyWith(t,[l,r,n]),r<1&&s?n:(s||a.notifyWith(t,[l,1,0]),a.resolveWith(t,[l]),!1)},l=a.promise({elem:t,props:A.extend({},e),opts:A.extend(!0,{specialEasing:{},easing:A.easing._default},n),originalProperties:e,originalOptions:n,startTime:oe||ce(),duration:n.duration,tweens:[],createTween:function(e,n){var r=A.Tween(t,l.opts,e,n,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(r),r},stop:function(e){var n=0,r=e?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return e?(a.notifyWith(t,[l,1,0]),a.resolveWith(t,[l,e])):a.rejectWith(t,[l,e]),this}}),c=l.props;for(function(t,e){var n,r,i,o,s;for(n in t)if(i=e[r=K(n)],o=t[n],Array.isArray(o)&&(i=o[1],o=t[n]=o[0]),n!==r&&(t[r]=o,delete t[n]),(s=A.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete t[r],o)n in t||(t[n]=o[n],e[n]=i);else e[r]=i}(c,l.opts.specialEasing);o<s;o++)if(r=fe.prefilters[o].call(l,t,c,l.opts))return g(r.stop)&&(A._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return A.map(c,he,l),g(l.opts.start)&&l.opts.start.call(t,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),A.fx.timer(A.extend(u,{elem:t,anim:l,queue:l.opts.queue})),l}A.Animation=A.extend(fe,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return ct(n.elem,t,it.exec(e),n),n}]},tweener:function(t,e){g(t)?(e=t,t=["*"]):t=t.match($);for(var n,r=0,i=t.length;r<i;r++)n=t[r],fe.tweeners[n]=fe.tweeners[n]||[],fe.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var r,i,o,s,a,u,l,c,d="width"in e||"height"in e,h=this,f={},p=t.style,m=t.nodeType&&lt(t),v=Q.get(t,"fxshow");for(r in n.queue||(null==(s=A._queueHooks(t,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,h.always((function(){h.always((function(){s.unqueued--,A.queue(t,"fx").length||s.empty.fire()}))}))),e)if(i=e[r],ae.test(i)){if(delete e[r],o=o||"toggle"===i,i===(m?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;m=!0}f[r]=v&&v[r]||A.style(t,r)}if((u=!A.isEmptyObject(e))||!A.isEmptyObject(f))for(r in d&&1===t.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(l=v&&v.display)&&(l=Q.get(t,"display")),"none"===(c=A.css(t,"display"))&&(l?c=l:(ft([t],!0),l=t.style.display||l,c=A.css(t,"display"),ft([t]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===A.css(t,"float")&&(u||(h.done((function(){p.display=l})),null==l&&(c=p.display,l="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",h.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),u=!1,f)u||(v?"hidden"in v&&(m=v.hidden):v=Q.access(t,"fxshow",{display:l}),o&&(v.hidden=!m),m&&ft([t],!0),h.done((function(){for(r in m||ft([t]),Q.remove(t,"fxshow"),f)A.style(t,r,f[r])}))),u=he(m?v[r]:0,r,h),r in v||(v[r]=u.start,m&&(u.end=u.start,u.start=0))}],prefilter:function(t,e){e?fe.prefilters.unshift(t):fe.prefilters.push(t)}}),A.speed=function(t,e,n){var r=t&&"object"==typeof t?A.extend({},t):{complete:n||!n&&e||g(t)&&t,duration:t,easing:n&&e||e&&!g(e)&&e};return A.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in A.fx.speeds?r.duration=A.fx.speeds[r.duration]:r.duration=A.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){g(r.old)&&r.old.call(this),r.queue&&A.dequeue(this,r.queue)},r},A.fn.extend({fadeTo:function(t,e,n,r){return this.filter(lt).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(t,e,n,r){var i=A.isEmptyObject(t),o=A.speed(e,n,r),s=function(){var e=fe(this,A.extend({},t),o);(i||Q.get(this,"finish"))&&e.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(t,e,n){var r=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,i=null!=t&&t+"queueHooks",o=A.timers,s=Q.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&ue.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=t&&o[i].queue!==t||(o[i].anim.stop(n),e=!1,o.splice(i,1));!e&&n||A.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=Q.get(this),r=n[t+"queue"],i=n[t+"queueHooks"],o=A.timers,s=r?r.length:0;for(n.finish=!0,A.queue(this,t,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<s;e++)r[e]&&r[e].finish&&r[e].finish.call(this);delete n.finish}))}}),A.each(["toggle","show","hide"],(function(t,e){var n=A.fn[e];A.fn[e]=function(t,r,i){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(de(e,!0),t,r,i)}})),A.each({slideDown:de("show"),slideUp:de("hide"),slideToggle:de("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){A.fn[t]=function(t,n,r){return this.animate(e,t,n,r)}})),A.timers=[],A.fx.tick=function(){var t,e=0,n=A.timers;for(oe=Date.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||A.fx.stop(),oe=void 0},A.fx.timer=function(t){A.timers.push(t),A.fx.start()},A.fx.interval=13,A.fx.start=function(){se||(se=!0,le())},A.fx.stop=function(){se=null},A.fx.speeds={slow:600,fast:200,_default:400},A.fn.delay=function(t,e){return t=A.fx&&A.fx.speeds[t]||t,e=e||"fx",this.queue(e,(function(e,n){var i=r.setTimeout(e,t);n.stop=function(){r.clearTimeout(i)}}))},function(){var t=b.createElement("input"),e=b.createElement("select").appendChild(b.createElement("option"));t.type="checkbox",v.checkOn=""!==t.value,v.optSelected=e.selected,(t=b.createElement("input")).value="t",t.type="radio",v.radioValue="t"===t.value}();var pe,me=A.expr.attrHandle;A.fn.extend({attr:function(t,e){return V(this,A.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each((function(){A.removeAttr(this,t)}))}}),A.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?A.prop(t,e,n):(1===o&&A.isXMLDoc(t)||(i=A.attrHooks[e.toLowerCase()]||(A.expr.match.bool.test(e)?pe:void 0)),void 0!==n?null===n?void A.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))?r:null==(r=A.find.attr(t,e))?void 0:r)},attrHooks:{type:{set:function(t,e){if(!v.radioValue&&"radio"===e&&F(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,r=0,i=e&&e.match($);if(i&&1===t.nodeType)for(;n=i[r++];)t.removeAttribute(n)}}),pe={set:function(t,e,n){return!1===e?A.removeAttr(t,n):t.setAttribute(n,n),n}},A.each(A.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=me[e]||A.find.attr;me[e]=function(t,e,r){var i,o,s=e.toLowerCase();return r||(o=me[s],me[s]=i,i=null!=n(t,e,r)?s:null,me[s]=o),i}}));var ve=/^(?:input|select|textarea|button)$/i,ge=/^(?:a|area)$/i;function ye(t){return(t.match($)||[]).join(" ")}function be(t){return t.getAttribute&&t.getAttribute("class")||""}function we(t){return Array.isArray(t)?t:"string"==typeof t&&t.match($)||[]}A.fn.extend({prop:function(t,e){return V(this,A.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each((function(){delete this[A.propFix[t]||t]}))}}),A.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&A.isXMLDoc(t)||(e=A.propFix[e]||e,i=A.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=A.find.attr(t,"tabindex");return e?parseInt(e,10):ve.test(t.nodeName)||ge.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),v.optSelected||(A.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),A.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){A.propFix[this.toLowerCase()]=this})),A.fn.extend({addClass:function(t){var e,n,r,i,o,s;return g(t)?this.each((function(e){A(this).addClass(t.call(this,e,be(this)))})):(e=we(t)).length?this.each((function(){if(r=be(this),n=1===this.nodeType&&" "+ye(r)+" "){for(o=0;o<e.length;o++)i=e[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");s=ye(n),r!==s&&this.setAttribute("class",s)}})):this},removeClass:function(t){var e,n,r,i,o,s;return g(t)?this.each((function(e){A(this).removeClass(t.call(this,e,be(this)))})):arguments.length?(e=we(t)).length?this.each((function(){if(r=be(this),n=1===this.nodeType&&" "+ye(r)+" "){for(o=0;o<e.length;o++)for(i=e[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");s=ye(n),r!==s&&this.setAttribute("class",s)}})):this:this.attr("class","")},toggleClass:function(t,e){var n,r,i,o,s=typeof t,a="string"===s||Array.isArray(t);return g(t)?this.each((function(n){A(this).toggleClass(t.call(this,n,be(this),e),e)})):"boolean"==typeof e&&a?e?this.addClass(t):this.removeClass(t):(n=we(t),this.each((function(){if(a)for(o=A(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==t&&"boolean"!==s||((r=be(this))&&Q.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":Q.get(this,"__className__")||""))})))},hasClass:function(t){var e,n,r=0;for(e=" "+t+" ";n=this[r++];)if(1===n.nodeType&&(" "+ye(be(n))+" ").indexOf(e)>-1)return!0;return!1}});var De=/\r/g;A.fn.extend({val:function(t){var e,n,r,i=this[0];return arguments.length?(r=g(t),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?t.call(this,n,A(this).val()):t)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=A.map(i,(function(t){return null==t?"":t+""}))),(e=A.valHooks[this.type]||A.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,i,"value")||(this.value=i))}))):i?(e=A.valHooks[i.type]||A.valHooks[i.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(De,""):null==n?"":n:void 0}}),A.extend({valHooks:{option:{get:function(t){var e=A.find.attr(t,"value");return null!=e?e:ye(A.text(t))}},select:{get:function(t){var e,n,r,i=t.options,o=t.selectedIndex,s="select-one"===t.type,a=s?null:[],u=s?o+1:i.length;for(r=o<0?u:s?o:0;r<u;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!F(n.parentNode,"optgroup"))){if(e=A(n).val(),s)return e;a.push(e)}return a},set:function(t,e){for(var n,r,i=t.options,o=A.makeArray(e),s=i.length;s--;)((r=i[s]).selected=A.inArray(A.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),A.each(["radio","checkbox"],(function(){A.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=A.inArray(A(t).val(),e)>-1}},v.checkOn||(A.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})})),v.focusin="onfocusin"in r;var xe=/^(?:focusinfocus|focusoutblur)$/,Ce=function(t){t.stopPropagation()};A.extend(A.event,{trigger:function(t,e,n,i){var o,s,a,u,l,c,d,h,p=[n||b],m=f.call(t,"type")?t.type:t,v=f.call(t,"namespace")?t.namespace.split("."):[];if(s=h=a=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!xe.test(m+A.event.triggered)&&(m.indexOf(".")>-1&&(v=m.split("."),m=v.shift(),v.sort()),l=m.indexOf(":")<0&&"on"+m,(t=t[A.expando]?t:new A.Event(m,"object"==typeof t&&t)).isTrigger=i?2:3,t.namespace=v.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:A.makeArray(e,[t]),d=A.event.special[m]||{},i||!d.trigger||!1!==d.trigger.apply(n,e))){if(!i&&!d.noBubble&&!y(n)){for(u=d.delegateType||m,xe.test(u+m)||(s=s.parentNode);s;s=s.parentNode)p.push(s),a=s;a===(n.ownerDocument||b)&&p.push(a.defaultView||a.parentWindow||r)}for(o=0;(s=p[o++])&&!t.isPropagationStopped();)h=s,t.type=o>1?u:d.bindType||m,(c=(Q.get(s,"events")||Object.create(null))[t.type]&&Q.get(s,"handle"))&&c.apply(s,e),(c=l&&s[l])&&c.apply&&X(s)&&(t.result=c.apply(s,e),!1===t.result&&t.preventDefault());return t.type=m,i||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(p.pop(),e)||!X(n)||l&&g(n[m])&&!y(n)&&((a=n[l])&&(n[l]=null),A.event.triggered=m,t.isPropagationStopped()&&h.addEventListener(m,Ce),n[m](),t.isPropagationStopped()&&h.removeEventListener(m,Ce),A.event.triggered=void 0,a&&(n[l]=a)),t.result}},simulate:function(t,e,n){var r=A.extend(new A.Event,n,{type:t,isSimulated:!0});A.event.trigger(r,null,e)}}),A.fn.extend({trigger:function(t,e){return this.each((function(){A.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];if(n)return A.event.trigger(t,e,n,!0)}}),v.focusin||A.each({focus:"focusin",blur:"focusout"},(function(t,e){var n=function(t){A.event.simulate(e,t.target,A.event.fix(t))};A.event.special[e]={setup:function(){var r=this.ownerDocument||this.document||this,i=Q.access(r,e);i||r.addEventListener(t,n,!0),Q.access(r,e,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=Q.access(r,e)-1;i?Q.access(r,e,i):(r.removeEventListener(t,n,!0),Q.remove(r,e))}}}));var Ae=r.location,Ee={guid:Date.now()},Ne=/\?/;A.parseXML=function(t){var e,n;if(!t||"string"!=typeof t)return null;try{e=(new r.DOMParser).parseFromString(t,"text/xml")}catch(t){}return n=e&&e.getElementsByTagName("parsererror")[0],e&&!n||A.error("Invalid XML: "+(n?A.map(n.childNodes,(function(t){return t.textContent})).join("\n"):t)),e};var ke=/\[\]$/,Se=/\r?\n/g,Te=/^(?:submit|button|image|reset|file)$/i,Fe=/^(?:input|select|textarea|keygen)/i;function _e(t,e,n,r){var i;if(Array.isArray(e))A.each(e,(function(e,i){n||ke.test(t)?r(t,i):_e(t+"["+("object"==typeof i&&null!=i?e:"")+"]",i,n,r)}));else if(n||"object"!==x(e))r(t,e);else for(i in e)_e(t+"["+i+"]",e[i],n,r)}A.param=function(t,e){var n,r=[],i=function(t,e){var n=g(e)?e():e;r[r.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!A.isPlainObject(t))A.each(t,(function(){i(this.name,this.value)}));else for(n in t)_e(n,t[n],e,i);return r.join("&")},A.fn.extend({serialize:function(){return A.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=A.prop(this,"elements");return t?A.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!A(this).is(":disabled")&&Fe.test(this.nodeName)&&!Te.test(t)&&(this.checked||!vt.test(t))})).map((function(t,e){var n=A(this).val();return null==n?null:Array.isArray(n)?A.map(n,(function(t){return{name:e.name,value:t.replace(Se,"\r\n")}})):{name:e.name,value:n.replace(Se,"\r\n")}})).get()}});var Pe=/%20/g,Be=/#.*$/,Ie=/([?&])_=[^&]*/,Oe=/^(.*?):[ \t]*([^\r\n]*)$/gm,Le=/^(?:GET|HEAD)$/,Me=/^\/\//,$e={},je={},Re="*/".concat("*"),He=b.createElement("a");function Ue(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var r,i=0,o=e.toLowerCase().match($)||[];if(g(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(t[r]=t[r]||[]).unshift(n)):(t[r]=t[r]||[]).push(n)}}function qe(t,e,n,r){var i={},o=t===je;function s(a){var u;return i[a]=!0,A.each(t[a]||[],(function(t,a){var l=a(e,n,r);return"string"!=typeof l||o||i[l]?o?!(u=l):void 0:(e.dataTypes.unshift(l),s(l),!1)})),u}return s(e.dataTypes[0])||!i["*"]&&s("*")}function ze(t,e){var n,r,i=A.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r||(r={}))[n]=e[n]);return r&&A.extend(!0,t,r),t}He.href=Ae.href,A.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ae.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ae.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Re,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":A.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?ze(ze(t,A.ajaxSettings),e):ze(A.ajaxSettings,t)},ajaxPrefilter:Ue($e),ajaxTransport:Ue(je),ajax:function(t,e){"object"==typeof t&&(e=t,t=void 0);var n,i,o,s,a,u,l,c,d,h,f=A.ajaxSetup({},e=e||{}),p=f.context||f,m=f.context&&(p.nodeType||p.jquery)?A(p):A.event,v=A.Deferred(),g=A.Callbacks("once memory"),y=f.statusCode||{},w={},D={},x="canceled",C={readyState:0,getResponseHeader:function(t){var e;if(l){if(!s)for(s={};e=Oe.exec(o);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(t,e){return null==l&&(t=D[t.toLowerCase()]=D[t.toLowerCase()]||t,w[t]=e),this},overrideMimeType:function(t){return null==l&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(l)C.always(t[C.status]);else for(e in t)y[e]=[y[e],t[e]];return this},abort:function(t){var e=t||x;return n&&n.abort(e),E(0,e),this}};if(v.promise(C),f.url=((t||f.url||Ae.href)+"").replace(Me,Ae.protocol+"//"),f.type=e.method||e.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match($)||[""],null==f.crossDomain){u=b.createElement("a");try{u.href=f.url,u.href=u.href,f.crossDomain=He.protocol+"//"+He.host!=u.protocol+"//"+u.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=A.param(f.data,f.traditional)),qe($e,f,e,C),l)return C;for(d in(c=A.event&&f.global)&&0==A.active++&&A.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Le.test(f.type),i=f.url.replace(Be,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(Pe,"+")):(h=f.url.slice(i.length),f.data&&(f.processData||"string"==typeof f.data)&&(i+=(Ne.test(i)?"&":"?")+f.data,delete f.data),!1===f.cache&&(i=i.replace(Ie,"$1"),h=(Ne.test(i)?"&":"?")+"_="+Ee.guid+++h),f.url=i+h),f.ifModified&&(A.lastModified[i]&&C.setRequestHeader("If-Modified-Since",A.lastModified[i]),A.etag[i]&&C.setRequestHeader("If-None-Match",A.etag[i])),(f.data&&f.hasContent&&!1!==f.contentType||e.contentType)&&C.setRequestHeader("Content-Type",f.contentType),C.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Re+"; q=0.01":""):f.accepts["*"]),f.headers)C.setRequestHeader(d,f.headers[d]);if(f.beforeSend&&(!1===f.beforeSend.call(p,C,f)||l))return C.abort();if(x="abort",g.add(f.complete),C.done(f.success),C.fail(f.error),n=qe(je,f,e,C)){if(C.readyState=1,c&&m.trigger("ajaxSend",[C,f]),l)return C;f.async&&f.timeout>0&&(a=r.setTimeout((function(){C.abort("timeout")}),f.timeout));try{l=!1,n.send(w,E)}catch(t){if(l)throw t;E(-1,t)}}else E(-1,"No Transport");function E(t,e,s,u){var d,h,b,w,D,x=e;l||(l=!0,a&&r.clearTimeout(a),n=void 0,o=u||"",C.readyState=t>0?4:0,d=t>=200&&t<300||304===t,s&&(w=function(t,e,n){for(var r,i,o,s,a=t.contents,u=t.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||t.converters[i+" "+u[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}(f,C,s)),!d&&A.inArray("script",f.dataTypes)>-1&&A.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),w=function(t,e,n,r){var i,o,s,a,u,l={},c=t.dataTypes.slice();if(c[1])for(s in t.converters)l[s.toLowerCase()]=t.converters[s];for(o=c.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!u&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=l[u+" "+o]||l["* "+o]))for(i in l)if((a=i.split(" "))[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+u+" to "+o}}}return{state:"success",data:e}}(f,w,C,d),d?(f.ifModified&&((D=C.getResponseHeader("Last-Modified"))&&(A.lastModified[i]=D),(D=C.getResponseHeader("etag"))&&(A.etag[i]=D)),204===t||"HEAD"===f.type?x="nocontent":304===t?x="notmodified":(x=w.state,h=w.data,d=!(b=w.error))):(b=x,!t&&x||(x="error",t<0&&(t=0))),C.status=t,C.statusText=(e||x)+"",d?v.resolveWith(p,[h,x,C]):v.rejectWith(p,[C,x,b]),C.statusCode(y),y=void 0,c&&m.trigger(d?"ajaxSuccess":"ajaxError",[C,f,d?h:b]),g.fireWith(p,[C,x]),c&&(m.trigger("ajaxComplete",[C,f]),--A.active||A.event.trigger("ajaxStop")))}return C},getJSON:function(t,e,n){return A.get(t,e,n,"json")},getScript:function(t,e){return A.get(t,void 0,e,"script")}}),A.each(["get","post"],(function(t,e){A[e]=function(t,n,r,i){return g(n)&&(i=i||r,r=n,n=void 0),A.ajax(A.extend({url:t,type:e,dataType:i,data:n,success:r},A.isPlainObject(t)&&t))}})),A.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),A._evalUrl=function(t,e,n){return A.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){A.globalEval(t,e,n)}})},A.fn.extend({wrapAll:function(t){var e;return this[0]&&(g(t)&&(t=t.call(this[0])),e=A(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return g(t)?this.each((function(e){A(this).wrapInner(t.call(this,e))})):this.each((function(){var e=A(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=g(t);return this.each((function(n){A(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){A(this).replaceWith(this.childNodes)})),this}}),A.expr.pseudos.hidden=function(t){return!A.expr.pseudos.visible(t)},A.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},A.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(t){}};var Ve={0:200,1223:204},We=A.ajaxSettings.xhr();v.cors=!!We&&"withCredentials"in We,v.ajax=We=!!We,A.ajaxTransport((function(t){var e,n;if(v.cors||We&&!t.crossDomain)return{send:function(i,o){var s,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];for(s in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);e=function(t){return function(){e&&(e=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Ve[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=e(),n=a.onerror=a.ontimeout=e("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&r.setTimeout((function(){e&&n()}))},e=e("abort");try{a.send(t.hasContent&&t.data||null)}catch(t){if(e)throw t}},abort:function(){e&&e()}}})),A.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),A.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return A.globalEval(t),t}}}),A.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),A.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(r,i){e=A("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&i("error"===t.type?404:200,t.type)}),b.head.appendChild(e[0])},abort:function(){n&&n()}}}));var Ge,Ye=[],Ke=/(=)\?(?=&|$)|\?\?/;A.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ye.pop()||A.expando+"_"+Ee.guid++;return this[t]=!0,t}}),A.ajaxPrefilter("json jsonp",(function(t,e,n){var i,o,s,a=!1!==t.jsonp&&(Ke.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ke.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=g(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(Ke,"$1"+i):!1!==t.jsonp&&(t.url+=(Ne.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return s||A.error(i+" was not called"),s[0]},t.dataTypes[0]="json",o=r[i],r[i]=function(){s=arguments},n.always((function(){void 0===o?A(r).removeProp(i):r[i]=o,t[i]&&(t.jsonpCallback=e.jsonpCallback,Ye.push(i)),s&&g(o)&&o(s[0]),s=o=void 0})),"script"})),v.createHTMLDocument=((Ge=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ge.childNodes.length),A.parseHTML=function(t,e,n){return"string"!=typeof t?[]:("boolean"==typeof e&&(n=e,e=!1),e||(v.createHTMLDocument?((r=(e=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,e.head.appendChild(r)):e=b),o=!n&&[],(i=_.exec(t))?[e.createElement(i[1])]:(i=Ct([t],e,o),o&&o.length&&A(o).remove(),A.merge([],i.childNodes)));var r,i,o},A.fn.load=function(t,e,n){var r,i,o,s=this,a=t.indexOf(" ");return a>-1&&(r=ye(t.slice(a)),t=t.slice(0,a)),g(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),s.length>0&&A.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done((function(t){o=arguments,s.html(r?A("<div>").append(A.parseHTML(t)).find(r):t)})).always(n&&function(t,e){s.each((function(){n.apply(this,o||[t.responseText,e,t])}))}),this},A.expr.pseudos.animated=function(t){return A.grep(A.timers,(function(e){return t===e.elem})).length},A.offset={setOffset:function(t,e,n){var r,i,o,s,a,u,l=A.css(t,"position"),c=A(t),d={};"static"===l&&(t.style.position="relative"),a=c.offset(),o=A.css(t,"top"),u=A.css(t,"left"),("absolute"===l||"fixed"===l)&&(o+u).indexOf("auto")>-1?(s=(r=c.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(u)||0),g(e)&&(e=e.call(t,n,A.extend({},a))),null!=e.top&&(d.top=e.top-a.top+s),null!=e.left&&(d.left=e.left-a.left+i),"using"in e?e.using.call(t,d):c.css(d)}},A.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){A.offset.setOffset(this,t,e)}));var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,r=this[0],i={top:0,left:0};if("fixed"===A.css(r,"position"))e=r.getBoundingClientRect();else{for(e=this.offset(),n=r.ownerDocument,t=r.offsetParent||n.documentElement;t&&(t===n.body||t===n.documentElement)&&"static"===A.css(t,"position");)t=t.parentNode;t&&t!==r&&1===t.nodeType&&((i=A(t).offset()).top+=A.css(t,"borderTopWidth",!0),i.left+=A.css(t,"borderLeftWidth",!0))}return{top:e.top-i.top-A.css(r,"marginTop",!0),left:e.left-i.left-A.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===A.css(t,"position");)t=t.offsetParent;return t||st}))}}),A.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;A.fn[t]=function(r){return V(this,(function(t,r,i){var o;if(y(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===i)return o?o[e]:t[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):t[r]=i}),t,r,arguments.length)}})),A.each(["top","left"],(function(t,e){A.cssHooks[e]=Gt(v.pixelPosition,(function(t,n){if(n)return n=Wt(t,e),Rt.test(n)?A(t).position()[e]+"px":n}))})),A.each({Height:"height",Width:"width"},(function(t,e){A.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,r){A.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return V(this,(function(e,n,i){var o;return y(e)?0===r.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===i?A.css(e,n,a):A.style(e,n,i,a)}),e,s?i:void 0,s)}}))})),A.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){A.fn[e]=function(t){return this.on(e,t)}})),A.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),A.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){A.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}));var Xe=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;A.proxy=function(t,e){var n,r,i;if("string"==typeof e&&(n=t[e],e=t,t=n),g(t))return r=a.call(arguments,2),i=function(){return t.apply(e||this,r.concat(a.call(arguments)))},i.guid=t.guid=t.guid||A.guid++,i},A.holdReady=function(t){t?A.readyWait++:A.ready(!0)},A.isArray=Array.isArray,A.parseJSON=JSON.parse,A.nodeName=F,A.isFunction=g,A.isWindow=y,A.camelCase=K,A.type=x,A.now=Date.now,A.isNumeric=function(t){var e=A.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},A.trim=function(t){return null==t?"":(t+"").replace(Xe,"$1")},void 0===(n=function(){return A}.apply(e,[]))||(t.exports=n);var Je=r.jQuery,Qe=r.$;return A.noConflict=function(t){return r.$===A&&(r.$=Qe),t&&r.jQuery===A&&(r.jQuery=Je),A},void 0===i&&(r.jQuery=r.$=A),A}))},542:(t,e,n)=>{"use strict";var r=n(1642);t.exports=function(t,e,n){var i=[],o=!1,s=!1;function a(){if(o)throw new Error("Nested m.redraw.sync() call");o=!0;for(var e=0;e<i.length;e+=2)try{t(i[e],r(i[e+1]),u)}catch(t){n.error(t)}o=!1}function u(){s||(s=!0,e((function(){s=!1,a()})))}return u.sync=a,{mount:function(e,n){if(null!=n&&null==n.view&&"function"!=typeof n)throw new TypeError("m.mount(element, component) expects a component, not a vnode");var o=i.indexOf(e);o>=0&&(i.splice(o,2),t(e,[],u)),null!=n&&(i.push(e,n),t(e,r(n),u))},redraw:u}}},3600:(t,e,n)=>{"use strict";var r=n(1642),i=n(5334),o=n(187),s=n(3690),a=n(1490),u=n(5211),l=n(6797),c={};t.exports=function(t,e){var n;function d(e,r,i){if(e=s(e,r),null!=n){n();var o=i?i.state:null,a=i?i.title:null;i&&i.replace?t.history.replaceState(o,a,y.prefix+e):t.history.pushState(o,a,y.prefix+e)}else t.location.href=y.prefix+e}var h,f,p,m,v=c,g=y.SKIP={};function y(i,s,b){if(null==i)throw new Error("Ensure the DOM element that was passed to `m.route` is not undefined");var w,D=0,x=Object.keys(b).map((function(t){if("/"!==t[0])throw new SyntaxError("Routes must start with a `/`");if(/:([^\/\.-]+)(\.{3})?:/.test(t))throw new SyntaxError("Route parameter names must be separated with either `/`, `.`, or `-`");return{route:t,component:b[t],check:u(t)}})),C="function"==typeof setImmediate?setImmediate:setTimeout,A=o.resolve(),E=!1;if(n=null,null!=s){var N=a(s);if(!x.some((function(t){return t.check(N)})))throw new ReferenceError("Default route doesn't match any known routes")}function k(){E=!1;var n=t.location.hash;"#"!==y.prefix[0]&&(n=t.location.search+n,"?"!==y.prefix[0]&&"/"!==(n=t.location.pathname+n)[0]&&(n="/"+n));var r=n.concat().replace(/(?:%[a-f89][a-f0-9])+/gim,decodeURIComponent).slice(y.prefix.length),i=a(r);function o(){if(r===s)throw new Error("Could not resolve default route "+s);d(s,null,{replace:!0})}l(i.params,t.history.state),function t(n){for(;n<x.length;n++)if(x[n].check(i)){var s=x[n].component,a=x[n].route,u=s,l=m=function(o){if(l===m){if(o===g)return t(n+1);h=null==o||"function"!=typeof o.view&&"function"!=typeof o?"div":o,f=i.params,p=r,m=null,v=s.render?s:null,2===D?e.redraw():(D=2,e.redraw.sync())}};return void(s.view||"function"==typeof s?(s={},l(u)):s.onmatch?A.then((function(){return s.onmatch(i.params,r,a)})).then(l,o):l("div"))}o()}(0)}return n=function(){E||(E=!0,C(k))},"function"==typeof t.history.pushState?(w=function(){t.removeEventListener("popstate",n,!1)},t.addEventListener("popstate",n,!1)):"#"===y.prefix[0]&&(n=null,w=function(){t.removeEventListener("hashchange",k,!1)},t.addEventListener("hashchange",k,!1)),e.mount(i,{onbeforeupdate:function(){return!(!(D=D?2:1)||c===v)},oncreate:k,onremove:w,view:function(){if(D&&c!==v){var t=[r(h,f.key,f)];return v&&(t=v.render(t[0])),t}}})}return y.set=function(t,e,n){null!=m&&((n=n||{}).replace=!0),m=null,d(t,e,n)},y.get=function(){return p},y.prefix="#!",y.Link={view:function(t){var e,n,r=t.attrs.options,o={};l(o,t.attrs),o.selector=o.options=o.key=o.oninit=o.oncreate=o.onbeforeupdate=o.onupdate=o.onbeforeremove=o.onremove=null;var s=i(t.attrs.selector||"a",o,t.children);return(s.attrs.disabled=Boolean(s.attrs.disabled))?(s.attrs.href=null,s.attrs["aria-disabled"]="true",s.attrs.onclick=null):(e=s.attrs.onclick,n=s.attrs.href,s.attrs.href=y.prefix+n,s.attrs.onclick=function(t){var i;"function"==typeof e?i=e.call(t.currentTarget,t):null==e||"object"!=typeof e||"function"==typeof e.handleEvent&&e.handleEvent(t),!1===i||t.defaultPrevented||0!==t.button&&0!==t.which&&1!==t.which||t.currentTarget.target&&"_self"!==t.currentTarget.target||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey||(t.preventDefault(),t.redraw=!1,y.set(n,null,r))}),s}},y.param=function(t){return f&&null!=t?f[t]:f},y}},589:(t,e,n)=>{"use strict";var r=n(5334);r.trust=n(4031),r.fragment=n(2505),t.exports=r},7945:(t,e,n)=>{"use strict";var r=n(589),i=n(8450),o=n(570),s=function(){return r.apply(this,arguments)};s.m=r,s.trust=r.trust,s.fragment=r.fragment,s.mount=o.mount,s.route=n(2092),s.render=n(2031),s.redraw=o.redraw,s.request=i.request,s.jsonp=i.jsonp,s.parseQueryString=n(5784),s.buildQueryString=n(7477),s.parsePathname=n(1490),s.buildPathname=n(3690),s.vnode=n(1642),s.PromisePolyfill=n(7068),t.exports=s},570:(t,e,n)=>{"use strict";var r=n(2031);t.exports=n(542)(r,requestAnimationFrame,console)},6797:t=>{"use strict";t.exports=Object.assign||function(t,e){e&&Object.keys(e).forEach((function(n){t[n]=e[n]}))}},3690:(t,e,n)=>{"use strict";var r=n(7477),i=n(6797);t.exports=function(t,e){if(/:([^\/\.-]+)(\.{3})?:/.test(t))throw new SyntaxError("Template parameter names *must* be separated");if(null==e)return t;var n=t.indexOf("?"),o=t.indexOf("#"),s=o<0?t.length:o,a=n<0?s:n,u=t.slice(0,a),l={};i(l,e);var c=u.replace(/:([^\/\.-]+)(\.{3})?/g,(function(t,n,r){return delete l[n],null==e[n]?t:r?e[n]:encodeURIComponent(String(e[n]))})),d=c.indexOf("?"),h=c.indexOf("#"),f=h<0?c.length:h,p=d<0?f:d,m=c.slice(0,p);n>=0&&(m+=t.slice(n,s)),d>=0&&(m+=(n<0?"?":"&")+c.slice(d,f));var v=r(l);return v&&(m+=(n<0&&d<0?"?":"&")+v),o>=0&&(m+=t.slice(o)),h>=0&&(m+=(o<0?"":"&")+c.slice(h)),m}},5211:(t,e,n)=>{"use strict";var r=n(1490);t.exports=function(t){var e=r(t),n=Object.keys(e.params),i=[],o=new RegExp("^"+e.path.replace(/:([^\/.-]+)(\.{3}|\.(?!\.)|-)?|[\\^$*+.()|\[\]{}]/g,(function(t,e,n){return null==e?"\\"+t:(i.push({k:e,r:"..."===n}),"..."===n?"(.*)":"."===n?"([^/]+)\\.":"([^/]+)"+(n||""))}))+"$");return function(t){for(var r=0;r<n.length;r++)if(e.params[n[r]]!==t.params[n[r]])return!1;if(!i.length)return o.test(t.path);var s=o.exec(t.path);if(null==s)return!1;for(r=0;r<i.length;r++)t.params[i[r].k]=i[r].r?s[r+1]:decodeURIComponent(s[r+1]);return!0}}},1490:(t,e,n)=>{"use strict";var r=n(5784);t.exports=function(t){var e=t.indexOf("?"),n=t.indexOf("#"),i=n<0?t.length:n,o=e<0?i:e,s=t.slice(0,o).replace(/\/{2,}/g,"/");return s?("/"!==s[0]&&(s="/"+s),s.length>1&&"/"===s[s.length-1]&&(s=s.slice(0,-1))):s="/",{path:s,params:e<0?{}:r(t.slice(e+1,i))}}},7068:t=>{"use strict";var e=function t(e){if(!(this instanceof t))throw new Error("Promise must be called with `new`");if("function"!=typeof e)throw new TypeError("executor must be a function");var n=this,r=[],i=[],o=l(r,!0),s=l(i,!1),a=n._instance={resolvers:r,rejectors:i},u="function"==typeof setImmediate?setImmediate:setTimeout;function l(t,e){return function o(l){var d;try{if(!e||null==l||"object"!=typeof l&&"function"!=typeof l||"function"!=typeof(d=l.then))u((function(){e||0!==t.length||console.error("Possible unhandled promise rejection:",l);for(var n=0;n<t.length;n++)t[n](l);r.length=0,i.length=0,a.state=e,a.retry=function(){o(l)}}));else{if(l===n)throw new TypeError("Promise can't be resolved w/ itself");c(d.bind(l))}}catch(t){s(t)}}}function c(t){var e=0;function n(t){return function(n){e++>0||t(n)}}var r=n(s);try{t(n(o),r)}catch(t){r(t)}}c(e)};e.prototype.then=function(t,n){var r,i,o=this._instance;function s(t,e,n,s){e.push((function(e){if("function"!=typeof t)n(e);else try{r(t(e))}catch(t){i&&i(t)}})),"function"==typeof o.retry&&s===o.state&&o.retry()}var a=new e((function(t,e){r=t,i=e}));return s(t,o.resolvers,r,!0),s(n,o.rejectors,i,!1),a},e.prototype.catch=function(t){return this.then(null,t)},e.prototype.finally=function(t){return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))},e.resolve=function(t){return t instanceof e?t:new e((function(e){e(t)}))},e.reject=function(t){return new e((function(e,n){n(t)}))},e.all=function(t){return new e((function(e,n){var r=t.length,i=0,o=[];if(0===t.length)e([]);else for(var s=0;s<t.length;s++)!function(s){function a(t){i++,o[s]=t,i===r&&e(o)}null==t[s]||"object"!=typeof t[s]&&"function"!=typeof t[s]||"function"!=typeof t[s].then?a(t[s]):t[s].then(a,n)}(s)}))},e.race=function(t){return new e((function(e,n){for(var r=0;r<t.length;r++)t[r].then(e,n)}))},t.exports=e},187:(t,e,n)=>{"use strict";var r=n(7068);"undefined"!=typeof window?(void 0===window.Promise?window.Promise=r:window.Promise.prototype.finally||(window.Promise.prototype.finally=r.prototype.finally),t.exports=window.Promise):void 0!==n.g?(void 0===n.g.Promise?n.g.Promise=r:n.g.Promise.prototype.finally||(n.g.Promise.prototype.finally=r.prototype.finally),t.exports=n.g.Promise):t.exports=r},7477:t=>{"use strict";t.exports=function(t){if("[object Object]"!==Object.prototype.toString.call(t))return"";var e=[];for(var n in t)r(n,t[n]);return e.join("&");function r(t,n){if(Array.isArray(n))for(var i=0;i<n.length;i++)r(t+"["+i+"]",n[i]);else if("[object Object]"===Object.prototype.toString.call(n))for(var i in n)r(t+"["+i+"]",n[i]);else e.push(encodeURIComponent(t)+(null!=n&&""!==n?"="+encodeURIComponent(n):""))}}},5784:t=>{"use strict";t.exports=function(t){if(""===t||null==t)return{};"?"===t.charAt(0)&&(t=t.slice(1));for(var e=t.split("&"),n={},r={},i=0;i<e.length;i++){var o=e[i].split("="),s=decodeURIComponent(o[0]),a=2===o.length?decodeURIComponent(o[1]):"";"true"===a?a=!0:"false"===a&&(a=!1);var u=s.split(/\]\[?|\[/),l=r;s.indexOf("[")>-1&&u.pop();for(var c=0;c<u.length;c++){var d=u[c],h=u[c+1],f=""==h||!isNaN(parseInt(h,10));if(""===d)null==n[s=u.slice(0,c).join()]&&(n[s]=Array.isArray(l)?l.length:0),d=n[s]++;else if("__proto__"===d)break;if(c===u.length-1)l[d]=a;else{var p=Object.getOwnPropertyDescriptor(l,d);null!=p&&(p=p.value),null==p&&(l[d]=p=f?[]:{}),l=p}}}return r}},2031:(t,e,n)=>{"use strict";t.exports=n(4615)(window)},2505:(t,e,n)=>{"use strict";var r=n(1642),i=n(8239);t.exports=function(){var t=i.apply(0,arguments);return t.tag="[",t.children=r.normalizeChildren(t.children),t}},5334:(t,e,n)=>{"use strict";var r=n(1642),i=n(8239),o=/(?:(^|#|\.)([^#\.\[\]]+))|(\[(.+?)(?:\s*=\s*("|'|)((?:\\["'\]]|.)*?)\5)?\])/g,s={},a={}.hasOwnProperty;function u(t){for(var e in t)if(a.call(t,e))return!1;return!0}function l(t){for(var e,n="div",r=[],i={};e=o.exec(t);){var a=e[1],u=e[2];if(""===a&&""!==u)n=u;else if("#"===a)i.id=u;else if("."===a)r.push(u);else if("["===e[3][0]){var l=e[6];l&&(l=l.replace(/\\(["'])/g,"$1").replace(/\\\\/g,"\\")),"class"===e[4]?r.push(l):i[e[4]]=""===l?l:l||!0}}return r.length>0&&(i.className=r.join(" ")),s[t]={tag:n,attrs:i}}function c(t,e){var n=e.attrs,i=r.normalizeChildren(e.children),o=a.call(n,"class"),s=o?n.class:n.className;if(e.tag=t.tag,e.attrs=null,e.children=void 0,!u(t.attrs)&&!u(n)){var l={};for(var c in n)a.call(n,c)&&(l[c]=n[c]);n=l}for(var c in t.attrs)a.call(t.attrs,c)&&"className"!==c&&!a.call(n,c)&&(n[c]=t.attrs[c]);for(var c in null==s&&null==t.attrs.className||(n.className=null!=s?null!=t.attrs.className?String(t.attrs.className)+" "+String(s):s:null!=t.attrs.className?t.attrs.className:null),o&&(n.class=null),n)if(a.call(n,c)&&"key"!==c){e.attrs=n;break}return Array.isArray(i)&&1===i.length&&null!=i[0]&&"#"===i[0].tag?e.text=i[0].children:e.children=i,e}t.exports=function(t){if(null==t||"string"!=typeof t&&"function"!=typeof t&&"function"!=typeof t.view)throw Error("The selector must be either a string or a component.");var e=i.apply(1,arguments);return"string"==typeof t&&(e.children=r.normalizeChildren(e.children),"["!==t)?c(s[t]||l(t),e):(e.tag=t,e)}},8239:(t,e,n)=>{"use strict";var r=n(1642);t.exports=function(){var t,e=arguments[this],n=this+1;if(null==e?e={}:("object"!=typeof e||null!=e.tag||Array.isArray(e))&&(e={},n=this),arguments.length===n+1)t=arguments[n],Array.isArray(t)||(t=[t]);else for(t=[];n<arguments.length;)t.push(arguments[n++]);return r("",e.key,e,t)}},4615:(t,e,n)=>{"use strict";var r=n(1642);t.exports=function(t){var e,n=t&&t.document,i={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"};function o(t){return t.attrs&&t.attrs.xmlns||i[t.tag]}function s(t,e){if(t.state!==e)throw new Error("`vnode.state` must not be modified")}function a(t){var e=t.state;try{return this.apply(e,arguments)}finally{s(t,e)}}function u(){try{return n.activeElement}catch(t){return null}}function l(t,e,n,r,i,o,s){for(var a=n;a<r;a++){var u=e[a];null!=u&&c(t,u,i,s,o)}}function c(t,e,i,s,u){var d=e.tag;if("string"==typeof d)switch(e.state={},null!=e.attrs&&M(e.attrs,e,i),d){case"#":!function(t,e,r){e.dom=n.createTextNode(e.children),w(t,e.dom,r)}(t,e,u);break;case"<":h(t,e,s,u);break;case"[":!function(t,e,r,i,o){var s=n.createDocumentFragment();if(null!=e.children){var a=e.children;l(s,a,0,a.length,r,null,i)}e.dom=s.firstChild,e.domSize=s.childNodes.length,w(t,s,o)}(t,e,i,s,u);break;default:!function(t,e,i,s,a){var u=e.tag,c=e.attrs,d=c&&c.is,h=(s=o(e)||s)?d?n.createElementNS(s,u,{is:d}):n.createElementNS(s,u):d?n.createElement(u,{is:d}):n.createElement(u);if(e.dom=h,null!=c&&function(t,e,n){for(var r in e)k(t,r,null,e[r],n)}(e,c,s),w(t,h,a),!D(e)&&(null!=e.text&&(""!==e.text?h.textContent=e.text:e.children=[r("#",void 0,void 0,e.text,void 0,void 0)]),null!=e.children)){var f=e.children;l(h,f,0,f.length,i,null,s),"select"===e.tag&&null!=c&&function(t,e){if("value"in e)if(null===e.value)-1!==t.dom.selectedIndex&&(t.dom.value=null);else{var n=""+e.value;t.dom.value===n&&-1!==t.dom.selectedIndex||(t.dom.value=n)}"selectedIndex"in e&&k(t,"selectedIndex",null,e.selectedIndex,void 0)}(e,c)}}(t,e,i,s,u)}else!function(t,e,n,i,o){(function(t,e){var n;if("function"==typeof t.tag.view){if(t.state=Object.create(t.tag),null!=(n=t.state.view).$$reentrantLock$$)return;n.$$reentrantLock$$=!0}else{if(t.state=void 0,null!=(n=t.tag).$$reentrantLock$$)return;n.$$reentrantLock$$=!0,t.state=null!=t.tag.prototype&&"function"==typeof t.tag.prototype.view?new t.tag(t):t.tag(t)}if(M(t.state,t,e),null!=t.attrs&&M(t.attrs,t,e),t.instance=r.normalize(a.call(t.state.view,t)),t.instance===t)throw Error("A view cannot return the vnode it received as argument");n.$$reentrantLock$$=null})(e,n),null!=e.instance?(c(t,e.instance,n,i,o),e.dom=e.instance.dom,e.domSize=null!=e.dom?e.instance.domSize:0):e.domSize=0}(t,e,i,s,u)}var d={caption:"table",thead:"table",tbody:"table",tfoot:"table",tr:"tbody",th:"tr",td:"tr",colgroup:"table",col:"colgroup"};function h(t,e,r,i){var o=e.children.match(/^\s*?<(\w+)/im)||[],s=n.createElement(d[o[1]]||"div");"http://www.w3.org/2000/svg"===r?(s.innerHTML='<svg xmlns="http://www.w3.org/2000/svg">'+e.children+"</svg>",s=s.firstChild):s.innerHTML=e.children,e.dom=s.firstChild,e.domSize=s.childNodes.length,e.instance=[];for(var a,u=n.createDocumentFragment();a=s.firstChild;)e.instance.push(a),u.appendChild(a);w(t,u,i)}function f(t,e,n,r,i,o){if(e!==n&&(null!=e||null!=n))if(null==e||0===e.length)l(t,n,0,n.length,r,i,o);else if(null==n||0===n.length)x(t,e,0,e.length);else{var s=null!=e[0]&&null!=e[0].key,a=null!=n[0]&&null!=n[0].key,u=0,d=0;if(!s)for(;d<e.length&&null==e[d];)d++;if(!a)for(;u<n.length&&null==n[u];)u++;if(null===a&&null==s)return;if(s!==a)x(t,e,d,e.length),l(t,n,u,n.length,r,i,o);else if(a){for(var h,f,b,w,D,A=e.length-1,E=n.length-1;A>=d&&E>=u&&(b=e[A],w=n[E],b.key===w.key);)b!==w&&p(t,b,w,r,i,o),null!=w.dom&&(i=w.dom),A--,E--;for(;A>=d&&E>=u&&(h=e[d],f=n[u],h.key===f.key);)d++,u++,h!==f&&p(t,h,f,r,g(e,d,i),o);for(;A>=d&&E>=u&&u!==E&&h.key===w.key&&b.key===f.key;)y(t,b,D=g(e,d,i)),b!==f&&p(t,b,f,r,D,o),++u<=--E&&y(t,h,i),h!==w&&p(t,h,w,r,i,o),null!=w.dom&&(i=w.dom),d++,b=e[--A],w=n[E],h=e[d],f=n[u];for(;A>=d&&E>=u&&b.key===w.key;)b!==w&&p(t,b,w,r,i,o),null!=w.dom&&(i=w.dom),E--,b=e[--A],w=n[E];if(u>E)x(t,e,d,A+1);else if(d>A)l(t,n,u,E+1,r,i,o);else{var N,k,S=i,T=E-u+1,F=new Array(T),_=0,P=0,B=2147483647,I=0;for(P=0;P<T;P++)F[P]=-1;for(P=E;P>=u;P--){null==N&&(N=m(e,d,A+1));var O=N[(w=n[P]).key];null!=O&&(B=O<B?O:-1,F[P-u]=O,b=e[O],e[O]=null,b!==w&&p(t,b,w,r,i,o),null!=w.dom&&(i=w.dom),I++)}if(i=S,I!==A-d+1&&x(t,e,d,A+1),0===I)l(t,n,u,E+1,r,i,o);else if(-1===B)for(k=function(t){var e=[0],n=0,r=0,i=0,o=v.length=t.length;for(i=0;i<o;i++)v[i]=t[i];for(i=0;i<o;++i)if(-1!==t[i]){var s=e[e.length-1];if(t[s]<t[i])v[i]=s,e.push(i);else{for(n=0,r=e.length-1;n<r;){var a=(n>>>1)+(r>>>1)+(n&r&1);t[e[a]]<t[i]?n=a+1:r=a}t[i]<t[e[n]]&&(n>0&&(v[i]=e[n-1]),e[n]=i)}}for(r=e[(n=e.length)-1];n-- >0;)e[n]=r,r=v[r];return v.length=0,e}(F),_=k.length-1,P=E;P>=u;P--)f=n[P],-1===F[P-u]?c(t,f,r,o,i):k[_]===P-u?_--:y(t,f,i),null!=f.dom&&(i=n[P].dom);else for(P=E;P>=u;P--)f=n[P],-1===F[P-u]&&c(t,f,r,o,i),null!=f.dom&&(i=n[P].dom)}}else{var L=e.length<n.length?e.length:n.length;for(u=u<d?u:d;u<L;u++)(h=e[u])===(f=n[u])||null==h&&null==f||(null==h?c(t,f,r,o,g(e,u+1,i)):null==f?C(t,h):p(t,h,f,r,g(e,u+1,i),o));e.length>L&&x(t,e,u,e.length),n.length>L&&l(t,n,u,n.length,r,i,o)}}}function p(t,e,n,i,s,u){var l=e.tag;if(l===n.tag){if(n.state=e.state,n.events=e.events,function(t,e){do{var n;if(null!=t.attrs&&"function"==typeof t.attrs.onbeforeupdate&&void 0!==(n=a.call(t.attrs.onbeforeupdate,t,e))&&!n)break;if("string"!=typeof t.tag&&"function"==typeof t.state.onbeforeupdate&&void 0!==(n=a.call(t.state.onbeforeupdate,t,e))&&!n)break;return!1}while(0);return t.dom=e.dom,t.domSize=e.domSize,t.instance=e.instance,t.attrs=e.attrs,t.children=e.children,t.text=e.text,!0}(n,e))return;if("string"==typeof l)switch(null!=n.attrs&&$(n.attrs,n,i),l){case"#":!function(t,e){t.children.toString()!==e.children.toString()&&(t.dom.nodeValue=e.children),e.dom=t.dom}(e,n);break;case"<":!function(t,e,n,r,i){e.children!==n.children?(A(t,e),h(t,n,r,i)):(n.dom=e.dom,n.domSize=e.domSize,n.instance=e.instance)}(t,e,n,u,s);break;case"[":!function(t,e,n,r,i,o){f(t,e.children,n.children,r,i,o);var s=0,a=n.children;if(n.dom=null,null!=a){for(var u=0;u<a.length;u++){var l=a[u];null!=l&&null!=l.dom&&(null==n.dom&&(n.dom=l.dom),s+=l.domSize||1)}1!==s&&(n.domSize=s)}}(t,e,n,i,s,u);break;default:!function(t,e,n,i){var s=e.dom=t.dom;i=o(e)||i,"textarea"===e.tag&&(null==e.attrs&&(e.attrs={}),null!=e.text&&(e.attrs.value=e.text,e.text=void 0)),function(t,e,n,r){if(null!=n)for(var i in n)k(t,i,e&&e[i],n[i],r);var o;if(null!=e)for(var i in e)null==(o=e[i])||null!=n&&null!=n[i]||S(t,i,o,r)}(e,t.attrs,e.attrs,i),D(e)||(null!=t.text&&null!=e.text&&""!==e.text?t.text.toString()!==e.text.toString()&&(t.dom.firstChild.nodeValue=e.text):(null!=t.text&&(t.children=[r("#",void 0,void 0,t.text,void 0,t.dom.firstChild)]),null!=e.text&&(e.children=[r("#",void 0,void 0,e.text,void 0,void 0)]),f(s,t.children,e.children,n,null,i)))}(e,n,i,u)}else!function(t,e,n,i,o,s){if(n.instance=r.normalize(a.call(n.state.view,n)),n.instance===n)throw Error("A view cannot return the vnode it received as argument");$(n.state,n,i),null!=n.attrs&&$(n.attrs,n,i),null!=n.instance?(null==e.instance?c(t,n.instance,i,s,o):p(t,e.instance,n.instance,i,o,s),n.dom=n.instance.dom,n.domSize=n.instance.domSize):null!=e.instance?(C(t,e.instance),n.dom=void 0,n.domSize=0):(n.dom=e.dom,n.domSize=e.domSize)}(t,e,n,i,s,u)}else C(t,e),c(t,n,i,u,s)}function m(t,e,n){for(var r=Object.create(null);e<n;e++){var i=t[e];if(null!=i){var o=i.key;null!=o&&(r[o]=e)}}return r}var v=[];function g(t,e,n){for(;e<t.length;e++)if(null!=t[e]&&null!=t[e].dom)return t[e].dom;return n}function y(t,e,r){var i=n.createDocumentFragment();b(t,i,e),w(t,i,r)}function b(t,e,n){for(;null!=n.dom&&n.dom.parentNode===t;){if("string"!=typeof n.tag){if(null!=(n=n.instance))continue}else if("<"===n.tag)for(var r=0;r<n.instance.length;r++)e.appendChild(n.instance[r]);else if("["!==n.tag)e.appendChild(n.dom);else if(1===n.children.length){if(null!=(n=n.children[0]))continue}else for(r=0;r<n.children.length;r++){var i=n.children[r];null!=i&&b(t,e,i)}break}}function w(t,e,n){null!=n?t.insertBefore(e,n):t.appendChild(e)}function D(t){if(null==t.attrs||null==t.attrs.contenteditable&&null==t.attrs.contentEditable)return!1;var e=t.children;if(null!=e&&1===e.length&&"<"===e[0].tag){var n=e[0].children;t.dom.innerHTML!==n&&(t.dom.innerHTML=n)}else if(null!=t.text||null!=e&&0!==e.length)throw new Error("Child node of a contenteditable must be trusted");return!0}function x(t,e,n,r){for(var i=n;i<r;i++){var o=e[i];null!=o&&C(t,o)}}function C(t,e){var n,r,i,o=0,u=e.state;if("string"!=typeof e.tag&&"function"==typeof e.state.onbeforeremove&&null!=(i=a.call(e.state.onbeforeremove,e))&&"function"==typeof i.then&&(o=1,n=i),e.attrs&&"function"==typeof e.attrs.onbeforeremove&&null!=(i=a.call(e.attrs.onbeforeremove,e))&&"function"==typeof i.then&&(o|=2,r=i),s(e,u),o){if(null!=n){var l=function(){1&o&&((o&=2)||c())};n.then(l,l)}null!=r&&(l=function(){2&o&&((o&=1)||c())},r.then(l,l))}else N(e),E(t,e);function c(){s(e,u),N(e),E(t,e)}}function A(t,e){for(var n=0;n<e.instance.length;n++)t.removeChild(e.instance[n])}function E(t,e){for(;null!=e.dom&&e.dom.parentNode===t;){if("string"!=typeof e.tag){if(null!=(e=e.instance))continue}else if("<"===e.tag)A(t,e);else{if("["!==e.tag&&(t.removeChild(e.dom),!Array.isArray(e.children)))break;if(1===e.children.length){if(null!=(e=e.children[0]))continue}else for(var n=0;n<e.children.length;n++){var r=e.children[n];null!=r&&E(t,r)}}break}}function N(t){if("string"!=typeof t.tag&&"function"==typeof t.state.onremove&&a.call(t.state.onremove,t),t.attrs&&"function"==typeof t.attrs.onremove&&a.call(t.attrs.onremove,t),"string"!=typeof t.tag)null!=t.instance&&N(t.instance);else{var e=t.children;if(Array.isArray(e))for(var n=0;n<e.length;n++){var r=e[n];null!=r&&N(r)}}}function k(t,e,r,i,o){if("key"!==e&&"is"!==e&&null!=i&&!T(e)&&(r!==i||function(t,e){return"value"===e||"checked"===e||"selectedIndex"===e||"selected"===e&&t.dom===u()||"option"===t.tag&&t.dom.parentNode===n.activeElement}(t,e)||"object"==typeof i)){if("o"===e[0]&&"n"===e[1])return L(t,e,i);if("xlink:"===e.slice(0,6))t.dom.setAttributeNS("http://www.w3.org/1999/xlink",e.slice(6),i);else if("style"===e)I(t.dom,r,i);else if(F(t,e,o)){if("value"===e){if(("input"===t.tag||"textarea"===t.tag)&&t.dom.value===""+i&&t.dom===u())return;if("select"===t.tag&&null!==r&&t.dom.value===""+i)return;if("option"===t.tag&&null!==r&&t.dom.value===""+i)return}"input"===t.tag&&"type"===e?t.dom.setAttribute(e,i):t.dom[e]=i}else"boolean"==typeof i?i?t.dom.setAttribute(e,""):t.dom.removeAttribute(e):t.dom.setAttribute("className"===e?"class":e,i)}}function S(t,e,n,r){if("key"!==e&&"is"!==e&&null!=n&&!T(e))if("o"!==e[0]||"n"!==e[1]||T(e))if("style"===e)I(t.dom,n,null);else if(!F(t,e,r)||"className"===e||"value"===e&&("option"===t.tag||"select"===t.tag&&-1===t.dom.selectedIndex&&t.dom===u())||"input"===t.tag&&"type"===e){var i=e.indexOf(":");-1!==i&&(e=e.slice(i+1)),!1!==n&&t.dom.removeAttribute("className"===e?"class":e)}else t.dom[e]=null;else L(t,e,void 0)}function T(t){return"oninit"===t||"oncreate"===t||"onupdate"===t||"onremove"===t||"onbeforeremove"===t||"onbeforeupdate"===t}function F(t,e,n){return void 0===n&&(t.tag.indexOf("-")>-1||null!=t.attrs&&t.attrs.is||"href"!==e&&"list"!==e&&"form"!==e&&"width"!==e&&"height"!==e)&&e in t.dom}var _=/[A-Z]/g;function P(t){return"-"+t.toLowerCase()}function B(t){return"-"===t[0]&&"-"===t[1]?t:"cssFloat"===t?"float":t.replace(_,P)}function I(t,e,n){if(e===n);else if(null==n)t.style.cssText="";else if("object"!=typeof n)t.style.cssText=n;else if(null==e||"object"!=typeof e)for(var r in t.style.cssText="",n)null!=(i=n[r])&&t.style.setProperty(B(r),String(i));else{for(var r in n){var i;null!=(i=n[r])&&(i=String(i))!==String(e[r])&&t.style.setProperty(B(r),i)}for(var r in e)null!=e[r]&&null==n[r]&&t.style.removeProperty(B(r))}}function O(){this._=e}function L(t,e,n){if(null!=t.events){if(t.events[e]===n)return;null==n||"function"!=typeof n&&"object"!=typeof n?(null!=t.events[e]&&t.dom.removeEventListener(e.slice(2),t.events,!1),t.events[e]=void 0):(null==t.events[e]&&t.dom.addEventListener(e.slice(2),t.events,!1),t.events[e]=n)}else null==n||"function"!=typeof n&&"object"!=typeof n||(t.events=new O,t.dom.addEventListener(e.slice(2),t.events,!1),t.events[e]=n)}function M(t,e,n){"function"==typeof t.oninit&&a.call(t.oninit,e),"function"==typeof t.oncreate&&n.push(a.bind(t.oncreate,e))}function $(t,e,n){"function"==typeof t.onupdate&&n.push(a.bind(t.onupdate,e))}return O.prototype=Object.create(null),O.prototype.handleEvent=function(t){var e,n=this["on"+t.type];"function"==typeof n?e=n.call(t.currentTarget,t):"function"==typeof n.handleEvent&&n.handleEvent(t),this._&&!1!==t.redraw&&(0,this._)(),!1===e&&(t.preventDefault(),t.stopPropagation())},function(t,n,i){if(!t)throw new TypeError("Ensure the DOM element being passed to m.route/m.mount/m.render is not undefined.");var o=[],s=u(),a=t.namespaceURI;null==t.vnodes&&(t.textContent=""),n=r.normalizeChildren(Array.isArray(n)?n:[n]);var l=e;try{e="function"==typeof i?i:void 0,f(t,t.vnodes,n,o,null,"http://www.w3.org/1999/xhtml"===a?void 0:a)}finally{e=l}t.vnodes=n,null!=s&&u()!==s&&"function"==typeof s.focus&&s.focus();for(var c=0;c<o.length;c++)o[c]()}}},4031:(t,e,n)=>{"use strict";var r=n(1642);t.exports=function(t){return null==t&&(t=""),r("<",void 0,void 0,t,void 0,void 0)}},1642:t=>{"use strict";function e(t,e,n,r,i,o){return{tag:t,key:e,attrs:n,children:r,text:i,dom:o,domSize:void 0,state:void 0,events:void 0,instance:void 0}}e.normalize=function(t){return Array.isArray(t)?e("[",void 0,void 0,e.normalizeChildren(t),void 0,void 0):null==t||"boolean"==typeof t?null:"object"==typeof t?t:e("#",void 0,void 0,String(t),void 0,void 0)},e.normalizeChildren=function(t){var n=[];if(t.length){for(var r=null!=t[0]&&null!=t[0].key,i=1;i<t.length;i++)if((null!=t[i]&&null!=t[i].key)!==r)throw new TypeError("Vnodes must either always have keys or never have keys!");for(i=0;i<t.length;i++)n[i]=e.normalize(t[i])}return n},t.exports=e},8450:(t,e,n)=>{"use strict";var r=n(187),i=n(570);t.exports=n(1950)(window,r,i.redraw)},1950:(t,e,n)=>{"use strict";var r=n(3690);t.exports=function(t,e,n){var i=0;function o(t){return new e(t)}function s(t){return function(i,s){"string"!=typeof i?(s=i,i=i.url):null==s&&(s={});var a=new e((function(e,n){t(r(i,s.params),s,(function(t){if("function"==typeof s.type)if(Array.isArray(t))for(var n=0;n<t.length;n++)t[n]=new s.type(t[n]);else t=new s.type(t);e(t)}),n)}));if(!0===s.background)return a;var u=0;function l(){0==--u&&"function"==typeof n&&n()}return function t(e){var n=e.then;return e.constructor=o,e.then=function(){u++;var r=n.apply(e,arguments);return r.then(l,(function(t){if(l(),0===u)throw t})),t(r)},e}(a)}}function a(t,e){for(var n in t.headers)if({}.hasOwnProperty.call(t.headers,n)&&e.test(n))return!0;return!1}return o.prototype=e.prototype,o.__proto__=e,{request:s((function(e,n,r,i){var o,s=null!=n.method?n.method.toUpperCase():"GET",u=n.body,l=!(null!=n.serialize&&n.serialize!==JSON.serialize||u instanceof t.FormData),c=n.responseType||("function"==typeof n.extract?"":"json"),d=new t.XMLHttpRequest,h=!1,f=d,p=d.abort;for(var m in d.abort=function(){h=!0,p.call(this)},d.open(s,e,!1!==n.async,"string"==typeof n.user?n.user:void 0,"string"==typeof n.password?n.password:void 0),l&&null!=u&&!a(n,/^content-type$/i)&&d.setRequestHeader("Content-Type","application/json; charset=utf-8"),"function"==typeof n.deserialize||a(n,/^accept$/i)||d.setRequestHeader("Accept","application/json, text/*"),n.withCredentials&&(d.withCredentials=n.withCredentials),n.timeout&&(d.timeout=n.timeout),d.responseType=c,n.headers)({}).hasOwnProperty.call(n.headers,m)&&d.setRequestHeader(m,n.headers[m]);d.onreadystatechange=function(t){if(!h&&4===t.target.readyState)try{var o,s=t.target.status>=200&&t.target.status<300||304===t.target.status||/^file:\/\//i.test(e),a=t.target.response;if("json"===c?t.target.responseType||"function"==typeof n.extract||(a=JSON.parse(t.target.responseText)):c&&"text"!==c||null==a&&(a=t.target.responseText),"function"==typeof n.extract?(a=n.extract(t.target,n),s=!0):"function"==typeof n.deserialize&&(a=n.deserialize(a)),s)r(a);else{try{o=t.target.responseText}catch(t){o=a}var u=new Error(o);u.code=t.target.status,u.response=a,i(u)}}catch(t){i(t)}},"function"==typeof n.config&&(d=n.config(d,n,e)||d)!==f&&(o=d.abort,d.abort=function(){h=!0,o.call(this)}),null==u?d.send():"function"==typeof n.serialize?d.send(n.serialize(u)):u instanceof t.FormData?d.send(u):d.send(JSON.stringify(u))})),jsonp:s((function(e,n,r,o){var s=n.callbackName||"_mithril_"+Math.round(1e16*Math.random())+"_"+i++,a=t.document.createElement("script");t[s]=function(e){delete t[s],a.parentNode.removeChild(a),r(e)},a.onerror=function(){delete t[s],a.parentNode.removeChild(a),o(new Error("JSONP request failed"))},a.src=e+(e.indexOf("?")<0?"?":"&")+encodeURIComponent(n.callbackKey||"callback")+"="+encodeURIComponent(s),t.document.documentElement.appendChild(a)}))}}},2092:(t,e,n)=>{"use strict";var r=n(570);t.exports=n(3600)(window,r)},30:(t,e,n)=>{"use strict";t.exports=n(6807)},6807:t=>{!function(){"use strict";n.SKIP={},n.lift=function(){var t=arguments[0],e=Array.prototype.slice.call(arguments,1);return i(e).map((function(e){return t.apply(void 0,e)}))},n.scan=function(t,e,r){var i=r.map((function(r){var i=t(e,r);return i!==n.SKIP&&(e=i),i}));return i(e),i},n.merge=i,n.combine=r,n.scanMerge=function(t,e){var n=t.map((function(t){return t[0]})),i=r((function(){var r=arguments[arguments.length-1];return n.forEach((function(n,i){r.indexOf(n)>-1&&(e=t[i][1](e,n()))})),e}),n);return i(e),i},n["fantasy-land/of"]=n;var e=!1;function n(t){var e,i=[],s=[];function a(e){return arguments.length&&e!==n.SKIP&&(t=e,o(a)&&(a._changing(),a._state="active",i.forEach((function(e,n){e(s[n](t))})))),t}function u(){return(e=n()).map((function(t){return!0===t&&(a._parents.forEach((function(t){t._unregisterChild(a)})),a._state="ended",a._parents.length=i.length=s.length=0),t})),e}return a.constructor=n,a._state=arguments.length&&t!==n.SKIP?"active":"pending",a._parents=[],a._changing=function(){o(a)&&(a._state="changing"),i.forEach((function(t){t._changing()}))},a._map=function(e,r){var o=r?n():n(e(t));return o._parents.push(a),i.push(o),s.push(e),o},a.map=function(t){return a._map(t,"active"!==a._state)},a.toJSON=function(){return null!=t&&"function"==typeof t.toJSON?t.toJSON():t},a["fantasy-land/map"]=a.map,a["fantasy-land/ap"]=function(t){return r((function(t,e){return t()(e())}),[t,a])},a._unregisterChild=function(t){var e=i.indexOf(t);-1!==e&&(i.splice(e,1),s.splice(e,1))},Object.defineProperty(a,"end",{get:function(){return e||u()}}),a}function r(t,e){var r=e.every((function(t){if(t.constructor!==n)throw new Error("Ensure that each item passed to stream.combine/stream.merge/lift is a stream");return"active"===t._state})),i=r?n(t.apply(null,e.concat([e]))):n(),o=[],s=e.map((function(n){return n._map((function(s){return o.push(n),(r||e.every((function(t){return"pending"!==t._state})))&&(r=!0,i(t.apply(null,e.concat([o]))),o=[]),s}),!0)})),a=i.end.map((function(t){!0===t&&(s.forEach((function(t){t.end(!0)})),a.end(!0))}));return i}function i(t){return r((function(){return t.map((function(t){return t()}))}),t)}function o(t){return"pending"===t._state||"active"===t._state||"changing"===t._state}Object.defineProperty(n,"HALT",{get:function(){return e||console.log("HALT is deprecated and has been renamed to SKIP"),e=!0,n.SKIP}}),t.exports=n}()},619:(t,e,n)=>{"use strict";function r(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return i(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}n.r(e),n.d(e,{decode:()=>b,default:()=>C,encode:()=>w,toASCII:()=>x,toUnicode:()=>D,ucs2decode:()=>m,ucs2encode:()=>v});var o=2147483647,s=36,a=/^xn--/,u=/[^\0-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,c={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=Math.floor,h=String.fromCharCode;function f(t){throw new RangeError(c[t])}function p(t,e){var n=t.split("@"),r="";n.length>1&&(r=n[0]+"@",t=n[1]);var i=function(t,e){for(var n=[],r=t.length;r--;)n[r]=e(t[r]);return n}((t=t.replace(l,".")).split("."),e).join(".");return r+i}function m(t){for(var e=[],n=0,r=t.length;n<r;){var i=t.charCodeAt(n++);if(i>=55296&&i<=56319&&n<r){var o=t.charCodeAt(n++);56320==(64512&o)?e.push(((1023&i)<<10)+(1023&o)+65536):(e.push(i),n--)}else e.push(i)}return e}var v=function(t){return String.fromCodePoint.apply(String,t)},g=function(t,e){return t+22+75*(t<26)-((0!=e)<<5)},y=function(t,e,n){var r=0;for(t=n?d(t/700):t>>1,t+=d(t/e);t>455;r+=s)t=d(t/35);return d(r+36*t/(t+38))},b=function(t){var e,n=[],r=t.length,i=0,a=128,u=72,l=t.lastIndexOf("-");l<0&&(l=0);for(var c=0;c<l;++c)t.charCodeAt(c)>=128&&f("not-basic"),n.push(t.charCodeAt(c));for(var h=l>0?l+1:0;h<r;){for(var p=i,m=1,v=s;;v+=s){h>=r&&f("invalid-input");var g=(e=t.charCodeAt(h++))-48<10?e-22:e-65<26?e-65:e-97<26?e-97:s;(g>=s||g>d((o-i)/m))&&f("overflow"),i+=g*m;var b=v<=u?1:v>=u+26?26:v-u;if(g<b)break;var w=s-b;m>d(o/w)&&f("overflow"),m*=w}var D=n.length+1;u=y(i-p,D,0==p),d(i/D)>o-a&&f("overflow"),a+=d(i/D),i%=D,n.splice(i++,0,a)}return String.fromCodePoint.apply(String,n)},w=function(t){for(var e,n=[],i=(t=m(t)).length,a=128,u=0,l=72,c=r(t);!(e=c()).done;){var p=e.value;p<128&&n.push(h(p))}var v=n.length,b=v;for(v&&n.push("-");b<i;){for(var w,D=o,x=r(t);!(w=x()).done;){var C=w.value;C>=a&&C<D&&(D=C)}var A=b+1;D-a>d((o-u)/A)&&f("overflow"),u+=(D-a)*A,a=D;for(var E,N=r(t);!(E=N()).done;){var k=E.value;if(k<a&&++u>o&&f("overflow"),k==a){for(var S=u,T=s;;T+=s){var F=T<=l?1:T>=l+26?26:T-l;if(S<F)break;var _=S-F,P=s-F;n.push(h(g(F+_%P,0))),S=d(_/P)}n.push(h(g(S,0))),l=y(u,A,b==v),u=0,++b}}++u,++a}return n.join("")},D=function(t){return p(t,(function(t){return a.test(t)?b(t.slice(4).toLowerCase()):t}))},x=function(t){return p(t,(function(t){return u.test(t)?"xn--"+w(t):t}))};const C={version:"2.1.0",ucs2:{decode:m,encode:v},decode:b,encode:w,toASCII:x,toUnicode:D}},1655:t=>{!function(){var e=["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderStyle","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing","tabSize","MozTabSize"],n="undefined"!=typeof window,r=n&&null!=window.mozInnerScreenX;function i(t,i,o){if(!n)throw new Error("textarea-caret-position#getCaretCoordinates should only be called in a browser");var s=o&&o.debug||!1;if(s){var a=document.querySelector("#input-textarea-caret-position-mirror-div");a&&a.parentNode.removeChild(a)}var u=document.createElement("div");u.id="input-textarea-caret-position-mirror-div",document.body.appendChild(u);var l=u.style,c=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,d="INPUT"===t.nodeName;l.whiteSpace="pre-wrap",d||(l.wordWrap="break-word"),l.position="absolute",s||(l.visibility="hidden"),e.forEach((function(t){d&&"lineHeight"===t?l.lineHeight=c.height:l[t]=c[t]})),r?t.scrollHeight>parseInt(c.height)&&(l.overflowY="scroll"):l.overflow="hidden",u.textContent=t.value.substring(0,i),d&&(u.textContent=u.textContent.replace(/\s/g," "));var h=document.createElement("span");h.textContent=t.value.substring(i)||".",u.appendChild(h);var f={top:h.offsetTop+parseInt(c.borderTopWidth),left:h.offsetLeft+parseInt(c.borderLeftWidth),height:parseInt(c.lineHeight)};return s?h.style.backgroundColor="#aaa":document.body.removeChild(u),f}void 0!==t.exports?t.exports=i:n&&(window.getCaretCoordinates=i)}()},391:(t,e,n)=>{var r=n(9152),i=n(356);void 0===i.$&&(i.$=r),void 0===i.jQuery&&(i.jQuery=r),t.exports=r},1683:(t,e,n)=>{var r=n(2898),i=n(356);void 0===i.ColorThief&&(i.ColorThief=r),t.exports=r},982:(t,e,n)=>{var r=n(4757),i=n(356);void 0===i.dayjs&&(i.dayjs=r),t.exports=r},4473:(t,e,n)=>{var r=n(7945),i=n(356);void 0===i.m&&(i.m=r),t.exports=r},1885:(t,e,n)=>{var r=n(619),i=n(356);void 0===i.punycode&&(i.punycode=r),t.exports=r},9882:t=>{t.exports=function(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r},t.exports.__esModule=!0,t.exports.default=t.exports},9696:t=>{t.exports=function(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},230:t=>{t.exports=function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},1904:t=>{t.exports=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},8213:t=>{function e(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}t.exports=function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},402:t=>{t.exports=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},t.exports.__esModule=!0,t.exports.default=t.exports},9767:t=>{function e(n){return t.exports=e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},2061:(t,e,n)=>{var r=n(480);t.exports=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&r(t,e)},t.exports.__esModule=!0,t.exports.default=t.exports},1209:t=>{t.exports=function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o=[],s=!0,a=!1;try{for(n=n.call(t);!(s=(r=n.next()).done)&&(o.push(r.value),!e||o.length!==e);s=!0);}catch(t){a=!0,i=t}finally{try{s||null==n.return||n.return()}finally{if(a)throw i}}return o}},t.exports.__esModule=!0,t.exports.default=t.exports},6067:t=>{t.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},8974:(t,e,n)=>{var r=n(2075).default,i=n(230);t.exports=function(t,e){if(e&&("object"===r(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return i(t)},t.exports.__esModule=!0,t.exports.default=t.exports},9440:(t,e,n)=>{var r=n(2075).default;function i(){"use strict";t.exports=i=function(){return e},t.exports.__esModule=!0,t.exports.default=t.exports;var e={},n=Object.prototype,o=n.hasOwnProperty,s=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",c=a.toStringTag||"@@toStringTag";function d(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,n){return t[e]=n}}function h(t,e,n,r){var i=e&&e.prototype instanceof m?e:m,o=Object.create(i.prototype),a=new S(r||[]);return s(o,"_invoke",{value:A(t,n,a)}),o}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=h;var p={};function m(){}function v(){}function g(){}var y={};d(y,u,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(T([])));w&&w!==n&&o.call(w,u)&&(y=w);var D=g.prototype=m.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function C(t,e){function n(i,s,a,u){var l=f(t[i],t,s);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==r(d)&&o.call(d,"__await")?e.resolve(d.__await).then((function(t){n("next",t,a,u)}),(function(t){n("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return n("throw",t,a,u)}))}u(l.arg)}var i;s(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,i){n(t,r,e,i)}))}return i=i?i.then(o,o):o()}})}function A(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return{value:void 0,done:!0}}for(n.method=i,n.arg=o;;){var s=n.delegate;if(s){var a=E(s,n);if(a){if(a===p)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=f(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===p)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function E(t,e){var n=t.iterator[e.method];if(void 0===n){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,E(t,e),"throw"===e.method))return p;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var r=f(n,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function N(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(N,this),this.reset(!0)}function T(t){if(t){var e=t[u];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,r=function e(){for(;++n<t.length;)if(o.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:F}}function F(){return{value:void 0,done:!0}}return v.prototype=g,s(D,"constructor",{value:g,configurable:!0}),s(g,"constructor",{value:v,configurable:!0}),v.displayName=d(g,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,d(t,c,"GeneratorFunction")),t.prototype=Object.create(D),t},e.awrap=function(t){return{__await:t}},x(C.prototype),d(C.prototype,l,(function(){return this})),e.AsyncIterator=C,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var s=new C(h(t,n,r,i),o);return e.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},x(D),d(D,c,"Generator"),d(D,u,(function(){return this})),d(D,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,r){return s.type="throw",s.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var a=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(a&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=t,s.arg=e,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),k(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;k(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},e}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports},480:t=>{function e(n,r){return t.exports=e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n,r)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},5387:(t,e,n)=>{var r=n(9696),i=n(1209),o=n(9111),s=n(6067);t.exports=function(t,e){return r(t)||i(t,e)||o(t,e)||s()},t.exports.__esModule=!0,t.exports.default=t.exports},2075:t=>{function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},9111:(t,e,n)=>{var r=n(9882);t.exports=function(t,e){if(t){if("string"==typeof t)return r(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(t,e):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports},2507:(t,e,n)=>{var r=n(9440)();t.exports=r;try{regeneratorRuntime=r}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r].call(o.exports,o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};(()=>{"use strict";n.r(r),n.d(r,{app:()=>v,compat:()=>Co});var t={};n.r(t),n.d(t,{camelCaseToSnakeCase:()=>Z,generateRandomString:()=>tt,getPlainContent:()=>J,slug:()=>X,truncate:()=>K,ucfirst:()=>Q});var e={};n.r(e),n.d(e,{default:()=>mt,fireDeprecationWarning:()=>vt});var i={};n.r(i),n.d(i,{createFocusTrap:()=>dr});var o={};n.r(o),n.d(o,{extend:()=>xi,override:()=>Ci});var s={};n.r(s),n.d(s,{debounce:()=>ie,throttle:()=>re}),n(391),n(4473),n(982),n(9043),n(6199),n(7865),n(6935),n(9411);var a=n(5635),u=n.n(a),l=n(6982),c=n.n(l);function d(t,e){var n="select"===t.tag?t.attrs.multi?"multi":"select":t.attrs.type;return t.attrs.onchange="multi"===n?function(){e([].slice.call(this.selectedOptions,(function(t){return t.value})))}:"select"===n?function(t){e(this.selectedOptions[0].value)}:"checkbox"===n?function(t){e(this.checked)}:t.attrs.oninput=function(t){e(this.value)},"select"===t.tag?t.children.forEach((function(t){t.attrs.value!==e()&&t.children[0]!==e()||(t.attrs.selected=!0)})):"checkbox"===n?t.attrs.checked=e():"radio"===n?t.attrs.checked=e()===t.attrs.value:t.attrs.value=e(),t.attrs.bidi=null,t}d.view=function(t,e,n){return d(e,e.attrs.bidi)};const h=d;function f(t){var e=t.m,n=function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var o=e.apply(this,arguments);return o.attrs||(o.attrs={}),o.attrs.bidi&&h(o,o.attrs.bidi),o};Object.keys(e).forEach((function(t){return n[t]=e[t]})),t.m=n}var p=window;const v=new Proxy({},{get:function(t,e){return Reflect.get(p.app,e,p.app)},set:function(t,e,n){return Reflect.set(p.app,e,n,p.app)}});n(1859),dayjs.extend(u()),dayjs.extend(c()),f(window);var g=$.fn.tooltip;function y(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function w(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,b(t,e)}function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},D.apply(this,arguments)}function x(t,e,n){void 0===e&&(e=null),void 0===n&&(n={});var r=D({},n);r.state=r.state||{},r.state.key=Date.now(),m.route.set(t,e,r)}$.fn.tooltip=function(t,e){["DANGEROUS_tooltip_jquery_fn_deprecation_exempt"].includes(e)||console.warn("Calling `$.tooltip` is now deprecated. Please use the `<Tooltip>` component exposed by flarum/core instead. `$.tooltip` may be removed in a future version of Flarum.\n\nIf this component doesn't meet your requirements, please open an issue: https://github.com/flarum/core/issues/new?assignees=davwheat&labels=type/bug,needs-verification&template=bug-report.md&title=Tooltip%20component%20unsuitable%20for%20use%20case"),g.bind(this)(t)},n(1885),n(1683);var C=function(){function t(){this.stack=[]}var e=t.prototype;return e.getCurrent=function(){return this.stack[this.stack.length-1]},e.getPrevious=function(){return this.stack[this.stack.length-2]},e.push=function(t,e,n){void 0===n&&(n=m.route.get());var r=this.stack[this.stack.length-2];r&&r.name===t&&this.stack.pop();var i=this.getCurrent();i&&i.name===t?Object.assign(i,{url:n,title:e}):this.stack.push({name:t,url:n,title:e})},e.canGoBack=function(){return this.stack.length>1},e.back=function(){if(!this.canGoBack())return this.home();this.stack.pop(),m.route.set(this.getCurrent().url)},e.backUrl=function(){return this.stack[this.stack.length-2].url},e.home=function(){this.stack.splice(0),x("/")},t}(),A=function(){function t(t){this.pinnedKey="panePinned",this.$element=$(t),this.pinned="true"===localStorage.getItem(this.pinnedKey),this.active=!1,this.showing=!1,this.render()}var e=t.prototype;return e.enable=function(){this.active=!0,this.render()},e.disable=function(){this.active=!1,this.showing=!1,this.render()},e.show=function(){clearTimeout(this.hideTimeout),this.showing=!0,this.render()},e.hide=function(){this.showing=!1,this.render()},e.onmouseleave=function(){this.hideTimeout=setTimeout(this.hide.bind(this),250)},e.togglePinned=function(){this.pinned=!this.pinned,localStorage.setItem(this.pinnedKey,this.pinned?"true":"false"),this.render()},e.render=function(){this.$element.toggleClass("panePinned",this.pinned).toggleClass("hasPane",this.active).toggleClass("paneShowing",this.showing)},t}(),E=function(){function t(){this.element=void 0,this.attrs=void 0,this.state=void 0}var e=t.prototype;return e.oninit=function(t){this.setAttrs(t.attrs)},e.oncreate=function(t){this.element=t.dom},e.onbeforeupdate=function(t){this.setAttrs(t.attrs)},e.onupdate=function(t){},e.onbeforeremove=function(t){},e.onremove=function(t){},e.$=function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){var e=$(this.element);return t?e.find(t):e})),t.component=function(t,e){void 0===t&&(t={}),void 0===e&&(e=null);var n=D({},t);return m(this,n,e)},e.setAttrs=function(t){if(void 0===t&&(t={}),this.constructor.initAttrs(t),t){if("children"in t)throw new Error("["+this.constructor.name+'] The "children" attribute of attrs should never be used. Either pass children in as the vnode children or rename the attribute');if("tag"in t)throw new Error("["+this.constructor.name+'] You cannot use the "tag" attribute name with Mithril 2.')}this.attrs=t},t.initAttrs=function(t){},t}();function N(t,e){return t&&(t===e||t.prototype instanceof e)}var k=function(){function t(t,e){void 0===e&&(e={}),this.type=t,this.data=e}var e=t.prototype;return e.matches=function(t,e){var n=this;return void 0===e&&(e={}),!!N(this.type,t)&&Object.keys(e).every((function(t){return n.data[t]===e[t]}))},e.get=function(t){return this.data[t]},e.set=function(t,e){this.data[t]=e},t}(),S=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).bodyClass="",e.scrollTopOnCreate=!0,e.useBrowserScrollRestoration=!0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),v.previous=v.current,v.current=new k(this.constructor,{routeName:this.attrs.routeName}),v.drawer.hide(),v.modal.close()},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.bodyClass&&$("#app").addClass(this.bodyClass),this.scrollTopOnCreate&&$(window).scrollTop(0),"scrollRestoration"in history&&(history.scrollRestoration=this.useBrowserScrollRestoration?"auto":"manual")},n.onremove=function(e){t.prototype.onremove.call(this,e),this.bodyClass&&$("#app").removeClass(this.bodyClass)},e}(E);function T(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function F(t,e,n){return e&&T(t.prototype,e),n&&T(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _(t){var e=typeof t;return"function"===e||"object"===e&&!!t}var P=function(t,e){this.content=void 0,this.priority=void 0,this.content=t,this.priority=e},B=function(){function t(){this._items={}}var e=t.prototype;return e.isEmpty=function(){return 0===Object.keys(this._items).length},e.has=function(t){return Object.keys(this._items).includes(t)},e.get=function(t){return this._items[t].content},e.getPriority=function(t){return this._items[t].priority},e.add=function(t,e,n){return void 0===n&&(n=0),this._items[t]=new P(e,n),this},e.replace=function(t,e,n){return void 0===e&&(e=null),void 0===n&&(n=null),this.has(t)?(null!==e&&(this._items[t].content=e),null!==n&&(this._items[t].priority=n),this):this},e.setContent=function(t,e){if(!this.has(t))throw new Error("[ItemList] Cannot set content of Item. Key `"+t+"` is not present.");return this.replace(t,e)},e.setPriority=function(t,e){if(!this.has(t))throw new Error("[ItemList] Cannot set priority of Item. Key `"+t+"` is not present.");return this._items[t].priority=e,this},e.remove=function(t){return delete this._items[t],this},e.merge=function(t){var e=this;return Object.keys(t._items).forEach((function(n){var r=t._items[n];r instanceof P&&(e._items[n]=r)})),this},e.toArray=function(t){var e=this;return void 0===t&&(t=!1),Object.keys(this._items).map((function(n,r){var i=e._items[n];return!t||_(i.content)?D({},i,{content:e.createItemContentProxy(_(i.content)?i.content:Object(i.content),n)}):D({},i)})).sort((function(t,e){return e.priority-t.priority})).map((function(t){return t.content}))},e.toObject=function(){var t=this;return Object.keys(this._items).reduce((function(e,n){var r={content:t.get(n),itemName:n,priority:t.getPriority(n)};return e[n]=r,e}),{})},e.createItemContentProxy=function(t,e){return new Proxy(t,{get:function(t,n,r){return"itemName"===n?e:Reflect.get(t,n,r)},set:function(t,n,r,i){if(null!==e&&"itemName"===n)throw new Error("`itemName` property is read-only");return Reflect.set(t,n,r,i)}})},F(t,[{key:"items",get:function(){return new Proxy(this._items,{set:function(){return console.warn("Modifying `ItemList.items` is not allowed."),!1}})}}]),t}(),I=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){return m("li",{className:"Dropdown-separator"})},e}(E);I.isListItem=!0;const O=I;function L(t){var e,n,r="";if("string"==typeof t||"number"==typeof t)r+=t;else if("object"==typeof t)if(Array.isArray(t))for(e=0;e<t.length;e++)t[e]&&(n=L(t[e]))&&(r&&(r+=" "),r+=n);else for(e in t)t[e]&&(r&&(r+=" "),r+=e);return r}const M=function(){for(var t,e,n=0,r="";n<arguments.length;)(t=arguments[n++])&&(e=L(t))&&(r&&(r+=" "),r+=e);return r};function j(t){return"object"==typeof t&&null!==t&&"tag"in t}function R(t){return j(t)&&t.tag===O}function H(t,e,n){void 0===e&&(e="li"),void 0===n&&(n={});var r,i,o,s=e;return(r=t instanceof Array?t:[t],o=[],r.filter(Boolean).forEach((function(t,e){(!R(t)||i&&!R(i)&&e!==r.length-1)&&(i=t,o.push(t))})),o).map((function(t){var e,r,i=[t.itemName&&"item-"+t.itemName];if(j(t)&&t.tag.isListItem)return t.attrs=t.attrs||{},t.attrs.key=t.attrs.key||t.itemName,t.key=t.attrs.key,t;j(t)&&(i.push((null==(r=t.attrs)?void 0:r.itemClassName)||t.itemClassName),null!=t.tag.isActive&&t.tag.isActive(t.attrs)&&i.push("active"));var o=j(t)&&(null==t||null==(e=t.attrs)?void 0:e.key)||t.itemName;return m(s,Object.assign({className:M(i),key:o},n),t)}))}var U=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){return m("header",{className:"Hero DiscussionHero"},m("div",{className:"container"},m("ul",{className:"DiscussionHero-items"},H(this.items().toArray()))))},n.items=function(){var t=new B,e=this.attrs.discussion,n=e.badges().toArray();return n.length&&t.add("badges",m("ul",{className:"DiscussionHero-badges badges"},H(n)),10),t.add("title",m("h1",{className:"DiscussionHero-title"},e.title())),t},e}(E);function q(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}function z(t,e){var n=t[e];return delete t[e],n}var V,W=["options"],G=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(t){var e=t.attrs,n=e.options,r=void 0===n?{}:n,i=q(e,W);i.href||(i.href="");var o=t.children||{tag:"#",children:t.text};return i.external?m("a",i,o):(i.href===m.route.get()&&("replace"in r||(r.replace=!0)),z(i,"force")&&("state"in r||(r.state={}),"key"in r.state||(r.state.key=Date.now())),i.options=r,m(m.route.Link,i,o))},e}(E);function Y(t,e){var n;void 0===e&&(e={}),e.className=M("Avatar",e.className),null!=(n=e).loading||(n.loading="lazy");var r="",i="undefined"===e.title||e.title;if(i||delete e.title,t){var o=t.displayName()||"?",s=t.avatarUrl();if(i&&(e.title=e.title||o),s)return m("img",Object.assign({},e,{src:s,alt:""}));r=o.charAt(0).toUpperCase(),e.style={"--avatar-bg":t.color()}}return m("span",e,r)}function K(t,e,n){return void 0===n&&(n=0),(n>0?"...":"")+t.substring(n,n+e)+(t.length>n+e?"...":"")}function X(t,e){switch(void 0===e&&(e=V.ALPHANUMERIC),e){case V.UTF8:return t.toLowerCase().replace(/(?:(?![0-9A-Za-z\xAA\xB2\xB3\xB5\xB9\xBA\xBC-\xBE\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u052F\u0531-\u0556\u0559\u0560-\u0588\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05EF-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u07FD\u0800-\u082D\u0840-\u085B\u0860-\u086A\u0870-\u0887\u0889-\u088E\u0898-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09F4-\u09F9\u09FC\u09FE\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71-\u0B77\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BF2\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C78-\u0C7E\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1-\u0CF3\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D63\u0D66-\u0D78\u0D7A-\u0D7F\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECE\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F33\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u137C\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1715\u171F-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u17F0-\u17F9\u180B-\u180D\u180F-\u1819\u1820-\u1878\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ACE\u1B00-\u1B4C\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CD0-\u1CD2\u1CD4-\u1CFA\u1D00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2070\u2071\u2074-\u2079\u207F-\u2089\u2090-\u209C\u20D0-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2150-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2C00-\u2CE4\u2CEB-\u2CF3\u2CFD\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u3192-\u3195\u31A0-\u31BF\u31F0-\u31FF\u3220-\u3229\u3248-\u324F\u3251-\u325F\u3280-\u3289\u32B1-\u32BF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA672\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA827\uA82C\uA830-\uA835\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0-\uDEFB\uDF00-\uDF23\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC58-\uDC76\uDC79-\uDC9E\uDCA7-\uDCAF\uDCE0-\uDCF2\uDCF4\uDCF5\uDCFB-\uDD1B\uDD20-\uDD39\uDD80-\uDDB7\uDDBC-\uDDCF\uDDD2-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE38-\uDE3A\uDE3F-\uDE48\uDE60-\uDE7E\uDE80-\uDE9F\uDEC0-\uDEC7\uDEC9-\uDEE6\uDEEB-\uDEEF\uDF00-\uDF35\uDF40-\uDF55\uDF58-\uDF72\uDF78-\uDF91\uDFA9-\uDFAF]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDCFA-\uDD27\uDD30-\uDD39\uDE60-\uDE7E\uDE80-\uDEA9\uDEAB\uDEAC\uDEB0\uDEB1\uDEFD-\uDF27\uDF30-\uDF54\uDF70-\uDF85\uDFB0-\uDFCB\uDFE0-\uDFF6]|\uD804[\uDC00-\uDC46\uDC52-\uDC75\uDC7F-\uDCBA\uDCC2\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD44-\uDD47\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDC9-\uDDCC\uDDCE-\uDDDA\uDDDC\uDDE1-\uDDF4\uDE00-\uDE11\uDE13-\uDE37\uDE3E-\uDE41\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3B-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC5E-\uDC61\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB8\uDEC0-\uDEC9\uDF00-\uDF1A\uDF1D-\uDF2B\uDF30-\uDF3B\uDF40-\uDF46]|\uD806[\uDC00-\uDC3A\uDCA0-\uDCF2\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD35\uDD37\uDD38\uDD3B-\uDD43\uDD50-\uDD59\uDDA0-\uDDA7\uDDAA-\uDDD7\uDDDA-\uDDE1\uDDE3\uDDE4\uDE00-\uDE3E\uDE47\uDE50-\uDE99\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC6C\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD8E\uDD90\uDD91\uDD93-\uDD98\uDDA0-\uDDA9\uDEE0-\uDEF6\uDF00-\uDF10\uDF12-\uDF3A\uDF3E-\uDF42\uDF50-\uDF59\uDFB0\uDFC0-\uDFD4]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2F\uDC40-\uDC55]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDE70-\uDEBE\uDEC0-\uDEC9\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF5B-\uDF61\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE96\uDF00-\uDF4A\uDF4F-\uDF87\uDF8F-\uDF9F\uDFE0\uDFE1\uDFE3\uDFE4\uDFF0\uDFF1]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD32\uDD50-\uDD52\uDD55\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD833[\uDF00-\uDF2D\uDF30-\uDF46]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44\uDEC0-\uDED3\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDC30-\uDC6D\uDC8F\uDD00-\uDD2C\uDD30-\uDD3D\uDD40-\uDD49\uDD4E\uDE90-\uDEAE\uDEC0-\uDEF9]|\uD839[\uDCD0-\uDCF9\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDCC7-\uDCD6\uDD00-\uDD4B\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD83C[\uDD00-\uDD0C]|\uD83E[\uDFF0-\uDFF9]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF]|\uDB40[\uDD00-\uDDEF])[\s\S])/gi,"-").replace(/-+/g,"-").replace(/-$|^-/g,"");case V.ALPHANUMERIC:default:return t.toLowerCase().replace(/[^a-z0-9]/gi,"-").replace(/-+/g,"-").replace(/-$|^-/g,"")}}function J(t){var e=t.replace(/(<\/p>|<br>)/g,"$1 &nbsp;").replace(/<img\b[^>]*>/gi," "),n=(new DOMParser).parseFromString(e,"text/html").documentElement;return J.removeSelectors.forEach((function(t){n.querySelectorAll(t).forEach((function(t){t.remove()}))})),n.innerText.replace(/\s+/g," ").trim()}function Q(t){return t.substr(0,1).toUpperCase()+t.substr(1)}function Z(t){return t.replace(/[A-Z]/g,(function(t){return"_"+t.toLowerCase()}))}function tt(t){if(t<0)throw new Error("Cannot generate a random string with length less than 0.");if(0===t)return"";var e=new Uint8Array(t/2);return window.crypto.getRandomValues(e),Array.from(e,(function(t){return t.toString(16).padStart(2,"0")})).join("")}function et(t,e,n){if(!e&&!n)return t;var r=e instanceof RegExp?e:new RegExp(null!=e?e:"","gi"),i=t,o=0;return n&&(e&&(o=Math.max(0,t.search(r)-n/2)),i=K(i,n,o)),i=$("<div/>").text(i).html(),e&&(i=i.replace(r,"<mark>$&</mark>")),m.trust(i)}function nt(t,e){return void 0===e&&(e={}),e.className=M("icon",t,e.className),m("i",Object.assign({"aria-hidden":"true"},e))}!function(t){t.ALPHANUMERIC="alphanum",t.UTF8="utf8"}(V||(V={})),J.removeSelectors=["blockquote","script"];var rt=n(4757),it=n.n(rt);function ot(t){return t instanceof Array?t.map((function(t){return ot(t)})).join(""):"object"==typeof t&&null!==t?t.children?ot(t.children):String(t.text):String(t)}function st(t){var e=it()(t),n=it()();return e.isAfter(n)&&(e=n),e.diff(it()())<-2592e6?e.year()===it()().year()?e.format(ot(v.translator.trans("core.lib.datetime_formats.humanTimeShort"))):e.format(ot(v.translator.trans("core.lib.datetime_formats.humanTimeLong"))):e.fromNow()}function at(t){return t>=1e6?Math.floor(t/1e6)+ot(v.translator.trans("core.lib.number_suffix.mega_text")):t>=1e3?(t/1e3).toFixed(1)+ot(v.translator.trans("core.lib.number_suffix.kilo_text")):t.toString()}var ut=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).showing=!1,e}w(e,t),e.initAttrs=function(t){t.className||(t.className=""),t.buttonClassName||(t.buttonClassName=""),t.menuClassName||(t.menuClassName=""),t.label||(t.label=""),null!=t.caretIcon||(t.caretIcon="fas fa-caret-down"),t.accessibleToggleLabel||(t.accessibleToggleLabel=ot(v.translator.trans("core.lib.dropdown.toggle_dropdown_accessible_label")))};var n=e.prototype;return n.view=function(t){var e=t.children?H(t.children):[],n=!this.attrs.lazyDraw||this.showing;return m("div",{className:"ButtonGroup Dropdown dropdown "+this.attrs.className+" itemCount"+e.length+(this.showing?" open":"")},this.getButton(t.children),n&&this.getMenu(e))},n.oncreate=function(e){var n=this;t.prototype.oncreate.call(this,e),this.$().on("shown.bs.dropdown",(function(){var t,e,r,i,o,s,a,u,l,c,d,h=n.attrs,f=h.lazyDraw,p=h.onshow;n.showing=!0,f&&m.redraw.sync(),"function"==typeof p&&p(),f||m.redraw();var v=n.$(".Dropdown-menu"),g=v.hasClass("Dropdown-menu--right"),y=null!=(t=null==(e=v.offset())?void 0:e.top)?t:0,b=null!=(r=v.height())?r:0,w=null!=(i=$(window).scrollTop())?i:0,D=null!=(o=$(window).height())?o:0;v.removeClass("Dropdown-menu--top Dropdown-menu--right"),v.toggleClass("Dropdown-menu--top",y+b>w+D),((null==(s=v.offset())?void 0:s.top)||0)<0&&v.removeClass("Dropdown-menu--top");var x=null!=(a=null==(u=v.offset())?void 0:u.left)?a:0,C=null!=(l=v.width())?l:0,A=null!=(c=$(window).scrollLeft())?c:0,E=null!=(d=$(window).width())?d:0;v.toggleClass("Dropdown-menu--right",g||x+C>A+E)})),this.$().on("hidden.bs.dropdown",(function(){n.showing=!1,n.attrs.onhide&&n.attrs.onhide(),m.redraw()}))},n.getButton=function(t){return m("button",{className:"Dropdown-toggle "+this.attrs.buttonClassName,"aria-haspopup":"menu","aria-label":this.attrs.accessibleToggleLabel,"data-toggle":"dropdown",onclick:this.attrs.onclick},this.getButtonContent(t))},n.getButtonContent=function(t){return[this.attrs.icon?nt(this.attrs.icon,{className:"Button-icon"}):"",m("span",{className:"Button-label"},this.attrs.label),this.attrs.caretIcon?nt(this.attrs.caretIcon,{className:"Button-caret"}):""]},n.getMenu=function(t){return m("ul",{className:"Dropdown-menu dropdown-menu "+this.attrs.menuClassName},t)},e}(E);function lt(t){var e=it()(t),n=e.format(),r=e.format("LLLL"),i=st(t);return m("time",{pubdate:!0,datetime:n,title:r,"data-humantime":!0},i)}var ct=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){var t=this.attrs.discussion,e=this.attrs.lastPost&&t.replyCount(),n=t[e?"lastPostedUser":"user"](),r=t[e?"lastPostedAt":"createdAt"]();return m("span",null,!!e&&nt("fas fa-reply")," ",Wi.translator.trans("core.forum.discussion_list."+(e?"replied":"started")+"_text",{user:n,ago:lt(r)}))},e}(E),dt=function(){function t(){this.callbacks=void 0,this.data=void 0;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.callbacks=e,this.data={},this.needsRebuild()}var e=t.prototype;return e.needsRebuild=function(){var t=this,e=!1;return this.callbacks.forEach((function(n,r){var i=n();i!==t.data[r]&&(t.data[r]=i,e=!0)})),e},e.check=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];this.callbacks=this.callbacks.concat(e),this.needsRebuild()},e.invalidate=function(){this.data={}},t}(),ht=["display","size","containerClassName","className"],ft=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){var t=this.attrs,e=t.display,n=void 0===e?"block":e,r=t.size,i=void 0===r?"medium":r,o=t.containerClassName,s=t.className,a=q(t,ht),u=M("LoadingIndicator",s),l=M("LoadingIndicator-container","unset"!==n&&"LoadingIndicator-container--"+n,i&&"LoadingIndicator-container--"+i,o);return m("div",Object.assign({"aria-label":v.translator.trans("core.lib.loading_indicator.accessible_label"),role:"status"},a.containerAttrs,{"data-size":i,className:l}),m("div",Object.assign({"aria-hidden":"true",className:u},a)))},e}(E),pt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.handler=function(){return this.attrs.when()||void 0},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.boundHandler=this.handler.bind(this),$(window).on("beforeunload",this.boundHandler)},n.onremove=function(e){t.prototype.onremove.call(this,e),$(window).off("beforeunload",this.boundHandler)},n.view=function(t){return m("[",null,t.children)},e}(E);function mt(){var t;v.forum.attribute("debug")&&(t=console).warn.apply(t,arguments)}function vt(t,e,n,r){void 0===n&&(n="2.0"),void 0===r&&(r="flarum/core"),mt("[Flarum "+n+" Deprecation] "+t+"\n\nSee: https://github.com/"+r+"/pull/"+e)}var gt=["type","title","aria-label","icon","disabled","loading","className","class"],yt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(t){var e=this.attrs,n=e.type,r=e.title,i=e["aria-label"],o=e.icon,s=e.disabled,a=e.loading,u=e.className,l=e.class,c=q(e,gt);n||(n="button"),i||(i=r),"object"==typeof i&&(i=ot(i)),(s||a)&&delete c.onclick;var d=D({disabled:s,className:u=M(l,u,{hasIcon:o,disabled:s||a,loading:a}),type:n,"aria-label":i},c);return m("button",d,this.getButtonContent(t.children))},n.oncreate=function(n){var r;t.prototype.oncreate.call(this,n);var i=this.attrs["aria-label"];this.view!==e.prototype.view||i||ot(n.children)||null!=(r=this.element)&&null!=r.getAttribute&&r.getAttribute("aria-label")||mt('[Flarum Accessibility Warning] Button has no content and no accessible label. This means that screen-readers will not be able to interpret its meaning and just read "Button". Consider providing accessible text via the `aria-label` attribute. https://web.dev/button-name',this.element)},n.getButtonContent=function(t){var e=this.attrs.icon;return[e&&nt(e,{className:"Button-icon"}),t&&m("span",{className:"Button-label"},t),this.attrs.loading&&m(ft,{size:"small",display:"inline"})]},e}(E),bt=n(1655),wt=n.n(bt),Dt=null;function xt(t,e){var n=e.text,r=e.selectionStart,i=e.selectionEnd,o=t.selectionStart,s=t.value.slice(0,o),a=t.value.slice(t.selectionEnd);if(null===Dt||!0===Dt){t.contentEditable="true";try{Dt=document.execCommand("insertText",!1,n)}catch(t){Dt=!1}t.contentEditable="false"}Dt&&!t.value.slice(0,t.selectionStart).endsWith(n)&&(Dt=!1),Dt||(t.value=s+n+a,t.dispatchEvent(new CustomEvent("input",{bubbles:!0,cancelable:!0}))),null!=r&&null!=i?t.setSelectionRange(r,i):t.setSelectionRange(o,t.selectionEnd)}var Ct=function(){function t(t,e){this.el=void 0,this.el=document.createElement("textarea"),this.build(t,e)}var e=t.prototype;return e.build=function(t,e){var n=this;this.el.className=e.classNames.join(" "),this.el.disabled=e.disabled,this.el.placeholder=e.placeholder,this.el.value=e.value;var r=function(t){e.inputListeners.forEach((function(t){t()})),t.redraw=!1};this.el.oninput=function(t){e.oninput(n.el.value),r(t)},this.el.onclick=r,this.el.onkeyup=r,this.el.addEventListener("keydown",(function(t){n.keyHandlers(e).toArray().forEach((function(e){return e(t)}))})),t.append(this.el)},e.keyHandlers=function(t){var e=new B;return e.add("submit",(function(e){(e.metaKey||e.ctrlKey)&&"Enter"===e.key&&t.onsubmit()})),e},e.moveCursorTo=function(t){this.setSelectionRange(t,t)},e.getSelectionRange=function(){return[this.el.selectionStart,this.el.selectionEnd]},e.getLastNChars=function(t){return this.el.value.slice(Math.max(0,this.el.selectionStart-t),this.el.selectionStart)},e.insertAtCursor=function(t){this.insertAt(this.el.selectionStart,t)},e.insertAt=function(t,e){this.insertBetween(t,t,e)},e.insertBetween=function(t,e,n){this.setSelectionRange(t,e);var r=t+n.length;xt(this.el,{text:n,selectionStart:r,selectionEnd:r})},e.replaceBeforeCursor=function(t,e){this.insertBetween(t,this.el.selectionStart,e)},e.setSelectionRange=function(t,e){this.el.setSelectionRange(t,e),this.focus()},e.getCaretCoordinates=function(t){var e=wt()(this.el,t);return{top:e.top-this.el.scrollTop,left:e.left}},e.disabled=function(t){this.el.disabled=t},e.focus=function(){this.el.focus()},e.destroy=function(){this.el.remove()},t}(),At=["text","tooltipVisible","showOnFocus","position","ignoreTitleWarning","html","delay"],Et=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).firstChild=null,e.childDomNode=null,e.oldText="",e.oldVisibility=void 0,e.shouldRecreateTooltip=!1,e.shouldChangeTooltipVisibility=!1,e}w(e,t);var n=e.prototype;return n.view=function(t){var e=t.children,n=this.attrs,r=(n.text,n.tooltipVisible),i=(n.showOnFocus,n.position,n.ignoreTitleWarning),o=void 0!==i&&i;n.html,n.delay,q(n,At),this.attrs.title&&!o&&console.warn("`title` attribute was passed to Tooltip component. Was this intentional? Tooltip content should be passed to the `text` attr instead.");var s=this.getRealText();if(s!==this.oldText&&(this.oldText=s,this.shouldRecreateTooltip=!0),r!==this.oldVisibility&&(this.oldVisibility=this.attrs.tooltipVisible,this.shouldChangeTooltipVisibility=!0),void 0===e)throw new Error("Tooltip component was provided with no direct child DOM element. Tooltips must contain a single direct DOM node to attach to.");if(1!==e.length)throw new Error("Tooltip component was either passed more than one or no child node.\n\nPlease wrap multiple children in another element, such as a <div> or <span>.");var a=e[0];if("object"!=typeof a||Array.isArray(a)||null===a)throw new Error("Tooltip component was provided with no direct child DOM element. Tooltips must contain a single direct DOM node to attach to.");if("string"==typeof a.tag&&["#","[","<"].includes(a.tag))throw new Error('Tooltip component with provided with a vnode with tag "'+a.tag+'". This is not a DOM element, so is not a valid child element. Please wrap this vnode in another element, such as a <div> or <span>.');return this.firstChild=a,e},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.checkDomNodeChanged(),this.recreateTooltip()},n.onupdate=function(e){t.prototype.onupdate.call(this,e),this.checkDomNodeChanged(),this.recreateTooltip()},n.recreateTooltip=function(){this.shouldRecreateTooltip&&null!==this.childDomNode&&($(this.childDomNode).tooltip("destroy","DANGEROUS_tooltip_jquery_fn_deprecation_exempt"),this.createTooltip(),this.shouldRecreateTooltip=!1),this.shouldChangeTooltipVisibility&&(this.shouldChangeTooltipVisibility=!1,this.updateVisibility())},n.updateVisibility=function(){null!==this.childDomNode&&(!0===this.attrs.tooltipVisible?$(this.childDomNode).tooltip("show","DANGEROUS_tooltip_jquery_fn_deprecation_exempt"):!1===this.attrs.tooltipVisible&&$(this.childDomNode).tooltip("hide","DANGEROUS_tooltip_jquery_fn_deprecation_exempt"))},n.createTooltip=function(){if(null!==this.childDomNode){var t=this.attrs,e=t.showOnFocus,n=void 0===e||e,r=t.position,i=void 0===r?"top":r,o=t.delay,s=t.html,a=void 0!==s&&s,u=t.tooltipVisible,l=(t.text,"boolean"==typeof u?"manual":M("hover",[n&&"focus"])),c=this.getRealText();this.childDomNode.setAttribute("title",c),this.childDomNode.setAttribute("aria-label",c),$(this.childDomNode).tooltip({html:a,delay:o,placement:i,trigger:l},"DANGEROUS_tooltip_jquery_fn_deprecation_exempt")}},n.getRealText=function(){var t=this.attrs.text;return Array.isArray(t)?ot(t):t},n.checkDomNodeChanged=function(){var t=this.firstChild.dom;t&&!t.isSameNode(this.childDomNode)&&(this.childDomNode=t,this.shouldRecreateTooltip=!0)},e}(E),Nt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.value=this.attrs.value||"",this.disabled=!!this.attrs.disabled},n.view=function(){return m("div",{className:"TextEditor"},m("div",{className:"TextEditor-editorContainer"}),m("ul",{className:"TextEditor-controls Composer-footer"},H(this.controlItems().toArray()),m("li",{className:"TextEditor-toolbar"},this.toolbarItems().toArray())))},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.attrs.composer.editor=this.buildEditor(this.$(".TextEditor-editorContainer")[0])},n.onupdate=function(e){t.prototype.onupdate.call(this,e);var n=!!this.attrs.disabled;this.disabled!==n&&(this.disabled=n,this.attrs.composer.editor.disabled(n))},n.buildEditorParams=function(){var t=this;return{classNames:["FormControl","Composer-flexible","TextEditor-editor"],disabled:this.disabled,placeholder:this.attrs.placeholder||"",value:this.value,oninput:this.oninput.bind(this),inputListeners:[],onsubmit:function(){t.onsubmit(),m.redraw()}}},n.buildEditor=function(t){return new Ct(t,this.buildEditorParams())},n.controlItems=function(){var t=new B;return t.add("submit",m(yt,{icon:"fas fa-paper-plane",className:"Button Button--primary",itemClassName:"App-primaryControl",onclick:this.onsubmit.bind(this)},this.attrs.submitLabel)),this.attrs.preview&&t.add("preview",m(Et,{text:v.translator.trans("core.forum.composer.preview_tooltip")},m(yt,{icon:"far fa-eye",className:"Button Button--icon",onclick:this.attrs.preview}))),t},n.toolbarItems=function(){return new B},n.oninput=function(t){this.value=t,this.attrs.onchange(this.value)},n.onsubmit=function(){this.attrs.onsubmit(this.value)},e}(E),kt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),this.composer=this.attrs.composer,this.loading=!1,this.attrs.confirmExit&&this.composer.preventClosingWhen((function(){return n.hasChanges()}),this.attrs.confirmExit),this.composer.fields.content(this.attrs.originalContent||"")},n.view=function(){var t;return m(pt,{when:this.hasChanges.bind(this)},m("div",{className:M("ComposerBody",this.attrs.className)},Y(this.attrs.user,{className:"ComposerBody-avatar"}),m("div",{className:"ComposerBody-content"},m("ul",{className:"ComposerBody-header"},H(this.headerItems().toArray())),m("div",{className:"ComposerBody-editor"},m(Nt,{submitLabel:this.attrs.submitLabel,placeholder:this.attrs.placeholder,disabled:this.loading||this.attrs.disabled,composer:this.composer,preview:null==(t=this.jumpToPreview)?void 0:t.bind(this),onchange:this.composer.fields.content,onsubmit:this.onsubmit.bind(this),value:this.composer.fields.content()}))),m(ft,{display:"unset",containerClassName:M("ComposerBody-loading",this.loading&&"active"),size:"large"})))},n.hasChanges=function(){var t=this.composer.fields.content();return t&&t!==this.attrs.originalContent},n.headerItems=function(){return new B},n.onsubmit=function(){},n.loaded=function(){this.loading=!1,m.redraw()},e}(E);function St(t){Wi.composer.isFullScreen()&&(Wi.composer.minimize(),t.stopPropagation())}var Tt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.placeholder=e.placeholder||ot(Wi.translator.trans("core.forum.composer_reply.body_placeholder")),e.submitLabel=e.submitLabel||Wi.translator.trans("core.forum.composer_reply.submit_button"),e.confirmExit=e.confirmExit||ot(Wi.translator.trans("core.forum.composer_reply.discard_confirmation"))};var n=e.prototype;return n.headerItems=function(){var e=t.prototype.headerItems.call(this),n=this.attrs.discussion;return e.add("title",m("h3",null,nt("fas fa-reply")," ",m(G,{href:Wi.route.discussion(n),onclick:St},n.title()))),e},n.jumpToPreview=function(t){St(t),m.route.set(Wi.route.discussion(this.attrs.discussion,"reply"))},n.data=function(){return{content:this.composer.fields.content(),relationships:{discussion:this.attrs.discussion}}},n.onsubmit=function(){var t=this,e=this.attrs.discussion;this.loading=!0,m.redraw();var n=this.data();Wi.store.createRecord("posts").save(n).then((function(n){if(Wi.viewingDiscussion(e)){var r=Wi.current.get("stream");r.update().then((function(){return r.goToNumber(n.number())}))}else{var i,o=m(yt,{className:"Button Button--link",onclick:function(){m.route.set(Wi.route.post(n)),Wi.alerts.dismiss(i)}},Wi.translator.trans("core.forum.composer_reply.view_button"));i=Wi.alerts.show({type:"success",controls:[o]},Wi.translator.trans("core.forum.composer_reply.posted_message"))}t.composer.hide()}),this.loaded.bind(this))},e}(kt),Ft=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(t){var e=Object.assign({},this.attrs),n=z(e,"type");e.className=M("Alert","Alert--"+n,e.className);var r=z(e,"title"),i=z(e,"icon"),o=z(e,"content")||t.children,s=z(e,"controls")||[],a=z(e,"dismissible"),u=z(e,"ondismiss"),l=[];return(a||void 0===a)&&l.push(m(yt,{"aria-label":v.translator.trans("core.lib.alert.dismiss_a11y_label"),icon:"fas fa-times",className:"Button Button--link Button--icon Alert-dismiss",onclick:u})),m("div",e,!!r&&m("div",{className:"Alert-title"},!!i&&m("span",{className:"Alert-title-icon"},nt(i)),m("span",{className:"Alert-title-text"},r)),m("span",{className:"Alert-body"},o),m("ul",{className:"Alert-controls"},H(s.concat(l))))},e}(E),_t=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).loading=!1,e.alertAttrs=null,e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e);var r=[];["className","title","content","onsubmit"].forEach((function(t){n[t]||(n[t]=function(){},r.push(t))})),r.length>0&&mt("Modal `"+this.constructor.name+"` does not implement all abstract methods of the Modal super class. Missing methods: "+r.join(", ")+".")},n.oncreate=function(e){var n=this;t.prototype.oncreate.call(this,e),this.attrs.animateShow((function(){return n.onready()}))},n.onbeforeremove=function(e){if(t.prototype.onbeforeremove.call(this,e),!this.attrs.state.modal)return new Promise((function(t){return setTimeout(t,300)}))},n.view=function(){var t=this;return this.alertAttrs&&(this.alertAttrs.dismissible=!1),m("div",{className:M("Modal modal-dialog fade",this.className())},m("div",{className:"Modal-content"},this.dismissibleOptions.viaCloseButton&&m("div",{className:"Modal-close App-backControl"},m(yt,{icon:"fas fa-times",onclick:function(){return t.hide()},className:"Button Button--icon Button--link","aria-label":v.translator.trans("core.lib.modal.close")})),m("form",{onsubmit:this.onsubmit.bind(this)},m("div",{className:"Modal-header"},m("h3",{className:"App-titleControl App-titleControl--text"},this.title())),!!this.alertAttrs&&m("div",{className:"Modal-alert"},m(Ft,this.alertAttrs)),this.content())))},n.onsubmit=function(t){},n.onready=function(){this.$().find("input, select, textarea").first().trigger("focus").trigger("select")},n.hide=function(){this.attrs.animateHide()},n.loaded=function(){this.loading=!1,m.redraw()},n.onerror=function(t){var e;this.alertAttrs=t.alert,m.redraw(),422===t.status&&null!=(e=t.response)&&e.errors?this.$("form [name="+t.response.errors[0].source.pointer.replace("/data/attributes/","")+"]").trigger("select"):this.onready()},F(e,[{key:"dismissibleOptions",get:function(){return this.constructor.dismissibleOptions}}],[{key:"dismissibleOptions",get:function(){return this.isDismissible?{isDismissible:!0,viaCloseButton:this.isDismissibleViaCloseButton,viaEscKey:this.isDismissibleViaEscKey,viaBackdropClick:this.isDismissibleViaBackdropClick}:{isDismissible:!1,viaCloseButton:!1,viaEscKey:!1,viaBackdropClick:!1}}}]),e}(E);_t.isDismissible=!0,_t.isDismissibleViaCloseButton=!0,_t.isDismissibleViaEscKey=!0,_t.isDismissibleViaBackdropClick=!0;var Pt=n(30);const Bt=n.n(Pt)();var It=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).email=void 0,e.success=!1,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.email=Bt(this.attrs.email||"")},n.className=function(){return"ForgotPasswordModal Modal--small"},n.title=function(){return Wi.translator.trans("core.forum.forgot_password.title")},n.content=function(){return this.success?m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},m("p",{className:"helpText"},Wi.translator.trans("core.forum.forgot_password.email_sent_message")),m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",onclick:this.hide.bind(this)},Wi.translator.trans("core.forum.forgot_password.dismiss_button"))))):m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},m("p",{className:"helpText"},Wi.translator.trans("core.forum.forgot_password.text")),this.fields().toArray()))},n.fields=function(){var t=new B,e=ot(Wi.translator.trans("core.forum.forgot_password.email_placeholder"));return t.add("email",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"email",type:"email",placeholder:e,"aria-label":e,bidi:this.email,disabled:this.loading})),50),t.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.forgot_password.submit_button"))),-10),t},n.onsubmit=function(t){var e=this;t.preventDefault(),this.loading=!0,Wi.request({method:"POST",url:Wi.forum.attribute("apiUrl")+"/forgot",body:this.requestParams(),errorHandler:this.onerror.bind(this)}).then((function(){e.success=!0,e.alertAttrs=null})).catch((function(){})).then(this.loaded.bind(this))},n.requestParams=function(){return{email:this.email()}},n.onerror=function(e){404===e.status&&e.alert&&(e.alert.content=Wi.translator.trans("core.forum.forgot_password.not_found_message")),t.prototype.onerror.call(this,e)},e}(_t),Ot=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){return m("div",{className:"LogInButtons"},this.items().toArray())},n.items=function(){return new B},e}(E),Lt=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).username=void 0,e.email=void 0,e.password=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.username=Bt(this.attrs.username||""),this.email=Bt(this.attrs.email||""),this.password=Bt(this.attrs.password||"")},n.className=function(){return"Modal--small SignUpModal"},n.title=function(){return Wi.translator.trans("core.forum.sign_up.title")},n.content=function(){return[m("div",{className:"Modal-body"},this.body()),m("div",{className:"Modal-footer"},this.footer())]},n.isProvided=function(t){var e,n;return null!=(e=null==(n=this.attrs.provided)?void 0:n.includes(t))&&e},n.body=function(){return[!this.attrs.token&&m(Ot,null),m("div",{className:"Form Form--centered"},this.fields().toArray())]},n.fields=function(){var t=new B,e=ot(Wi.translator.trans("core.forum.sign_up.username_placeholder")),n=ot(Wi.translator.trans("core.forum.sign_up.email_placeholder")),r=ot(Wi.translator.trans("core.forum.sign_up.password_placeholder"));return t.add("username",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"username",type:"text",placeholder:e,"aria-label":e,bidi:this.username,disabled:this.loading||this.isProvided("username")})),30),t.add("email",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"email",type:"email",placeholder:n,"aria-label":n,bidi:this.email,disabled:this.loading||this.isProvided("email")})),20),this.attrs.token||t.add("password",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"password",type:"password",autocomplete:"new-password",placeholder:r,"aria-label":r,bidi:this.password,disabled:this.loading})),10),t.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.sign_up.submit_button"))),-10),t},n.footer=function(){return[m("p",{className:"SignUpModal-logIn"},Wi.translator.trans("core.forum.sign_up.log_in_text",{a:m("a",{onclick:this.logIn.bind(this)})}))]},n.logIn=function(){var t={identification:this.email()||this.username()};Wi.modal.show(Mt,t)},n.onready=function(){this.attrs.username&&!this.attrs.email?this.$("[name=email]").select():this.$("[name=username]").select()},n.onsubmit=function(t){t.preventDefault(),this.loading=!0;var e=this.submitData();Wi.request({url:Wi.forum.attribute("baseUrl")+"/register",method:"POST",body:e,errorHandler:this.onerror.bind(this)}).then((function(){return window.location.reload()}),this.loaded.bind(this))},n.submitData=function(){var t=this.attrs.token?{token:this.attrs.token}:{password:this.password()};return D({username:this.username(),email:this.email()},t)},e}(_t),Mt=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).identification=void 0,e.password=void 0,e.remember=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.identification=Bt(this.attrs.identification||""),this.password=Bt(this.attrs.password||""),this.remember=Bt(!!this.attrs.remember)},n.className=function(){return"LogInModal Modal--small"},n.title=function(){return Wi.translator.trans("core.forum.log_in.title")},n.content=function(){return[m("div",{className:"Modal-body"},this.body()),m("div",{className:"Modal-footer"},this.footer())]},n.body=function(){return[m(Ot,null),m("div",{className:"Form Form--centered"},this.fields().toArray())]},n.fields=function(){var t=new B,e=ot(Wi.translator.trans("core.forum.log_in.username_or_email_placeholder")),n=ot(Wi.translator.trans("core.forum.log_in.password_placeholder"));return t.add("identification",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"identification",type:"text",placeholder:e,"aria-label":e,bidi:this.identification,disabled:this.loading})),30),t.add("password",m("div",{className:"Form-group"},m("input",{className:"FormControl",name:"password",type:"password",autocomplete:"current-password",placeholder:n,"aria-label":n,bidi:this.password,disabled:this.loading})),20),t.add("remember",m("div",{className:"Form-group"},m("div",null,m("label",{className:"checkbox"},m("input",{type:"checkbox",bidi:this.remember,disabled:this.loading}),Wi.translator.trans("core.forum.log_in.remember_me_label")))),10),t.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.log_in.submit_button"))),-10),t},n.footer=function(){return m("[",null,m("p",{className:"LogInModal-forgotPassword"},m("a",{onclick:this.forgotPassword.bind(this)},Wi.translator.trans("core.forum.log_in.forgot_password_link"))),Wi.forum.attribute("allowSignUp")&&m("p",{className:"LogInModal-signUp"},Wi.translator.trans("core.forum.log_in.sign_up_text",{a:m("a",{onclick:this.signUp.bind(this)})})))},n.forgotPassword=function(){var t=this.identification(),e=t.includes("@")?{email:t}:void 0;Wi.modal.show(It,e)},n.signUp=function(){var t,e=this.identification(),n=((t={})[e.includes("@")?"email":"username"]=e,t);Wi.modal.show(Lt,n)},n.onready=function(){this.$("[name="+(this.identification()?"password":"identification")+"]").trigger("select")},n.onsubmit=function(t){t.preventDefault(),this.loading=!0,Wi.session.login(this.loginParams(),{errorHandler:this.onerror.bind(this)}).then((function(){return window.location.reload()}),this.loaded.bind(this))},n.loginParams=function(){return{identification:this.identification(),password:this.password(),remember:this.remember()}},n.onerror=function(e){401===e.status&&e.alert&&(e.alert.content=Wi.translator.trans("core.forum.log_in.invalid_login_message"),this.password("")),t.prototype.onerror.call(this,e)},e}(_t),$t=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).discussion=void 0,e.currentTitle=void 0,e.newTitle=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.discussion=this.attrs.discussion,this.currentTitle=this.attrs.currentTitle,this.newTitle=Bt(this.currentTitle)},n.className=function(){return"RenameDiscussionModal Modal--small"},n.title=function(){return Wi.translator.trans("core.forum.rename_discussion.title")},n.content=function(){return m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},m("div",{className:"Form-group"},m("input",{className:"FormControl",bidi:this.newTitle,type:"text"})),m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.rename_discussion.submit_button")))))},n.onsubmit=function(t){var e=this;t.preventDefault(),this.loading=!0;var n=this.newTitle(),r=this.currentTitle;if(n&&n!==r)return this.discussion.save({title:n}).then((function(){Wi.viewingDiscussion(e.discussion)&&Wi.current.get("stream").update(),m.redraw(),e.hide()})).catch((function(){e.loading=!1,m.redraw()}));this.hide()},e}(_t);const jt={controls:function(t,e){var n=this,r=new B;return["user","moderation","destructive"].forEach((function(i){var o=n[i+"Controls"](t,e).toArray();o.length&&(o.forEach((function(t){return r.add(t.itemName,t)})),r.add(i+"Separator",m(O,null)))})),r},userControls:function(t,e){var n=this,r=new B;return e instanceof ae&&r.add("reply",!Wi.session.user||t.canReply()?m(yt,{icon:"fas fa-reply",onclick:function(){return n.replyAction.bind(t)(!0,!1).catch((function(){}))}},Wi.translator.trans(Wi.session.user?"core.forum.discussion_controls.reply_button":"core.forum.discussion_controls.log_in_to_reply_button")):m(yt,{icon:"fas fa-reply",className:"disabled",title:ot(Wi.translator.trans("core.forum.discussion_controls.cannot_reply_text"))},Wi.translator.trans("core.forum.discussion_controls.cannot_reply_button"))),r},moderationControls:function(t){var e=new B;return t.canRename()&&e.add("rename",m(yt,{icon:"fas fa-pencil-alt",onclick:this.renameAction.bind(t)},Wi.translator.trans("core.forum.discussion_controls.rename_button"))),e},destructiveControls:function(t){var e=new B;return t.isHidden()?(t.canHide()&&e.add("restore",m(yt,{icon:"fas fa-reply",onclick:this.restoreAction.bind(t)},Wi.translator.trans("core.forum.discussion_controls.restore_button"))),t.canDelete()&&e.add("delete",m(yt,{icon:"fas fa-times",onclick:this.deleteAction.bind(t)},Wi.translator.trans("core.forum.discussion_controls.delete_forever_button")))):t.canHide()&&e.add("hide",m(yt,{icon:"far fa-trash-alt",onclick:this.hideAction.bind(t)},Wi.translator.trans("core.forum.discussion_controls.delete_button"))),e},replyAction:function(t,e){var n=this;return new Promise((function(r,i){return Wi.session.user?n.canReply()?(Wi.composer.composingReplyTo(n)&&!e||Wi.composer.load(Tt,{user:Wi.session.user,discussion:n}),Wi.composer.show(),t&&Wi.viewingDiscussion(n)&&!Wi.composer.isFullScreen()&&Wi.current.get("stream").goToNumber("reply"),r(Wi.composer)):i():(Wi.modal.show(Mt),i())}))},hideAction:function(){return this.pushData({attributes:{hiddenAt:new Date},relationships:{hiddenUser:Wi.session.user}}),this.save({isHidden:!0})},restoreAction:function(){return this.pushData({attributes:{hiddenAt:null},relationships:{hiddenUser:null}}),this.save({isHidden:!1})},deleteAction:function(){var t=this;if(confirm(ot(Wi.translator.trans("core.forum.discussion_controls.delete_confirmation"))))return Wi.viewingDiscussion(this)&&Wi.history.back(),this.delete().then((function(){return Wi.discussions.removeDiscussion(t)}))},renameAction:function(){return Wi.modal.show($t,{currentTitle:this.title(),discussion:this})}};function Rt(t){var e,n,r,i,o=$(t),s=!1,a=!1,u=0,l=function(t,e){var n;void 0===e&&(e={}),(n=e).duration||(n.duration="fast"),e.step=function(t){$(this).css("transform","translate("+t+"px, 0)")},o.find(".Slidable-content").animate({"background-position-x":t},e)},c=function(){l(0,{complete:function(){o.removeClass("sliding"),e.hide(),n.hide(),a=!1}})};return o.find(".Slidable-content").on("touchstart",(function(t){e=o.find(".Slidable-underneath--left:not(.disabled)"),n=o.find(".Slidable-underneath--right:not(.disabled)"),r=t.originalEvent.targetTouches[0].clientX,i=t.originalEvent.targetTouches[0].clientY,s=!0,u=0})).on("touchmove",(function(t){var l=t.originalEvent.targetTouches[0].clientX,c=t.originalEvent.targetTouches[0].clientY;if(s&&Math.abs(l-r)>Math.abs(c-i)&&(a=!0),s=!1,a){u=l-r;var d=function(t,e){if(t.length){var n="left"===e?u>0:u<0;n&&t.hasClass("Slidable-underneath--elastic")&&(u-=.5*u),t.toggle(n);var r=Math.max(0,Math.min(1,(Math.abs(u)-25)/50));t.find(".icon").css("transform","scale("+r+")")}else u=Math["left"===e?"min":"max"](0,u)};d(e,"left"),d(n,"right"),$(this).css("transform","translate("+u+"px, 0)"),$(this).css("background-position-x",u+"px"),o.toggleClass("sliding",!!u),t.preventDefault()}})).on("touchend",(function(){var t=function(t){t.click(),t.hasClass("Slidable-underneath--elastic")?c():l((u>0?1:-1)*o.width())};n.length&&u<-50?t(n):e.length&&u>50?t(e):c(),s=!1,a=!1})),{reset:c}}var Ht=/[.*+?^${}()|[\]\\]/g;function Ut(t){return t.replace(Ht,"\\$&")}var qt=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).subtree=void 0,e.highlightRegExp=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),this.subtree=new dt((function(){return n.attrs.discussion.freshness}),(function(){var t=Wi.session.user&&Wi.session.user.markedAllAsReadAt();return t&&t.getTime()}),(function(){return n.active()}))},n.elementAttrs=function(){return{className:M("DiscussionListItem",{active:this.active(),"DiscussionListItem--hidden":this.attrs.discussion.isHidden(),Slidable:"ontouchstart"in window})}},n.view=function(){var t=this.elementAttrs();return m("div",t,this.viewItems().toArray())},n.viewItems=function(){var t=new B,e=this.attrs.discussion,n=jt.controls(e,this).toArray();return t.add("controls",this.controlsView(n),100),t.add("slidableUnderneath",this.slidableUnderneathView(),90),t.add("content",this.contentView(),80),t},n.controlsView=function(t){return m("[",null,!!t.length&&m(ut,{icon:"fas fa-ellipsis-v",className:"DiscussionListItem-controls",buttonClassName:"Button Button--icon Button--flat Slidable-underneath Slidable-underneath--right",accessibleToggleLabel:Wi.translator.trans("core.forum.discussion_controls.toggle_dropdown_accessible_label")},t))},n.slidableUnderneathView=function(){var t=this.attrs.discussion.isUnread();return m("span",{className:M("Slidable-underneath Slidable-underneath--left Slidable-underneath--elastic",{disabled:!t}),onclick:this.markAsRead.bind(this)},nt("fas fa-check"))},n.contentView=function(){var t=this.attrs.discussion,e=t.isUnread(),n=t.isRead();return m("div",{className:M("DiscussionListItem-content","Slidable-content",{unread:e,read:n})},this.contentItems().toArray())},n.contentItems=function(){var t=new B;return t.add("authorAvatar",this.authorAvatarView(),100),t.add("badges",this.badgesView(),90),t.add("main",this.mainView(),80),t.add("replyCount",this.replyCountItem(),70),t},n.authorAvatarView=function(){var t=this.attrs.discussion,e=t.user();return m(Et,{text:Wi.translator.trans("core.forum.discussion_list.started_text",{user:e,ago:st(t.createdAt())}),position:"right"},m(G,{className:"DiscussionListItem-author",href:e?Wi.route.user(e):"#"},Y(e||null,{title:""})))},n.badgesView=function(){var t=this.attrs.discussion;return m("ul",{className:"DiscussionListItem-badges badges"},H(t.badges().toArray()))},n.mainView=function(){var t=this.attrs.discussion,e=this.getJumpTo();return m(G,{href:Wi.route.discussion(t,e),className:"DiscussionListItem-main"},this.mainItems().toArray())},n.mainItems=function(){var t=new B,e=this.attrs.discussion;return t.add("title",m("h2",{className:"DiscussionListItem-title"},et(e.title(),this.highlightRegExp)),100),t.add("info",m("ul",{className:"DiscussionListItem-info"},H(this.infoItems().toArray())),90),t},n.getJumpTo=function(){var t=this.attrs.discussion,e=0;if(this.attrs.params.q){var n=t.mostRelevantPost();n&&(e=n.number());var r=Ut(this.attrs.params.q);this.highlightRegExp=new RegExp(r+"|"+r.trim().replace(/\s+/g,"|"),"gi")}else{var i;e=Math.min(null!=(i=t.lastPostNumber())?i:0,(t.lastReadPostNumber()||0)+1)}return e},n.oncreate=function(e){if(t.prototype.oncreate.call(this,e),"ontouchstart"in window){var n=Rt(this.element);this.$(".DiscussionListItem-controls").on("hidden.bs.dropdown",(function(){return n.reset()}))}},n.onbeforeupdate=function(e){return t.prototype.onbeforeupdate.call(this,e),this.subtree.needsRebuild()},n.active=function(){return Wi.current.matches(ae,{discussion:this.attrs.discussion})},n.showFirstPost=function(){var t;return["newest","oldest"].includes(null!=(t=this.attrs.params.sort)?t:"")},n.showRepliesCount=function(){return"replies"===this.attrs.params.sort},n.markAsRead=function(){var t=this.attrs.discussion;t.isUnread()&&(t.save({lastReadPostNumber:t.lastPostNumber()}),m.redraw())},n.infoItems=function(){var t=new B;if(this.attrs.params.q){var e=this.attrs.discussion.mostRelevantPost()||this.attrs.discussion.firstPost();if(e&&"comment"===e.contentType()){var n,r=et(null!=(n=e.contentPlain())?n:"",this.highlightRegExp,175);t.add("excerpt",r,-100)}}else t.add("terminalPost",m(ct,{discussion:this.attrs.discussion,lastPost:!this.showFirstPost()}));return t},n.replyCountItem=function(){var t=this.attrs.discussion;return!this.showRepliesCount()&&t.isUnread()?m("button",{className:"Button--ua-reset DiscussionListItem-count",onclick:this.markAsRead.bind(this)},m("span",{"aria-hidden":"true"},at(t.unreadCount())),m("span",{className:"visually-hidden"},Wi.translator.trans("core.forum.discussion_list.unread_replies_a11y_label",{count:t.replyCount()}))):m("span",{className:"DiscussionListItem-count"},m("span",{"aria-hidden":"true"},at(t.replyCount())),m("span",{className:"visually-hidden"},Wi.translator.trans("core.forum.discussion_list.total_replies_a11y_label",{count:t.replyCount()})))},e}(E),zt=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){return m("div",{className:"Placeholder"},m("p",null,this.attrs.text))},e}(E),Vt=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){var t,e=this.attrs.state,n=e.getParams(),r=e.isInitialLoading()||e.isLoadingNext();if(r?t=m(ft,null):e.hasNext()&&(t=m(yt,{className:"Button",onclick:e.loadNext.bind(e)},Wi.translator.trans("core.forum.discussion_list.load_more_button"))),e.isEmpty()){var i=Wi.translator.trans("core.forum.discussion_list.empty_text");return m("div",{className:"DiscussionList"},m(zt,{text:i}))}var o=e.pageSize;return m("div",{className:M("DiscussionList",{"DiscussionList--searchResults":e.isSearchResults()})},m("ul",{role:"feed","aria-busy":r,className:"DiscussionList-discussions"},e.getPages().map((function(t,e){return t.items.map((function(t,r){return m("li",{key:t.id(),"data-id":t.id(),role:"article","aria-setsize":"-1","aria-posinset":e*o+r},m(qt,{discussion:t,params:n}))}))}))),m("div",{className:"DiscussionList-loadMore"},t))},e}(E),Wt=function(t){t.pageX<10&&Wi.pane.show()},Gt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){if(this.attrs.state.hasItems())return m("aside",{className:"DiscussionPage-list"},this.enoughSpace()&&m(Vt,{state:this.attrs.state}))},n.oncreate=function(e){t.prototype.oncreate.call(this,e);var n=$(e.dom),r=Wi.pane;if(n.hover(r.show.bind(r),r.onmouseleave.bind(r)),$(document).on("mousemove",Wt),Wi.previous.matches(ae)){var i=Wi.cache.discussionListPaneScrollTop||0;n.scrollTop(i)}else{var o=n.find(".DiscussionListItem.active");if(o.length){var s=n.offset().top,a=s+n.outerHeight(),u=o.offset().top,l=u+o.outerHeight();(u<s||l>a)&&n.scrollTop(n.scrollTop()-s+u)}}},n.onremove=function(t){Wi.cache.discussionListPaneScrollTop=$(t.dom).scrollTop(),$(document).off("mousemove",Wt)},n.enoughSpace=function(){return!$(".App-navigation").is(":visible")},e}(E),Yt=function(){function t(t){this.callback=t,this.ticking=!1}var e=t.prototype;return e.loop=function(){var t=this;this.ticking||(requestAnimationFrame((function(){t.update(),t.ticking=!1})),this.ticking=!0)},e.update=function(){this.callback(window.pageYOffset)},e.start=function(){this.active||window.addEventListener("scroll",this.active=this.loop.bind(this),{passive:!0})},e.stop=function(){window.removeEventListener("scroll",this.active),this.active=null},t}(),Kt=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){return m("div",{className:"Post CommentPost LoadingPost"},m("header",{className:"Post-header"},Y(null,{className:"PostUser-avatar"}),m("div",{className:"fakeText"})),m("div",{className:"Post-body"},m("div",{className:"fakeText"}),m("div",{className:"fakeText"}),m("div",{className:"fakeText"})))},e}(E);function Xt(t){var e=t&&t.displayName()||v.translator.trans("core.lib.username.deleted_text");return m("span",{className:"username"},e)}var Jt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(t){t.className=t.className||"",t.surround=t.surround||function(t){return t()}};var n=e.prototype;return n.view=function(){return m("div",{className:this.attrs.className})},n.oncreate=function(e){var n,r=this;t.prototype.oncreate.call(this,e);var i=function(){if(r.attrs.composer.isVisible()){var t=r.attrs.composer.fields.content();n!==t&&(n=t,r.attrs.surround((function(){return s9e.TextFormatter.preview(n||"",e.dom)})))}};i(),this.updateInterval=setInterval(i,50)},n.onremove=function(e){t.prototype.onremove.call(this,e),clearInterval(this.updateInterval)},e}(E),Qt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=this;return Wi.composer.composingReplyTo(this.attrs.discussion)?m("article",{className:"Post CommentPost editing","aria-busy":"true"},m("header",{className:"Post-header"},m("div",{className:"PostUser"},m("h3",{className:"PostUser-name"},Y(Wi.session.user,{className:"PostUser-avatar"}),Xt(Wi.session.user)),m("ul",{className:"PostUser-badges badges"},H(Wi.session.user.badges().toArray())))),m(Jt,{className:"Post-body",composer:Wi.composer,surround:this.anchorPreview.bind(this)})):m("button",{className:"Post ReplyPlaceholder",onclick:function(){jt.replyAction.call(t.attrs.discussion,!0).catch((function(){}))}},m("span",{className:"Post-header"},Y(Wi.session.user,{className:"PostUser-avatar"})," ",Wi.translator.trans("core.forum.post_stream.reply_placeholder")))},n.anchorPreview=function(t){var e=$(window).scrollTop()+$(window).height()>=$(document).height();t(),e&&$(window).scrollTop($(document).height())},e}(E),Zt=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.discussion=this.attrs.discussion,this.stream=this.attrs.stream,this.scrollListener=new Yt(this.onscroll.bind(this))},n.view=function(){var t,e=this,n=this.stream.viewingEnd(),r=this.stream.posts(),i=this.discussion.postIds(),o=function(t){$(t.dom).addClass("fadeIn"),setTimeout((function(){return $(t.dom).removeClass("fadeIn")}),500)},s=r.map((function(n,r){var s,a={"data-index":e.stream.visibleStart+r};if(n){var u=n.createdAt(),l=Wi.postComponents[n.contentType()];s=!!l&&m(l,{post:n}),a.key="post"+n.id(),a.oncreate=o,a["data-time"]=u.toISOString(),a["data-number"]=n.number(),a["data-id"]=n.id(),a["data-type"]=n.contentType();var c=u-t;c>3456e5&&(s=[m("div",{className:"PostStream-timeGap"},m("span",null,Wi.translator.trans("core.forum.post_stream.time_lapsed_text",{period:dayjs().add(c,"ms").fromNow(!0)}))),s]),t=u}else a.key="post"+i[e.stream.visibleStart+r],s=m(Kt,null);var d=m("div",Object.assign({className:"PostStream-item"},a),s);return 0===r&&e.afterFirstPostItems().toArray().length>0?m.fragment(D({},a),[d,m("div",{className:"PostStream-item PostStream-afterFirstPost",key:"afterFirstPost"},e.afterFirstPostItems().toArray())]):d}));return!n&&r[this.stream.visibleEnd-this.stream.visibleStart-1]&&s.push(m("div",{className:"PostStream-loadMore",key:"loadMore"},m(yt,{className:"Button",onclick:this.stream.loadNext.bind(this.stream)},Wi.translator.trans("core.forum.post_stream.load_more_button")))),n&&s.push.apply(s,this.endItems().toArray()),!n||Wi.session.user&&!this.discussion.canReply()||s.push(m("div",{className:"PostStream-item",key:"reply","data-index":this.stream.count(),oncreate:o},m(Qt,{discussion:this.discussion}))),m("div",{className:"PostStream",role:"feed","aria-live":"off","aria-busy":this.stream.pagesLoading?"true":"false"},s)},n.afterFirstPostItems=function(){return new B},n.endItems=function(){return new B},n.onupdate=function(e){t.prototype.onupdate.call(this,e),this.triggerScroll()},n.oncreate=function(e){var n=this;t.prototype.oncreate.call(this,e),this.triggerScroll(),setTimeout((function(){return n.scrollListener.start()}))},n.onremove=function(e){t.prototype.onremove.call(this,e),this.scrollListener.stop(),clearTimeout(this.calculatePositionTimeout)},n.triggerScroll=function(){if(this.stream.needsScroll){var t=this.stream.targetPost;this.stream.needsScroll=!1,"number"in t?this.scrollToNumber(t.number,this.stream.animateScroll):"index"in t&&this.scrollToIndex(t.index,this.stream.animateScroll,t.reply)}},n.onscroll=function(t){void 0===t&&(t=window.pageYOffset),this.stream.paused||this.stream.pagesLoading||(this.updateScrubber(t),this.loadPostsIfNeeded(t),clearTimeout(this.calculatePositionTimeout),this.calculatePositionTimeout=setTimeout(this.calculatePosition.bind(this,t),100))},n.loadPostsIfNeeded=function(t){void 0===t&&(t=window.pageYOffset);var e=this.getMarginTop(),n=$(window).height()-e,r=t+e;if(this.stream.visibleStart>0){var i=this.$(".PostStream-item[data-index="+this.stream.visibleStart+"]");i.length&&i.offset().top>r-300&&this.stream.loadPrevious()}if(this.stream.visibleEnd<this.stream.count()){var o=this.$(".PostStream-item[data-index="+(this.stream.visibleEnd-1)+"]");o.length&&o.offset().top+o.outerHeight(!0)<r+n+300&&this.stream.loadNext()}},n.updateScrubber=function(t){void 0===t&&(t=window.pageYOffset);var e=this.getMarginTop(),n=$(window).height()-e,r=t+e,i=this.$(".PostStream-item[data-index]"),o=0,s="",a=null;i.each((function(){var t=$(this),e=t.offset().top,i=t.outerHeight(!0);if(e+i<r)return!0;if(e>r+n)return!1;var u=Math.max(0,r-e),l=Math.min(i,r+n-e)-u;null===a&&(a=parseFloat(t.data("index"))+u/i),l>0&&(o+=l/i);var c=t.data("time");c&&(s=c)})),this.stream.index=null!==a?a+1:this.stream.count(),this.stream.visible=o,s&&(this.stream.description=dayjs(s).format(ot(Wi.translator.trans("core.lib.datetime_formats.scrubber"))))},n.calculatePosition=function(t){void 0===t&&(t=window.pageYOffset);var e,n,r=this.getMarginTop(),i=$(window),o=i.height()-r,s=i.scrollTop()+r,a=t+r;this.$(".PostStream-item").each((function(){var t=$(this),r=t.offset().top,i=t.outerHeight(!0),u=Math.max(0,a-r);if(void 0===e&&(u/i<.75||(i-u)/o>.25)&&(e=t.data("number")),r+i>s){if(!(r+i<s+o))return!1;t.data("number")&&(n=t.data("number"))}})),e&&this.attrs.onPositionChange(e||1,n,e)},n.getMarginTop=function(){var t="phone"===Wi.screen()?"#app-navigation":"#header";return this.$()&&$(t).outerHeight()+parseInt(this.$().css("margin-top"),10)},n.scrollToNumber=function(t,e){var n=this.$(".PostStream-item[data-number="+t+"]");return this.scrollToItem(n,e).then(this.flashItem.bind(this,n))},n.scrollToIndex=function(t,e,n){var r=n?$(".PostStream-item:last-child"):this.$(".PostStream-item[data-index="+t+"]");this.scrollToItem(r,e,!0,n),n&&this.flashItem(r)},n.scrollToItem=function(t,e,n,r){var i=this,o=$("html, body").stop(!0),s=t.data("index");if(t.length){var a=t.offset().top-this.getMarginTop(),u=t.offset().top+t.height(),l=$(document).scrollTop(),c=l+$(window).height();if(n||a<l||u>c){var d=r?u-$(window).height()+Wi.composer.computedHeight():t.is(":first-child")?0:a;e?d!==l&&o.animate({scrollTop:d},"fast"):o.scrollTop(d)}}var h=function(){i.updateScrubber(),void 0!==s&&(i.stream.index=s+1)};return h(),this.stream.forceUpdateScrubber=!0,Promise.all([o.promise(),this.stream.loadPromise]).then((function(){var t;if(m.redraw.sync(),r){var e=$(".PostStream-item:last-child");$(window).scrollTop(e.offset().top+e.height()-$(window).height()+Wi.composer.computedHeight())}else 0===s?$(window).scrollTop(0):(t=$(".PostStream-item[data-index="+s+"]").offset())&&$(window).scrollTop(t.top-i.getMarginTop());h(),i.calculatePosition(),i.stream.paused=!1,i.loadPostsIfNeeded()}))},n.flashItem=function(t){t.removeClass("fadeIn"),t.addClass("flash").on("animationend webkitAnimationEnd",(function(e){t.removeClass("flash")}))},e}(E);function te(t,e){return void 0===e&&(e=v.data.locale),new Intl.NumberFormat(e).format(t)}var ee=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.stream=this.attrs.stream,this.handlers={},this.scrollListener=new Yt(this.updateScrubberValues.bind(this,{fromScroll:!0,forceHeightChange:!0}))},n.view=function(){var t=this.stream.count(),e=Wi.translator.trans("core.forum.post_scrubber.viewing_text",{count:t,index:m("span",{className:"Scrubber-index"}),formattedCount:m("span",{className:"Scrubber-count"},te(t))}),n=this.stream.discussion.unreadCount(),r=t?Math.min(t-this.stream.index,n)/t:0;function i(t){var e=$(t.dom),n={top:100-100*r+"%",height:100*r+"%",opacity:r?1:0};t.state.oldStyle?e.stop(!0).css(t.state.oldStyle).animate(n):e.css(n),t.state.oldStyle=n}var o=["PostStreamScrubber","Dropdown"];return this.attrs.className&&o.push(this.attrs.className),m("div",{className:o.join(" ")},m("button",{className:"Button Dropdown-toggle","data-toggle":"dropdown"},e," ",nt("fas fa-sort")),m("div",{className:"Dropdown-menu dropdown-menu"},m("div",{className:"Scrubber"},m("a",{className:"Scrubber-first",onclick:this.goToFirst.bind(this)},nt("fas fa-angle-double-up")," ",this.firstPostLabel()),m("div",{className:"Scrubber-scrollbar"},m("div",{className:"Scrubber-before"}),m("div",{className:"Scrubber-handle"},m("div",{className:"Scrubber-bar"}),m("div",{className:"Scrubber-info"},m("strong",null,e),m("span",{className:"Scrubber-description"}))),m("div",{className:"Scrubber-after"}),m("div",{className:"Scrubber-unread",oncreate:i,onupdate:i},this.unreadLabel(n))),m("a",{className:"Scrubber-last",onclick:this.goToLast.bind(this)},nt("fas fa-angle-double-down")," ",this.lastPostLabel()))))},n.firstPostLabel=function(){return Wi.translator.trans("core.forum.post_scrubber.original_post_link")},n.unreadLabel=function(t){return Wi.translator.trans("core.forum.post_scrubber.unread_text",{count:t})},n.lastPostLabel=function(){return Wi.translator.trans("core.forum.post_scrubber.now_link")},n.onupdate=function(e){var n=this;t.prototype.onupdate.call(this,e),this.stream.forceUpdateScrubber&&(this.stream.forceUpdateScrubber=!1,this.stream.loadPromise.then((function(){return n.updateScrubberValues({animate:!0,forceHeightChange:!0})})))},n.oncreate=function(e){var n=this;t.prototype.oncreate.call(this,e),$(window).on("resize",this.handlers.onresize=this.onresize.bind(this)).resize(),this.$(".Scrubber-scrollbar").bind("click",this.onclick.bind(this)).bind("dragstart mousedown touchstart",(function(t){return t.preventDefault()})),this.dragging=!1,this.mouseStart=0,this.indexStart=0,this.$(".Scrubber-handle").bind("mousedown touchstart",this.onmousedown.bind(this)).click((function(t){return t.stopPropagation()})),$(document).on("mousemove touchmove",this.handlers.onmousemove=this.onmousemove.bind(this)).on("mouseup touchend",this.handlers.onmouseup=this.onmouseup.bind(this)),setTimeout((function(){return n.scrollListener.start()})),this.stream.loadPromise.then((function(){return n.updateScrubberValues({animate:!1,forceHeightChange:!0})}))},n.onremove=function(e){t.prototype.onremove.call(this,e),this.scrollListener.stop(),$(window).off("resize",this.handlers.onresize),$(document).off("mousemove touchmove",this.handlers.onmousemove).off("mouseup touchend",this.handlers.onmouseup)},n.updateScrubberValues=function(t){var e=this;void 0===t&&(t={});var n=this.stream.index,r=this.stream.count(),i=this.stream.visible||1,o=this.percentPerPost(),s=this.$();s.find(".Scrubber-index").text(te(this.stream.sanitizeIndex(Math.max(1,n)))),s.find(".Scrubber-description").text(this.stream.description),s.toggleClass("disabled",this.stream.disabled());var a={};if(a.before=Math.max(0,o.index*Math.min(n-1,r-i)),a.handle=Math.min(100-a.before,o.visible*i),a.after=100-a.before-a.handle,!(t.fromScroll&&this.stream.paused||this.adjustingHeight&&!t.forceHeightChange)){var u=t.animate?"animate":"css";this.adjustingHeight=!0;var l=[];for(var c in a){var d=s.find(".Scrubber-"+c);l.push(d.stop(!0,!0)[u]({height:a[c]+"%"},"fast").promise()),"animate"===u&&d.css("overflow","visible")}Promise.all(l).then((function(){return e.adjustingHeight=!1}))}},n.goToFirst=function(){this.stream.goToFirst(),this.updateScrubberValues({animate:!0,forceHeightChange:!0})},n.goToLast=function(){this.stream.goToLast(),this.updateScrubberValues({animate:!0,forceHeightChange:!0})},n.onresize=function(){var t=this.$(),e=this.$(".Scrubber-scrollbar");e.css("max-height",$(window).height()-t.offset().top+$(window).scrollTop()-parseInt($("#app").css("padding-bottom"),10)-(t.outerHeight()-e.outerHeight()))},n.onmousedown=function(t){t.redraw=!1,this.mouseStart=t.clientY||t.originalEvent.touches[0].clientY,this.indexStart=this.stream.index,this.dragging=!0,$("body").css("cursor","move"),this.$().toggleClass("dragging",this.dragging)},n.onmousemove=function(t){if(this.dragging){var e=((t.clientY||t.originalEvent.touches[0].clientY)-this.mouseStart)/this.$(".Scrubber-scrollbar").outerHeight()*100/this.percentPerPost().index||0,n=Math.min(this.indexStart+e,this.stream.count()-1);this.stream.index=Math.max(0,n),this.updateScrubberValues()}},n.onmouseup=function(){if(this.$().toggleClass("dragging",this.dragging),this.dragging){this.mouseStart=0,this.indexStart=0,this.dragging=!1,$("body").css("cursor",""),this.$().removeClass("open");var t=Math.floor(this.stream.index);this.stream.goToIndex(t)}},n.onclick=function(t){var e=this.$(".Scrubber-scrollbar"),n=((t.pageY||t.originalEvent.touches[0].pageY)-e.offset().top+$("body").scrollTop())/e.outerHeight()*100,r=(n-=parseFloat(e.find(".Scrubber-handle")[0].style.height)/2)/this.percentPerPost().index;r=Math.max(0,Math.min(this.stream.count()-1,r)),this.stream.goToIndex(Math.floor(r)),this.updateScrubberValues({animate:!0,forceHeightChange:!0}),this.$().removeClass("open")},n.percentPerPost=function(){var t=this.stream.count()||1,e=this.stream.visible||1,n=50/this.$(".Scrubber-scrollbar").outerHeight()*100,r=Math.max(100/t,n/e);return{index:t===e?0:(100-r*e)/(t-e),visible:r}},e}(E),ne=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.className=M(e.className,"Dropdown--split"),e.menuClassName=M(e.menuClassName,"Dropdown-menu--right")};var n=e.prototype;return n.getButton=function(t){var e=this.getFirstChild(t),n=Object.assign({},null==e?void 0:e.attrs);return n.className=M(n.className,"SplitDropdown-button Button",this.attrs.buttonClassName),m("[",null,m(yt,n,e.children),m("button",{className:"Dropdown-toggle Button Button--icon "+this.attrs.buttonClassName,"aria-haspopup":"menu","aria-label":this.attrs.accessibleToggleLabel,"data-toggle":"dropdown"},this.attrs.icon?nt(this.attrs.icon,{className:"Button-icon"}):null,nt("fas fa-caret-down",{className:"Button-caret"})))},n.getFirstChild=function(t){for(var e=t;e instanceof Array;)e=e[0];return e},e}(ut);function re(t,e,n,r){var i,o=!1,s=0;function a(){i&&clearTimeout(i)}function u(){for(var u=arguments.length,l=new Array(u),c=0;c<u;c++)l[c]=arguments[c];var d=this,h=Date.now()-s;function f(){s=Date.now(),n.apply(d,l)}function p(){i=void 0}o||(r&&!i&&f(),a(),void 0===r&&h>t?f():!0!==e&&(i=setTimeout(r?p:f,void 0===r?t-h:t)))}return"boolean"!=typeof e&&(r=n,n=e,e=void 0),u.cancel=function(){a(),o=!0},u}function ie(t,e,n){return void 0===n?re(t,e,!1):re(t,n,!1!==e)}function oe(t,e){var n=$(window),r=$(t).offset().top-n.scrollTop();e(),n.scrollTop($(t).offset().top-r)}var se=function(){function t(t,e){void 0===e&&(e=[]),this.discussion=void 0,this.paused=!1,this.loadPageTimeouts={},this.pagesLoading=0,this.index=0,this.number=1,this.visible=1,this.visibleStart=0,this.visibleEnd=0,this.animateScroll=!1,this.needsScroll=!1,this.targetPost=null,this.description="",this.forceUpdateScrubber=!1,this.loadPromise=null,this.loadNext=void 0,this.loadPrevious=void 0,this.discussion=t,this.loadNext=re(300,this._loadNext),this.loadPrevious=re(300,this._loadPrevious),this.show(e)}var e=t.prototype;return e.update=function(){return this.viewingEnd()?(this.visibleEnd=this.count(),this.loadRange(this.visibleStart,this.visibleEnd)):Promise.resolve()},e.goToFirst=function(){return this.goToIndex(0)},e.goToLast=function(){return this.goToIndex(this.count()-1,!0)},e.goToNumber=function(t,e){if(void 0===e&&(e=!1),"reply"===t){var n=this.goToLast();return this.targetPost=D({},this.targetPost,{reply:!0}),n}return this.paused=!0,this.loadPromise=this.loadNearNumber(t),this.needsScroll=!0,this.targetPost={number:t},this.animateScroll=!e,this.number=t,this.loadPromise.then((function(){return m.redraw()}))},e.goToIndex=function(t,e){return void 0===e&&(e=!1),this.paused=!0,this.loadPromise=this.loadNearIndex(t),this.needsScroll=!0,this.targetPost={index:t},this.animateScroll=!e,this.index=t,m.redraw(),this.loadPromise},e.loadNearNumber=function(t){return this.posts().some((function(e){return e&&Number(e.number())===Number(t)}))?Promise.resolve():(this.reset(),Wi.store.find("posts",{filter:{discussion:this.discussion.id()},page:{near:t}}).then(this.show.bind(this)))},e.loadNearIndex=function(e){if(e>=this.visibleStart&&e<this.visibleEnd)return Promise.resolve();var n=this.sanitizeIndex(e-t.loadCount/2),r=n+t.loadCount;return this.reset(n,r),this.loadRange(n,r).then(this.show.bind(this))},e._loadNext=function(){var e=this.visibleEnd,n=this.visibleEnd=this.sanitizeIndex(this.visibleEnd+t.loadCount),r=e-2*t.loadCount;r>this.visibleStart&&r>=0&&(this.visibleStart=r+t.loadCount+1,this.loadPageTimeouts[r]&&(clearTimeout(this.loadPageTimeouts[r]),delete this.loadPageTimeouts[r],this.pagesLoading--)),this.loadPage(e,n)},e._loadPrevious=function(){var e=this.visibleStart,n=this.visibleStart=this.sanitizeIndex(this.visibleStart-t.loadCount),r=n+2*t.loadCount;r<this.visibleEnd&&r<=this.count()&&(this.visibleEnd=r,this.loadPageTimeouts[r]&&(clearTimeout(this.loadPageTimeouts[r]),delete this.loadPageTimeouts[r],this.pagesLoading--)),this.loadPage(n,e,!0)},e.loadPage=function(t,e,n){var r=this;void 0===n&&(n=!1),this.pagesLoading++;var i=function(){t<r.visibleStart||e>r.visibleEnd||oe('.PostStream-item[data-index="'+(n?r.visibleEnd-1:r.visibleStart)+'"]',m.redraw.sync)};i(),this.loadPageTimeouts[t]=setTimeout((function(){r.loadRange(t,e).then((function(){i(),r.pagesLoading--})),delete r.loadPageTimeouts[t]}),this.pagesLoading-1?1e3:0)},e.loadRange=function(t,e){var n=[],r=[];return this.discussion.postIds().slice(t,e).forEach((function(t){var e=Wi.store.getById("posts",t);e&&e.discussion()&&void 0!==e.canEdit()?r.push(e):n.push(t)})),n.length?Wi.store.find("posts",n).then((function(t){return r.concat(t).sort((function(t,e){return t.number()-e.number()}))})):Promise.resolve(r)},e.show=function(t){var e;this.visibleStart=t.length?this.discussion.postIds().indexOf(null!=(e=t[0].id())?e:"0"):0,this.visibleEnd=this.sanitizeIndex(this.visibleStart+t.length)},e.reset=function(e,n){this.visibleStart=e||0,this.visibleEnd=this.sanitizeIndex(n||t.loadCount)},e.posts=function(){return this.discussion.postIds().slice(this.visibleStart,this.visibleEnd).map((function(t){var e=Wi.store.getById("posts",t);return e&&e.discussion()&&void 0!==e.canEdit()?e:null}))},e.count=function(){return this.discussion.postIds().length},e.disabled=function(){return this.visible>=this.count()},e.viewingEnd=function(){return Math.abs(this.count()-this.visibleEnd)<=1},e.sanitizeIndex=function(t){return Math.max(0,Math.min(this.count(),Math.floor(t)))},t}();se.loadCount=20;var ae=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).discussion=null,e.stream=null,e.near=0,e.useBrowserScrollRestoration=!0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n,r;t.prototype.oninit.call(this,e),this.load(),(Wi.discussions.hasItems()||Wi.discussions.isLoading())&&(null==(n=Wi.pane)||n.enable(),null==(r=Wi.pane)||r.hide()),this.bodyClass="App--discussion"},n.onremove=function(e){var n,r,i;t.prototype.onremove.call(this,e),null==(n=Wi.pane)||n.disable(),!this.discussion||!Wi.composer.composingReplyTo(this.discussion)||null!=(r=Wi.composer)&&null!=(i=r.fields)&&i.content()?Wi.composer.minimize():Wi.composer.hide()},n.view=function(){return m("div",{className:"DiscussionPage"},this.viewItems().toArray())},n.viewItems=function(){var t=new B;return t.add("pane",m(Gt,{state:Wi.discussions}),100),t.add("discussions",m("div",{className:"DiscussionPage-discussion"},this.discussion?this.pageContent().toArray():this.loadingItems().toArray()),90),t},n.loadingItems=function(){var t=new B;return t.add("spinner",m(ft,null),100),t},n.sidebar=function(){return m("nav",{className:"DiscussionPage-nav"},m("ul",null,H(this.sidebarItems().toArray())))},n.hero=function(){return m(U,{discussion:this.discussion})},n.pageContent=function(){var t=new B;return t.add("hero",this.hero(),100),t.add("main",m("div",{className:"container"},this.mainContent().toArray()),10),t},n.mainContent=function(){var t=new B;return t.add("sidebar",this.sidebar(),100),t.add("poststream",m("div",{className:"DiscussionPage-stream"},m(Zt,{discussion:this.discussion,stream:this.stream,onPositionChange:this.positionChanged.bind(this)})),10),t},n.load=function(){var t=Wi.preloadedApiDocument();if(t)setTimeout(this.show.bind(this,t),0);else{var e=this.requestParams();Wi.store.find("discussions",m.route.param("id"),e).then(this.show.bind(this))}m.redraw()},n.requestParams=function(){return{bySlug:!0,page:{near:this.near}}},n.show=function(t){var e,n,r=this;Wi.history.push("discussion",t.title()),Wi.setTitle(t.title()),Wi.setTitleCount(0);var i=[];if(t.payload&&t.payload.included){var o=t.id();i=t.payload.included.filter((function(t){return"posts"===t.type&&t.relationships&&t.relationships.discussion&&!Array.isArray(t.relationships.discussion.data)&&t.relationships.discussion.data.id===o})).map((function(t){return Wi.store.getById("posts",t.id)})).sort((function(t,e){return t.number()-e.number()})).slice(0,20)}this.stream=new se(t,i);var s=m.route.param("near"),a="reply"===s?"reply":parseInt(s);this.stream.goToNumber(a||(null!=(e=null==(n=i[0])?void 0:n.number())?e:0),!0).then((function(){r.discussion=t,Wi.current.set("discussion",t),Wi.current.set("stream",r.stream)}))},n.sidebarItems=function(){var t=new B;return this.discussion&&t.add("controls",m(ne,{icon:"fas fa-ellipsis-v",className:"App-primaryControl",buttonClassName:"Button--primary",accessibleToggleLabel:Wi.translator.trans("core.forum.discussion_controls.toggle_dropdown_accessible_label")},jt.controls(this.discussion,this).toArray()),100),t.add("scrubber",m(ee,{stream:this.stream,className:"App-titleControl"}),-100),t},n.positionChanged=function(t,e){var n=this.discussion;if(n){var r=Wi.route.discussion(n,this.near=t);window.history.replaceState(null,document.title,r),Wi.history.push("discussion",n.title()),Wi.session.user&&e>(n.lastReadPostNumber()||0)&&(n.save({lastReadPostNumber:e}),m.redraw())}},e}(S),ue=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){return m("ul",{className:"Header-controls"},H(this.items().toArray()))},n.items=function(){return new B},e}(E),le=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.active=this.isActive(e),void 0===e.force&&(e.force=!0)},e.prototype.view=function(e){var n=t.prototype.view.call(this,e);return n.tag=G,n.attrs.active=String(n.attrs.active),delete n.attrs.type,n},e.isActive=function(t){var e,n;return void 0!==t.active?t.active:(null==(e=m.route.get())?void 0:e.split("?")[0])===(null==(n=t.href)?void 0:n.split("?")[0])},e}(yt),ce=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.className="SessionDropdown",e.buttonClassName="Button Button--user Button--flat",e.menuClassName="Dropdown-menu--right",e.accessibleToggleLabel=ot(Wi.translator.trans("core.forum.header.session_dropdown_accessible_label"))};var n=e.prototype;return n.view=function(e){return t.prototype.view.call(this,D({},e,{children:this.items().toArray()}))},n.getButtonContent=function(){var t=Wi.session.user;return[Y(t)," ",m("span",{className:"Button-label"},Xt(t))]},n.items=function(){var t=new B,e=Wi.session.user;return t.add("profile",m(le,{icon:"fas fa-user",href:Wi.route.user(e)},Wi.translator.trans("core.forum.header.profile_button")),100),t.add("settings",m(le,{icon:"fas fa-cog",href:Wi.route("settings")},Wi.translator.trans("core.forum.header.settings_button")),50),Wi.forum.attribute("adminUrl")&&t.add("administration",m(le,{icon:"fas fa-wrench",href:Wi.forum.attribute("adminUrl"),target:"_blank"},Wi.translator.trans("core.forum.header.admin_button")),0),t.add("separator",m(O,null),-90),t.add("logOut",m(yt,{icon:"fas fa-sign-out-alt",onclick:Wi.session.logout.bind(Wi.session)},Wi.translator.trans("core.forum.header.log_out_button")),-100),t},e}(ut);function de(t){if(!t||"object"!=typeof t||t instanceof Array)return!1;var e=t.tag;return("string"!=typeof e||"a"===e||"button"===e)&&("object"!=typeof e&&"function"!=typeof e||!("initAttrs"in e)||e.initAttrs(t.attrs),"object"!=typeof e&&"function"!=typeof e||!("isActive"in e)?t.attrs.active:e.isActive(t.attrs))}var he=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){null!=e.caretIcon||(e.caretIcon="fas fa-sort"),t.initAttrs.call(this,e),e.className=M(e.className,"Dropdown--select")},e.prototype.getButtonContent=function(t){var e=t.find(de),n=e&&"object"==typeof e&&"children"in e&&e.children||this.attrs.defaultLabel;return Array.isArray(n)&&(n=n[0]),[m("span",{className:"Button-label"},n),this.attrs.caretIcon?nt(this.attrs.caretIcon,{className:"Button-caret"}):null]},e}(ut),fe=function(){function t(t,e){void 0===t&&(t={}),void 0===e&&(e=v.store),this.data={},this.freshness=new Date,this.exists=!1,this.store=void 0,this.data=t,this.store=e}var e=t.prototype;return e.id=function(){return"id"in this.data?this.data.id:void 0},e.attribute=function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){var e,n;return null==(e=this.data)||null==(n=e.attributes)?void 0:n[t]})),e.pushData=function(e){if("id"in e&&(this.data.id=e.id),"type"in e&&(this.data.type=e.type),"attributes"in e){var n;for(var r in(n=this.data).attributes||(n.attributes={}),e.attributes){var i=e.attributes[r];i&&i instanceof t&&(vt("Providing models as attributes to `Model.pushData()` or `Model.pushAttributes()` is deprecated.","3249"),delete e.attributes[r],e.relationships||(e.relationships={}),e.relationships[r]={data:t.getIdentifier(i)})}Object.assign(this.data.attributes,e.attributes)}if("relationships"in e){var o,s=null!=(o=this.data.relationships)?o:{};for(var a in e.relationships){var u=e.relationships[a];if(null!==u){var l;l=u instanceof t?{data:t.getIdentifier(u)}:u instanceof Array?{data:u.map(t.getIdentifier)}:u,e.relationships[a]=l,s[a]=l}else delete s[a],delete e.relationships[a]}this.data.relationships=s}return this.freshness=new Date,this},e.pushAttributes=function(t){this.pushData({attributes:t})},e.save=function(e,n){var r=this;void 0===n&&(n={});var i={type:this.data.type,attributes:e};if("id"in this.data&&(i.id=this.data.id),e.relationships){for(var o in i.relationships={},e.relationships){var s=e.relationships[o];null!==s&&(i.relationships[o]={data:s instanceof Array?s.map(t.getIdentifier):t.getIdentifier(s)})}delete e.relationships}var a=this.copyData();this.pushData(i);var u={data:i,meta:n.meta||void 0};return v.request(D({method:this.exists?"PATCH":"POST",url:v.forum.attribute("apiUrl")+this.apiEndpoint(),body:u},n)).then((function(t){return r.store.pushPayload(t)}),(function(t){throw r.pushData(a),m.redraw(),t}))},e.delete=function(t,e){var n=this;return void 0===t&&(t={}),void 0===e&&(e={}),this.exists?v.request(D({method:"DELETE",url:v.forum.attribute("apiUrl")+this.apiEndpoint(),body:t},e)).then((function(){n.exists=!1,n.store.remove(n)})):Promise.resolve()},e.apiEndpoint=function(){return"/"+this.data.type+("id"in this.data?"/"+this.data.id:"")},e.copyData=function(){return JSON.parse(JSON.stringify(this.data))},e.rawRelationship=function(t){var e,n;return null==(e=this.data.relationships)||null==(n=e[t])?void 0:n.data},t.attribute=function(t,e){return function(){return e?e(this.attribute(t)):this.attribute(t)}},t.hasOne=function(t){return function(){var e,n,r=null==(e=this.data.relationships)||null==(n=e[t])?void 0:n.data;if(r&&r instanceof Array)throw new Error("Relationship "+t+" on model "+this.data.type+" is plural, so the hasOne method cannot be used to access it.");return!!r&&this.store.getById(r.type,r.id)}},t.hasMany=function(t){return function(){var e,n,r=this,i=null==(e=this.data.relationships)||null==(n=e[t])?void 0:n.data;if(i&&!(i instanceof Array))throw new Error("Relationship "+t+" on model "+this.data.type+" is singular, so the hasMany method cannot be used to access it.");return!!i&&i.map((function(t){return r.store.getById(t.type,t.id)}))}},t.transformDate=function(t){return null!=t?new Date(t):t},t.getIdentifier=function(t){return t&&"id"in t.data?{type:t.data.type,id:t.data.id}:null},t}();function pe(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var r,i=e.slice(0,-1),o=e.slice(-1)[0],s={};return function(){var t=this,e=!1;return i.forEach((function(n){var r=t[n],i="function"==typeof r?r.call(t):r;s[n]!==i&&(e=!0,s[n]=i)})),e&&(r=o.apply(this,i.map((function(t){return s[t]})))),r}}function me(t){if(!t||t.length<4)return!1;var e=t.replace("#","");return 3===e.length&&(e+=e),(299*parseInt(e.slice(0,2),16)+587*parseInt(e.slice(2,4),16)+114*parseInt(e.slice(4,6),16))/1e3<(parseInt(getComputedStyle(document.body).getPropertyValue("--yiq-threshold").trim())||128)}function ve(t){return t?me(t)?"text-contrast--light":"text-contrast--dark":"text-contrast--unchanged"}var ge,ye=["type","icon","label","color","style"],be=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){var t=this.attrs,e=t.type,n=t.icon,r=t.label,i=t.color,o=t.style,s=void 0===o?{}:o,a=q(t,ye),u=M("Badge",[e&&"Badge--"+e],a.className,ve(i)),l=n?nt(n,{className:"Badge-icon"}):m.trust("&nbsp;"),c=D({},s,{"--badge-bg":i}),d=D({},a,{className:u,style:c}),h=m("div",d,l);return r?m(Et,{text:r},h):h},e}(E),we=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.title=function(){return fe.attribute("title").call(this)},n.slug=function(){return fe.attribute("slug").call(this)},n.createdAt=function(){return fe.attribute("createdAt",fe.transformDate).call(this)},n.user=function(){return fe.hasOne("user").call(this)},n.firstPost=function(){return fe.hasOne("firstPost").call(this)},n.lastPostedAt=function(){return fe.attribute("lastPostedAt",fe.transformDate).call(this)},n.lastPostedUser=function(){return fe.hasOne("lastPostedUser").call(this)},n.lastPost=function(){return fe.hasOne("lastPost").call(this)},n.lastPostNumber=function(){return fe.attribute("lastPostNumber").call(this)},n.commentCount=function(){return fe.attribute("commentCount").call(this)},n.replyCount=function(){return pe("commentCount",(function(t){var e;return Math.max(0,(null!=(e=t)?e:0)-1)})).call(this)},n.posts=function(){return fe.hasMany("posts").call(this)},n.mostRelevantPost=function(){return fe.hasOne("mostRelevantPost").call(this)},n.lastReadAt=function(){return fe.attribute("lastReadAt",fe.transformDate).call(this)},n.lastReadPostNumber=function(){return fe.attribute("lastReadPostNumber").call(this)},n.isUnread=function(){return pe("unreadCount",(function(t){return!!t})).call(this)},n.isRead=function(){return pe("unreadCount",(function(t){return!(!v.session.user||t)})).call(this)},n.hiddenAt=function(){return fe.attribute("hiddenAt",fe.transformDate).call(this)},n.hiddenUser=function(){return fe.hasOne("hiddenUser").call(this)},n.isHidden=function(){return pe("hiddenAt",(function(t){return!!t})).call(this)},n.canReply=function(){return fe.attribute("canReply").call(this)},n.canRename=function(){return fe.attribute("canRename").call(this)},n.canHide=function(){return fe.attribute("canHide").call(this)},n.canDelete=function(){return fe.attribute("canDelete").call(this)},n.removePost=function(t){var e=this.rawRelationship("posts");e&&e.some((function(n,r){return t===n.id&&(e.splice(r,1),!0)}))},n.unreadCount=function(){var t,e,n,r=v.session.user;if(r&&(null!=(t=null==(e=r.markedAllAsReadAt())?void 0:e.getTime())?t:0)<(null==(n=this.lastPostedAt())?void 0:n.getTime())){var i,o,s=Math.max(0,(null!=(i=this.lastPostNumber())?i:0)-(this.lastReadPostNumber()||0));return Math.min(s,null!=(o=this.commentCount())?o:0)}return 0},n.badges=function(){var t=new B;return this.isHidden()&&t.add("hidden",m(be,{type:"hidden",icon:"fas fa-trash",label:v.translator.trans("core.lib.badge.hidden_tooltip")})),t},n.postIds=function(){var t,e;return null!=(t=null==(e=this.rawRelationship("posts"))?void 0:e.map((function(t){return t.id})))?t:[]},e}(fe),De=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=this.attrs.state;return m("div",{className:"NotificationList"},m("div",{className:"NotificationList-header"},m("h4",{className:"App-titleControl App-titleControl--text"},Wi.translator.trans("core.forum.notifications.title")),m("div",{className:"App-primaryControl"},this.controlItems().toArray())),m("div",{className:"NotificationList-content"},this.content(t)))},n.controlItems=function(){var t=new B,e=this.attrs.state;return t.add("mark_all_as_read",m(Et,{text:Wi.translator.trans("core.forum.notifications.mark_all_as_read_tooltip")},m(yt,{className:"Button Button--link","data-container":".NotificationList",icon:"fas fa-check",title:Wi.translator.trans("core.forum.notifications.mark_all_as_read_tooltip"),onclick:e.markAllAsRead.bind(e)})),70),t.add("delete_all",m(Et,{text:Wi.translator.trans("core.forum.notifications.delete_all_tooltip")},m(yt,{className:"Button Button--link","data-container":".NotificationList",icon:"fas fa-trash-alt",title:Wi.translator.trans("core.forum.notifications.delete_all_tooltip"),onclick:function(){confirm(Wi.translator.trans("core.forum.notifications.delete_all_confirm"))&&e.deleteAll.call(e)}})),50),t},n.content=function(t){return t.isLoading()?m(ft,{className:"LoadingIndicator--block"}):t.hasItems()?t.getPages().map((function(t){var e=[],n={};return t.items.forEach((function(t){var r=t.subject();if(void 0!==r){var i=null;r instanceof we?i=r:r&&r.discussion&&(i=r.discussion());var o=i?i.id():0;n[o]=n[o]||{discussion:i,notifications:[]},n[o].notifications.push(t),-1===e.indexOf(n[o])&&e.push(n[o])}})),e.map((function(t){var e=t.discussion&&t.discussion.badges().toArray();return m("div",{className:"NotificationGroup"},t.discussion?m(G,{className:"NotificationGroup-header",href:Wi.route.discussion(t.discussion)},e&&!!e.length&&m("ul",{className:"NotificationGroup-badges badges"},H(e)),m("span",null,t.discussion.title())):m("div",{className:"NotificationGroup-header"},Wi.forum.attribute("title")),m("ul",{className:"NotificationGroup-content"},t.notifications.map((function(t){var e=Wi.notificationComponents[t.contentType()];return!!e&&m("li",null,m(e,{notification:t}))}))))}))})):m("div",{className:"NotificationList-empty"},Wi.translator.trans("core.forum.notifications.empty_text"))},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.$notifications=this.$(".NotificationList-content"),this.$scrollParent=this.inPanel()?this.$notifications:$(window),this.boundScrollHandler=this.scrollHandler.bind(this),this.$scrollParent.on("scroll",this.boundScrollHandler)},n.onremove=function(e){t.prototype.onremove.call(this,e),this.$scrollParent.off("scroll",this.boundScrollHandler)},n.scrollHandler=function(){var t=this.attrs.state,e=this.inPanel()?this.$scrollParent[0]:document.documentElement,n=Math.abs(e.scrollHeight-e.scrollTop-e.clientHeight)<=1;t.hasNext()&&!t.isLoadingNext()&&n&&t.loadNext()},n.inPanel=function(){return"auto"===this.$notifications.css("overflow")},e}(E),xe=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){e.className||(e.className="NotificationsDropdown"),e.buttonClassName||(e.buttonClassName="Button Button--flat"),e.menuClassName||(e.menuClassName="Dropdown-menu--right"),e.label||(e.label=ot(Wi.translator.trans("core.forum.notifications.tooltip"))),e.icon||(e.icon="fas fa-bell"),e.accessibleToggleLabel||(e.accessibleToggleLabel=ot(Wi.translator.trans("core.forum.notifications.toggle_dropdown_accessible_label"))),t.initAttrs.call(this,e)};var n=e.prototype;return n.getButton=function(e){var n=this.getNewCount(),r=t.prototype.getButton.call(this,e);return r.attrs.title=this.attrs.label,r.attrs.className=M(r.attrs.className,[n&&"new"]),r.attrs.onclick=this.onclick.bind(this),r},n.getButtonContent=function(){var t=this.getUnreadCount();return[this.attrs.icon?nt(this.attrs.icon,{className:"Button-icon"}):null,0!==t&&m("span",{className:"NotificationsDropdown-unread"},t),m("span",{className:"Button-label"},this.attrs.label)]},n.getMenu=function(){return m("div",{className:M("Dropdown-menu",this.attrs.menuClassName),onclick:this.menuClick.bind(this)},this.showing&&m(De,{state:this.attrs.state}))},n.onclick=function(){Wi.drawer.isOpen()?this.goToRoute():this.attrs.state.load()},n.goToRoute=function(){m.route.set(Wi.route("notifications"))},n.getUnreadCount=function(){return Wi.session.user.unreadNotificationCount()},n.getNewCount=function(){return Wi.session.user.newNotificationCount()},n.menuClick=function(t){(t.shiftKey||t.metaKey||t.ctrlKey||1===t.button)&&t.stopPropagation()},e}(ut);!function(t){t[t.Enter=13]="Enter",t[t.Escape=27]="Escape",t[t.Space=32]="Space",t[t.ArrowUp=38]="ArrowUp",t[t.ArrowDown=40]="ArrowDown",t[t.ArrowLeft=37]="ArrowLeft",t[t.ArrowRight=39]="ArrowRight",t[t.Tab=9]="Tab",t[t.Backspace=8]="Backspace"}(ge||(ge={}));var Ce=function(){function t(){this.callbacks=new Map,this.whenCallback=function(t){return!0}}var e=t.prototype;return e.onUp=function(t){return this.callbacks.set(ge.ArrowUp,(function(e){e.preventDefault(),t(e)})),this},e.onDown=function(t){return this.callbacks.set(ge.ArrowDown,(function(e){e.preventDefault(),t(e)})),this},e.onSelect=function(t,e){void 0===e&&(e=!1);var n=function(e){e.preventDefault(),t(e)};return e||this.callbacks.set(ge.Tab,n),this.callbacks.set(ge.Enter,n),this},e.onTab=function(t){return this.callbacks.set(9,(function(e){e.preventDefault(),t(e)})),this},e.onCancel=function(t){return this.callbacks.set(ge.Escape,(function(e){e.stopPropagation(),e.preventDefault(),t(e)})),this},e.onRemove=function(t){return this.callbacks.set(ge.Backspace,(function(e){e instanceof KeyboardEvent&&e.target instanceof HTMLInputElement&&0===e.target.selectionStart&&0===e.target.selectionEnd&&(t(e),e.preventDefault())})),this},e.when=function(t){return this.whenCallback=t,this},e.bindTo=function(t){t[0].addEventListener("keydown",this.navigate.bind(this))},e.navigate=function(t){if(this.whenCallback(t)){var e=this.callbacks.get(t.which);e&&e(t)}},t}();function Ae(t,e,n,r,i,o,s){try{var a=t[o](s),u=a.value}catch(t){return void n(t)}a.done?e(u):Promise.resolve(u).then(r,i)}function Ee(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function s(t){Ae(o,r,i,s,a,"next",t)}function a(t){Ae(o,r,i,s,a,"throw",t)}s(void 0)}))}}var Ne=n(2507),ke=n.n(Ne),Se=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).query=void 0,e.discussion=void 0,e.mostRelevantPost=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.query=this.attrs.query,this.discussion=this.attrs.discussion,this.mostRelevantPost=this.attrs.mostRelevantPost},n.view=function(){return m("li",{className:"DiscussionSearchResult","data-index":"discussions"+this.discussion.id()},m(G,{href:Wi.route.discussion(this.discussion,this.mostRelevantPost&&this.mostRelevantPost.number()||0)},this.viewItems().toArray()))},n.discussionTitle=function(){return this.discussion.title()},n.mostRelevantPostContent=function(){var t;return null==(t=this.mostRelevantPost)?void 0:t.contentPlain()},n.viewItems=function(){var t,e=new B;return e.add("discussion-title",m("div",{className:"DiscussionSearchResult-title"},et(this.discussionTitle(),this.query)),90),this.mostRelevantPost&&e.add("most-relevant",m("div",{className:"DiscussionSearchResult-excerpt"},et(null!=(t=this.mostRelevantPostContent())?t:"",this.query,100)),80),e},e}(E),Te=function(){function t(){this.results=new Map,this.queryString=null}var e=t.prototype;return e.search=function(){var t=Ee(ke().mark((function t(e){var n,r=this;return ke().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=e.toLowerCase(),this.results.set(e,[]),this.setQueryString(e),n={filter:{q:this.queryString||e},page:{limit:this.limit()},include:this.includes().join(",")},t.abrupt("return",Wi.store.find("discussions",n).then((function(t){r.results.set(e,t),m.redraw()})));case 5:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),e.view=function(t){t=t.toLowerCase();var e=(this.results.get(t)||[]).map((function(e){var n=e.mostRelevantPost();return m(Se,{query:t,discussion:e,mostRelevantPost:n})}));return[m("li",{className:"Dropdown-header"},Wi.translator.trans("core.forum.search.discussions_heading")),m("li",null,m(le,{icon:"fas fa-search",href:Wi.route("index",{q:this.queryString})},Wi.translator.trans("core.forum.search.all_discussions_button",{query:t})))].concat(e)},e.includes=function(){return["mostRelevantPost"]},e.limit=function(){return 3},e.queryMutators=function(){return[]},e.setQueryString=function(t){this.queryString=t+" "+this.queryMutators().join(" ")},t}(),Fe=function(){function t(){this.results=new Map}var e=t.prototype;return e.search=function(){var t=Ee(ke().mark((function t(e){var n=this;return ke().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Wi.store.find("users",{filter:{q:e},page:{limit:5}}).then((function(t){n.results.set(e,t),m.redraw()})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),e.view=function(t){t=t.toLowerCase();var e=(this.results.get(t)||[]).concat(Wi.store.all("users").filter((function(e){return[e.username(),e.displayName()].some((function(e){return e.toLowerCase().substr(0,t.length)===t}))}))).filter((function(t,e,n){return n.lastIndexOf(t)===e})).sort((function(t,e){return t.displayName().localeCompare(e.displayName())}));return e.length?[m("li",{className:"Dropdown-header"},Wi.translator.trans("core.forum.search.users_heading"))].concat(e.map((function(e){var n=Xt(e),r=[et(n.text,t)];return m("li",{className:"UserSearchResult","data-index":"users"+e.id()},m(G,{href:Wi.route.user(e)},Y(e),D({},n,{text:void 0,children:r})))}))):[]},t}(),_e=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).searchState=void 0,e.hasFocus=!1,e.sources=void 0,e.loadingSources=0,e.index=0,e.navigator=void 0,e.searchTimeout=void 0,e.updateMaxHeightHandler=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.searchState=this.attrs.state},n.view=function(){var t=this,e=this.searchState.getInitialSearch();if(this.sources||(this.sources=this.sourceItems().toArray()),!this.sources.length)return m("div",null);var n=ot(Wi.translator.trans("core.forum.header.search_placeholder")),r=!!e,i=!(!this.searchState.getValue()||!this.hasFocus),o=!(this.loadingSources||!this.searchState.getValue());return m("div",{role:"search","aria-label":Wi.translator.trans("core.forum.header.search_role_label"),className:M("Search",{open:this.searchState.getValue()&&this.hasFocus,focused:this.hasFocus,active:r,loading:!!this.loadingSources})},m("div",{className:"Search-input"},m("input",{"aria-label":n,className:"FormControl",type:"search",placeholder:n,value:this.searchState.getValue(),oninput:function(e){var n;return t.searchState.setValue(null==e||null==(n=e.target)?void 0:n.value)},onfocus:function(){return t.hasFocus=!0},onblur:function(){return t.hasFocus=!1}}),!!this.loadingSources&&m(ft,{size:"small",display:"inline",containerClassName:"Button Button--icon Button--link"}),o&&m("button",{className:"Search-clear Button Button--icon Button--link",onclick:this.clear.bind(this),"aria-label":Wi.translator.trans("core.forum.header.search_clear_button_accessible_label"),type:"button"},nt("fas fa-times-circle"))),m("ul",{className:"Dropdown-menu Search-results","aria-hidden":!i||void 0,"aria-live":i?"polite":void 0},i&&this.sources.map((function(e){return e.view(t.searchState.getValue())}))))},n.updateMaxHeight=function(){var t,e,n=window.innerHeight-this.element.querySelector(".Search-input>.FormControl").getBoundingClientRect().bottom-14;null==(t=this.element.querySelector(".Search-results"))||null==(e=t.style)||e.setProperty("max-height",n+"px")},n.onupdate=function(e){var n;t.prototype.onupdate.call(this,e),this.setIndex(this.getCurrentNumericIndex()),null!=(n=this.sources)&&n.length&&this.updateMaxHeight()},n.oncreate=function(e){var n,r=this;if(t.prototype.oncreate.call(this,e),null!=(n=this.sources)&&n.length){var i=this,o=this.searchState;this.setIndex(this.getCurrentNumericIndex()),this.$(".Search-results").on("mousedown",(function(t){return t.preventDefault()})).on("click",(function(){return r.$("input").trigger("blur")})).on("mouseenter","> li:not(.Dropdown-header)",(function(){i.setIndex(i.selectableItems().index(this))}));var s=this.$("input");this.navigator=new Ce,this.navigator.onUp((function(){return r.setIndex(r.getCurrentNumericIndex()-1,!0)})).onDown((function(){return r.setIndex(r.getCurrentNumericIndex()+1,!0)})).onSelect(this.selectResult.bind(this),!0).onCancel(this.clear.bind(this)).bindTo(s),s.on("input focus",(function(){var t=this.value.toLowerCase();t&&(i.searchTimeout&&clearTimeout(i.searchTimeout),i.searchTimeout=window.setTimeout((function(){var e;o.isCached(t)||(t.length>=i.constructor.MIN_SEARCH_LEN&&(null==(e=i.sources)||e.map((function(e){e.search&&(i.loadingSources++,e.search(t).then((function(){i.loadingSources=Math.max(0,i.loadingSources-1),m.redraw()})))}))),o.cache(t),m.redraw())}),250))})).on("focus",(function(){$(this).one("mouseup",(function(t){return t.preventDefault()})).trigger("select")})),this.updateMaxHeightHandler=this.updateMaxHeight.bind(this),window.addEventListener("resize",this.updateMaxHeightHandler)}},n.onremove=function(e){t.prototype.onremove.call(this,e),this.updateMaxHeightHandler&&window.removeEventListener("resize",this.updateMaxHeightHandler)},n.selectResult=function(){this.searchTimeout&&clearTimeout(this.searchTimeout),this.loadingSources=0;var t=this.getItem(this.index).find("a").attr("href");this.searchState.getValue()&&t?m.route.set(t):this.clear(),this.$("input").blur()},n.clear=function(){this.searchState.clear()},n.sourceItems=function(){var t=new B;return Wi.forum.attribute("canViewForum")&&t.add("discussions",new Te),Wi.forum.attribute("canSearchUsers")&&t.add("users",new Fe),t},n.selectableItems=function(){return this.$(".Search-results > li:not(.Dropdown-header)")},n.getCurrentNumericIndex=function(){return Math.max(0,this.selectableItems().index(this.getItem(this.index)))},n.getItem=function(t){var e=this.selectableItems(),n=e.filter('[data-index="'+t+'"]');return n.length||(n=e.eq(t)),n},n.setIndex=function(t,e){void 0===e&&(e=!1);var n=this.selectableItems(),r=n.parent(),i=t;t<0?i=n.length-1:t>=n.length&&(i=0);var o=n.removeClass("active").eq(i).addClass("active");if(this.index=parseInt(o.attr("data-index"))||i,e){var s,a=r.scrollTop(),u=r.offset().top,l=u+r.outerHeight(),c=o.offset().top,d=c+o.outerHeight();c<u?s=a-u+c-parseInt(r.css("padding-top"),10):d>l&&(s=a-l+d+parseInt(r.css("padding-bottom"),10)),void 0!==s&&r.stop(!0).animate({scrollTop:s},100)}},F(e,[{key:"state",get:function(){return vt("`state` property of the Search component is deprecated","3212"),this.searchState},set:function(t){void 0!==t&&vt("`state` property of the Search component is deprecated","3212"),this.searchState=t}}]),e}(E);_e.MIN_SEARCH_LEN=3;var Pe=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){return m("ul",{className:"Header-controls"},H(this.items().toArray()))},n.items=function(){var t=new B;if(t.add("search",m(_e,{state:Wi.search}),30),Wi.forum.attribute("showLanguageSelector")&&Object.keys(Wi.data.locales).length>1){var e=[],n=function(t){e.push(m(yt,{active:Wi.data.locale===t,icon:Wi.data.locale!==t||"fas fa-check",onclick:function(){Wi.session.user?Wi.session.user.savePreferences({locale:t}).then((function(){return window.location.reload()})):(document.cookie="locale="+t+"; path=/; expires=Tue, 19 Jan 2038 03:14:07 GMT",window.location.reload())}},Wi.data.locales[t]))};for(var r in Wi.data.locales)n(r);t.add("locale",m(he,{buttonClassName:"Button Button--link",accessibleToggleLabel:Wi.translator.trans("core.forum.header.locale_dropdown_accessible_label")},e),20)}return Wi.session.user?(t.add("notifications",m(xe,{state:Wi.notifications}),10),t.add("session",m(ce,null),0)):(Wi.forum.attribute("allowSignUp")&&t.add("signUp",m(yt,{className:"Button Button--link",onclick:function(){return Wi.modal.show(Lt)}},Wi.translator.trans("core.forum.header.sign_up_link")),10),t.add("logIn",m(yt,{className:"Button Button--link",onclick:function(){return Wi.modal.show(Mt)}},Wi.translator.trans("core.forum.header.log_in_link")),0)),t},e}(E),Be=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.className=e.className||"Button Button--icon Button--link"},e}(yt),Ie=function(){function t(){this.position=t.Position.HIDDEN,this.height=null,this.body={attrs:{}},this.editor=null,this.clear()}var e=t.prototype;return e.load=function(t,e){var n={componentClass:t,attrs:e};this.preventExit()||(this.isVisible()&&(this.clear(),m.redraw.sync()),this.body=n)},e.clear=function(){this.position=t.Position.HIDDEN,this.body={attrs:{}},this.onExit=null,this.fields={content:Bt("")},this.editor&&this.editor.destroy(),this.editor=null},e.show=function(){this.position!==t.Position.NORMAL&&this.position!==t.Position.FULLSCREEN&&(this.position=t.Position.NORMAL,m.redraw.sync())},e.hide=function(){this.clear(),m.redraw()},e.close=function(){this.preventExit()||this.hide()},e.minimize=function(){this.isVisible()&&(this.position=t.Position.MINIMIZED,m.redraw())},e.fullScreen=function(){this.isVisible()&&(this.position=t.Position.FULLSCREEN,m.redraw())},e.exitFullScreen=function(){this.position===t.Position.FULLSCREEN&&(this.position=t.Position.NORMAL,m.redraw())},e.bodyMatches=function(t,e){var n=this;return void 0===e&&(e={}),!!N(this.body.componentClass,t)&&Object.keys(e).every((function(t){return n.body.attrs[t]===e[t]}))},e.isVisible=function(){return this.position!==t.Position.HIDDEN},e.isFullScreen=function(){return this.position===t.Position.FULLSCREEN||"phone"===Wi.screen()},e.composingReplyTo=function(t){return this.isVisible()&&this.bodyMatches(Tt,{discussion:t})},e.preventExit=function(){if(this.isVisible()&&this.onExit)return this.onExit.callback()?!confirm(this.onExit.message):void 0},e.preventClosingWhen=function(t,e){this.onExit={callback:t,message:e}},e.minimumHeight=function(){return 200},e.maximumHeight=function(){return $(window).height()-$("#header").outerHeight()},e.computedHeight=function(){return this.position===t.Position.MINIMIZED?"":this.position===t.Position.FULLSCREEN?$(window).height():Math.max(this.minimumHeight(),Math.min(this.height,this.maximumHeight()))},t}();Ie.Position={HIDDEN:"hidden",NORMAL:"normal",MINIMIZED:"minimized",FULLSCREEN:"fullScreen"};const Oe=Ie;var Le=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.state=this.attrs.state,this.active=!1,this.prevPosition=this.state.position},n.view=function(){var t=this.state.body,e={normal:this.state.position===Oe.Position.NORMAL,minimized:this.state.position===Oe.Position.MINIMIZED,fullScreen:this.state.position===Oe.Position.FULLSCREEN,active:this.active,visible:this.state.isVisible()},n=this.state.position===Oe.Position.MINIMIZED?this.state.show.bind(this.state):void 0,r=t.componentClass;return m("div",{className:"Composer "+M(e)},m("div",{className:"Composer-handle",oncreate:this.configHandle.bind(this)}),m("ul",{className:"Composer-controls"},H(this.controlItems().toArray())),m("div",{className:"Composer-content",onclick:n},r&&m(r,Object.assign({},t.attrs,{composer:this.state,disabled:e.minimized}))))},n.onupdate=function(e){t.prototype.onupdate.call(this,e),this.state.position===this.prevPosition?this.updateHeight():(this.animatePositionChange(),this.prevPosition=this.state.position)},n.oncreate=function(e){var n=this;t.prototype.oncreate.call(this,e),this.initializeHeight(),this.$().hide().css("bottom",-this.state.computedHeight()),this.$().on("focus blur",":input,.TextEditor-editorContainer",(function(t){n.active="focusin"===t.type,m.redraw()})),this.$().on("keydown",":input,.TextEditor-editorContainer","esc",(function(){return n.state.close()})),this.handlers={},$(window).on("resize",this.handlers.onresize=this.updateHeight.bind(this)).resize(),$(document).on("mousemove",this.handlers.onmousemove=this.onmousemove.bind(this)).on("mouseup",this.handlers.onmouseup=this.onmouseup.bind(this))},n.onremove=function(e){t.prototype.onremove.call(this,e),$(window).off("resize",this.handlers.onresize),$(document).off("mousemove",this.handlers.onmousemove).off("mouseup",this.handlers.onmouseup)},n.configHandle=function(t){var e=this;$(t.dom).css("cursor","row-resize").bind("dragstart mousedown",(function(t){return t.preventDefault()})).mousedown((function(t){e.mouseStart=t.clientY,e.heightStart=e.$().height(),e.handle=$(this),$("body").css("cursor","row-resize")}))},n.onmousemove=function(t){if(this.handle){var e=this.mouseStart-t.clientY;this.changeHeight(this.heightStart+e);var n=$(window).scrollTop(),r=n>0&&n+$(window).height()>=$(document).height();this.updateBodyPadding(r)}},n.onmouseup=function(){this.handle&&(this.handle=null,$("body").css("cursor",""))},n.focus=function(){this.$(".Composer-content :input:enabled:visible, .TextEditor-editor").first().focus()},n.updateHeight=function(){var t=this.state.computedHeight(),e=this.$(".Composer-flexible");if(this.$().height(t),e.length){var n=e.offset().top-this.$().offset().top,r=parseInt(e.css("padding-bottom"),10),i=this.$(".Composer-footer").outerHeight(!0);e.height(this.$().outerHeight()-n-r-i)}},n.updateBodyPadding=function(){var t=this.state.position!==Oe.Position.HIDDEN&&this.state.position!==Oe.Position.MINIMIZED&&"phone"!==Wi.screen()?this.state.computedHeight()-parseInt($("#app").css("padding-bottom"),10):0;$("#content").css({paddingBottom:t})},n.animatePositionChange=function(){if(this.prevPosition!==Oe.Position.FULLSCREEN||this.state.position!==Oe.Position.NORMAL)switch(this.state.position){case Oe.Position.HIDDEN:return this.hide();case Oe.Position.MINIMIZED:return this.minimize();case Oe.Position.FULLSCREEN:return this.focus();case Oe.Position.NORMAL:return this.show()}else this.focus()},n.animateHeightChange=function(){var t=this.$().stop(!0),e=t.outerHeight(),n=$(window).scrollTop();t.show(),this.updateHeight();var r=t.outerHeight();this.prevPosition===Oe.Position.HIDDEN?t.css({bottom:-r,height:r}):t.css({height:e});var i=t.animate({bottom:0,height:r},"fast").promise();return this.updateBodyPadding(),$(window).scrollTop(n),i},n.showBackdrop=function(){this.$backdrop=$("<div/>").addClass("composer-backdrop").appendTo("body")},n.hideBackdrop=function(){this.$backdrop&&this.$backdrop.remove()},n.show=function(){var t=this;if(this.animateHeightChange().then((function(){return t.focus()})),"phone"===Wi.screen()){var e=document.documentElement,n=Math.min(e.scrollTop,e.scrollHeight-e.clientHeight);this.$().css("top",$(".App").is(".mobile-safari")?n:0),this.showBackdrop()}},n.hide=function(){var t=this,e=this.$();e.stop(!0).animate({bottom:-e.height()},"fast",(function(){e.hide(),t.hideBackdrop(),t.updateBodyPadding()}))},n.minimize=function(){this.animateHeightChange(),this.$().css("top","auto"),this.hideBackdrop()},n.controlItems=function(){var t=new B;return this.state.position===Oe.Position.FULLSCREEN?t.add("exitFullScreen",m(Be,{icon:"fas fa-compress",title:Wi.translator.trans("core.forum.composer.exit_full_screen_tooltip"),onclick:this.state.exitFullScreen.bind(this.state)})):(this.state.position!==Oe.Position.MINIMIZED&&(t.add("minimize",m(Be,{icon:"fas fa-minus minimize",title:Wi.translator.trans("core.forum.composer.minimize_tooltip"),onclick:this.state.minimize.bind(this.state),itemClassName:"App-backControl"})),t.add("fullScreen",m(Be,{icon:"fas fa-expand",title:Wi.translator.trans("core.forum.composer.full_screen_tooltip"),onclick:this.state.fullScreen.bind(this.state)}))),t.add("close",m(Be,{icon:"fas fa-times",title:Wi.translator.trans("core.forum.composer.close_tooltip"),onclick:this.state.close.bind(this.state)}))),t},n.initializeHeight=function(){this.state.height=localStorage.getItem("composerHeight"),this.state.height||(this.state.height=this.defaultHeight())},n.defaultHeight=function(){return this.$().height()},n.changeHeight=function(t){this.state.height=t,this.updateHeight(),localStorage.setItem("composerHeight",this.state.height)},e}(E),Me=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(t){var e,n,r,i,o,s=this,a=this.attrs.notification,u=null!=(e=null==(n=this.href)?void 0:n.call(this))?e:"",l=a.fromUser();return m(G,{className:M("Notification","Notification--"+a.contentType(),[!a.isRead()&&"unread"]),href:u,external:u.includes("://"),onclick:this.markAsRead.bind(this)},Y(l||null),nt(null==(r=this.icon)?void 0:r.call(this),{className:"Notification-icon"}),m("span",{className:"Notification-title"},m("span",{className:"Notification-content"},null==(i=this.content)?void 0:i.call(this)),m("span",{className:"Notification-title-spring"}),lt(a.createdAt())),!a.isRead()&&m(yt,{className:"Notification-action Button Button--link",icon:"fas fa-check",title:Wi.translator.trans("core.forum.notifications.mark_as_read_tooltip"),onclick:function(t){t.preventDefault(),t.stopPropagation(),s.markAsRead()}}),m("div",{className:"Notification-excerpt"},null==(o=this.excerpt)?void 0:o.call(this)))},n.markAsRead=function(){var t,e;this.attrs.notification.isRead()||(null==(t=Wi.session.user)||t.pushAttributes({unreadNotificationCount:(null!=(e=Wi.session.user.unreadNotificationCount())?e:1)-1}),this.attrs.notification.save({isRead:!0}))},e}(E),$e=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.icon=function(){return"fas fa-pencil-alt"},n.href=function(){var t=this.attrs.notification,e=t.subject();return e?Wi.route.discussion(e,t.content().postNumber):"#"},n.content=function(){return Wi.translator.trans("core.forum.notifications.discussion_renamed_text",{user:this.attrs.notification.fromUser()})},n.excerpt=function(){return null},e}(Me);function je(t){Wi.composer.isFullScreen()&&(Wi.composer.minimize(),t.stopPropagation())}var Re=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.submitLabel=e.submitLabel||Wi.translator.trans("core.forum.composer_edit.submit_button"),e.confirmExit=e.confirmExit||Wi.translator.trans("core.forum.composer_edit.discard_confirmation"),e.originalContent=e.originalContent||e.post.content(),e.user=e.user||e.post.user(),e.post.editedContent=e.originalContent};var n=e.prototype;return n.headerItems=function(){var e=t.prototype.headerItems.call(this),n=this.attrs.post;return e.add("title",m("h3",null,nt("fas fa-pencil-alt")," ",m(G,{href:Wi.route.discussion(n.discussion(),n.number()),onclick:je},Wi.translator.trans("core.forum.composer_edit.post_link",{number:n.number(),discussion:n.discussion().title()})))),e},n.jumpToPreview=function(t){je(t),m.route.set(Wi.route.post(this.attrs.post))},n.data=function(){return{content:this.composer.fields.content()}},n.onsubmit=function(){var t=this,e=this.attrs.post.discussion();this.loading=!0;var n=this.data();this.attrs.post.save(n).then((function(n){if(Wi.viewingDiscussion(e))Wi.current.get("stream").goToNumber(n.number());else var r=Wi.alerts.show({type:"success",controls:[m(yt,{className:"Button Button--link",onclick:function(){m.route.set(Wi.route.post(n)),Wi.alerts.dismiss(r)}},Wi.translator.trans("core.forum.composer_edit.view_button"))]},Wi.translator.trans("core.forum.composer_edit.edited_message"));t.composer.hide()}),this.loaded.bind(this))},e}(kt);const He={controls:function(t,e){var n=this,r=new B;return["user","moderation","destructive"].forEach((function(i){var o=n[i+"Controls"](t,e).toArray();o.length&&(o.forEach((function(t){return r.add(t.itemName,t)})),r.add(i+"Separator",m(O,null)))})),r},userControls:function(t,e){return new B},moderationControls:function(t,e){var n=new B;return"comment"===t.contentType()&&t.canEdit()&&(t.isHidden()||n.add("edit",m(yt,{icon:"fas fa-pencil-alt",onclick:this.editAction.bind(t)},Wi.translator.trans("core.forum.post_controls.edit_button")))),n},destructiveControls:function(t,e){var n=new B;return"comment"!==t.contentType()||t.isHidden()?("comment"===t.contentType()&&t.canHide()&&n.add("restore",m(yt,{icon:"fas fa-reply",onclick:this.restoreAction.bind(t)},Wi.translator.trans("core.forum.post_controls.restore_button"))),t.canDelete()&&n.add("delete",m(yt,{icon:"fas fa-times",onclick:this.deleteAction.bind(t,e)},Wi.translator.trans("core.forum.post_controls.delete_forever_button")))):t.canHide()&&n.add("hide",m(yt,{icon:"far fa-trash-alt",onclick:this.hideAction.bind(t)},Wi.translator.trans("core.forum.post_controls.delete_button"))),n},editAction:function(){var t=this;return new Promise((function(e){return Wi.composer.load(Re,{post:t}),Wi.composer.show(),e()}))},hideAction:function(){if(confirm(ot(Wi.translator.trans("core.forum.post_controls.hide_confirmation"))))return this.pushData({attributes:{hiddenAt:new Date},relationships:{hiddenUser:Wi.session.user}}),this.save({isHidden:!0}).then((function(){return m.redraw()}))},restoreAction:function(){return this.pushData({attributes:{hiddenAt:null},relationships:{hiddenUser:null}}),this.save({isHidden:!1}).then((function(){return m.redraw()}))},deleteAction:function(t){var e=this;if(confirm(ot(Wi.translator.trans("core.forum.post_controls.delete_confirmation"))))return t&&(t.loading=!0),this.delete().then((function(){var t=e.discussion();t.removePost(e.id()),t.postIds().length||(Wi.discussions.removeDiscussion(t),Wi.viewingDiscussion(t)&&Wi.history.back())})).catch((function(){})).then((function(){t&&(t.loading=!1),m.redraw()}))}};var Ue=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).loading=!1,e.subtree=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),this.loading=!1,this.subtree=new dt((function(){return n.loading}),(function(){return n.attrs.post.freshness}),(function(){var t=n.attrs.post.user();return t&&t.freshness}))},n.view=function(t){var e=this.elementAttrs();e.className=this.classes(e.className).join(" ");var n=He.controls(this.attrs.post,this).toArray(),r=this.footerItems().toArray();return m("article",e,m("div",null,this.viewItems(n,r).toArray()))},n.viewItems=function(t,e){var n=this,r=new B;return r.add("content",this.loading?m(ft,null):this.content(),100),r.add("actions",m("aside",{className:"Post-actions"},m("ul",null,H(this.actionItems().toArray()),!!t.length&&m("li",null,m(ut,{className:"Post-controls",buttonClassName:"Button Button--icon Button--flat",menuClassName:"Dropdown-menu--right",icon:"fas fa-ellipsis-h",onshow:function(){return n.$(".Post-controls").addClass("open")},onhide:function(){return n.$(".Post-controls").removeClass("open")},accessibleToggleLabel:Wi.translator.trans("core.forum.post_controls.toggle_dropdown_accessible_label")},t)))),90),r.add("footer",m("footer",{className:"Post-footer"},e.length>0?m("ul",null,H(e)):m("ul",null)),80),r},n.onbeforeupdate=function(e){return t.prototype.onbeforeupdate.call(this,e),this.subtree.needsRebuild()},n.onupdate=function(e){t.prototype.onupdate.call(this,e);var n=this.$(".Post-actions"),r=this.$(".Post-controls");n.toggleClass("openWithin",r.hasClass("open"))},n.elementAttrs=function(){return{}},n.content=function(){return[]},n.classes=function(t){var e=(t||"").split(" ").concat(["Post"]),n=this.attrs.post.user(),r=this.attrs.post.discussion();return this.loading&&e.push("Post--loading"),n&&n===Wi.session.user&&e.push("Post--by-actor"),n&&n===r.user()&&e.push("Post--by-start-user"),e},n.actionItems=function(){return new B},n.footerItems=function(){return new B},e}(E),qe=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.group&&(e.icon=e.group.icon()||"",e.color=e.group.color()||"",e.label=void 0===e.label?e.group.nameSingular():e.label,e.type="group--"+e.group.id(),delete e.group)},e}(be),ze=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.nameSingular=function(){return fe.attribute("nameSingular").call(this)},n.namePlural=function(){return fe.attribute("namePlural").call(this)},n.color=function(){return fe.attribute("color").call(this)},n.icon=function(){return fe.attribute("icon").call(this)},n.isHidden=function(){return fe.attribute("isHidden").call(this)},e}(fe);ze.ADMINISTRATOR_ID="1",ze.GUEST_ID="2",ze.MEMBER_ID="3";var Ve=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).username=void 0,e.email=void 0,e.isEmailConfirmed=void 0,e.setPassword=void 0,e.password=void 0,e.groups={},e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e);var r=this.attrs.user;this.username=Bt(r.username()||""),this.email=Bt(r.email()||""),this.isEmailConfirmed=Bt(r.isEmailConfirmed()||!1),this.setPassword=Bt(!1),this.password=Bt(r.password()||"");var i=r.groups()||[];v.store.all("groups").filter((function(t){return![ze.GUEST_ID,ze.MEMBER_ID].includes(t.id())})).forEach((function(t){return n.groups[t.id()]=Bt(i.includes(t))}))},n.className=function(){return"EditUserModal Modal--small"},n.title=function(){return v.translator.trans("core.lib.edit_user.title")},n.content=function(){var t=this.fields().toArray();return m("div",{className:"Modal-body"},t.length>1?m("div",{className:"Form"},this.fields().toArray()):v.translator.trans("core.lib.edit_user.nothing_available"))},n.fields=function(){var t=this,e=new B;return this.attrs.user.canEditCredentials()&&(e.add("username",m("div",{className:"Form-group"},m("label",null,v.translator.trans("core.lib.edit_user.username_heading")),m("input",{className:"FormControl",placeholder:ot(v.translator.trans("core.lib.edit_user.username_label")),bidi:this.username,disabled:this.nonAdminEditingAdmin()})),40),v.session.user!==this.attrs.user&&(e.add("email",m("div",{className:"Form-group"},m("label",null,v.translator.trans("core.lib.edit_user.email_heading")),m("div",null,m("input",{className:"FormControl",placeholder:ot(v.translator.trans("core.lib.edit_user.email_label")),bidi:this.email,disabled:this.nonAdminEditingAdmin()})),!this.isEmailConfirmed()&&this.userIsAdmin(v.session.user)&&m("div",null,m(yt,{className:"Button Button--block",loading:this.loading,onclick:this.activate.bind(this)},v.translator.trans("core.lib.edit_user.activate_button")))),30),e.add("password",m("div",{className:"Form-group"},m("label",null,v.translator.trans("core.lib.edit_user.password_heading")),m("div",null,m("label",{className:"checkbox"},m("input",{type:"checkbox",onchange:function(e){var n=e.target;t.setPassword(n.checked),m.redraw.sync(),n.checked&&t.$("[name=password]").select(),e.redraw=!1},disabled:this.nonAdminEditingAdmin()}),v.translator.trans("core.lib.edit_user.set_password_label")),this.setPassword()&&m("input",{className:"FormControl",type:"password",name:"password",placeholder:ot(v.translator.trans("core.lib.edit_user.password_label")),bidi:this.password,disabled:this.nonAdminEditingAdmin()}))),20))),this.attrs.user.canEditGroups()&&e.add("groups",m("div",{className:"Form-group EditUserModal-groups"},m("label",null,v.translator.trans("core.lib.edit_user.groups_heading")),m("div",null,Object.keys(this.groups).map((function(t){return v.store.getById("groups",t)})).filter(Boolean).map((function(e){return e&&m("label",{className:"checkbox"},m("input",{type:"checkbox",bidi:t.groups[e.id()],disabled:e.id()===ze.ADMINISTRATOR_ID&&(t.attrs.user===v.session.user||!t.userIsAdmin(v.session.user))}),m(qe,{group:e,label:null})," ",e.nameSingular())})))),10),e.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary",type:"submit",loading:this.loading},v.translator.trans("core.lib.edit_user.submit_button"))),-10),e},n.activate=function(){var t=this;this.loading=!0;var e={username:this.username(),isEmailConfirmed:!0};this.attrs.user.save(e,{errorHandler:this.onerror.bind(this)}).then((function(){t.isEmailConfirmed(!0),t.loading=!1,m.redraw()})).catch((function(){t.loading=!1,m.redraw()}))},n.data=function(){var t=this,e={},n={};return this.attrs.user.canEditCredentials()&&!this.nonAdminEditingAdmin()&&(e.username=this.username(),v.session.user!==this.attrs.user&&(e.email=this.email()),this.setPassword()&&(e.password=this.password())),this.attrs.user.canEditGroups()&&(n.groups=Object.keys(this.groups).filter((function(e){return t.groups[e]()})).map((function(t){return v.store.getById("groups",t)})).filter((function(t){return t instanceof ze}))),e.relationships=n,e},n.onsubmit=function(t){var e=this;t.preventDefault(),this.loading=!0,this.attrs.user.save(this.data(),{errorHandler:this.onerror.bind(this)}).then(this.hide.bind(this)).catch((function(){e.loading=!1,m.redraw()}))},n.nonAdminEditingAdmin=function(){return this.userIsAdmin(this.attrs.user)&&!this.userIsAdmin(v.session.user)},n.userIsAdmin=function(t){return!!((null==t?void 0:t.groups())||[]).some((function(t){return(null==t?void 0:t.id())===ze.ADMINISTRATOR_ID}))},e}(_t),We=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(t){return t.children[0]},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.boundOnresize=this.onresize.bind(this),$(window).on("resize",this.boundOnresize).resize()},n.onremove=function(e){t.prototype.onremove.call(this,e),$(window).off("resize",this.boundOnresize)},n.onresize=function(){var t=this,e=this.$(),n=$("#header"),r=$("#footer"),i=e.find("> ul");$(window).off(".affix"),i.removeClass("affix affix-top affix-bottom").removeData("bs.affix"),e.outerHeight(!0)>$(window).height()-n.outerHeight(!0)||i.affix({offset:{top:function(){return e.offset().top-n.outerHeight(!0)-parseInt(e.css("margin-top"),10)},bottom:function(){return t.bottom=r.outerHeight(!0)}}})},e}(E),Ge=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).user=null,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.bodyClass="App--user"},n.view=function(){return m("div",{className:"UserPage"},this.user?[m(Xe,{user:this.user,className:"Hero UserHero",editable:this.user.canEdit()||this.user===Wi.session.user,controlsButtonClassName:"Button"}),m("div",{className:"container"},m("div",{className:"sideNavContainer"},m(We,null,m("nav",{className:"sideNav UserPage-nav"},m("ul",null,H(this.sidebarItems().toArray())))),m("div",{className:"sideNavOffset UserPage-content"},this.content())))]:[m(ft,{display:"block"})])},n.content=function(){},n.show=function(t){this.user=t,Wi.current.set("user",t),Wi.setTitle(t.displayName()),m.redraw()},n.loadUser=function(t){var e=this,n=t.toLowerCase();Wi.preloadedApiDocument(),Wi.store.all("users").some((function(r){return!(r.username().toLowerCase()!==n&&r.id()!==t||!r.joinTime()||(e.show(r),0))})),this.user||Wi.store.find("users",t,{bySlug:!0}).then(this.show.bind(this))},n.sidebarItems=function(){var t=new B;return t.add("nav",m(he,{className:"App-titleControl",buttonClassName:"Button"},this.navItems().toArray())),t},n.navItems=function(){var t=new B,e=this.user,n=Wi.session.user===e;return t.add("posts",m(le,{href:Wi.route("user.posts",{username:e.slug()}),icon:"far fa-comment"},Wi.translator.trans("core.forum.user.posts_link")," ",m("span",{className:"Button-badge"},e.commentCount())),100),t.add("discussions",m(le,{href:Wi.route("user.discussions",{username:e.slug()}),icon:"fas fa-bars"},Wi.translator.trans("core.forum.user.discussions_link")," ",m("span",{className:"Button-badge"},e.discussionCount())),90),n&&(t.add("separator",m(O,null),-90),t.add("settings",m(le,{href:Wi.route("settings"),icon:"fas fa-cog"},Wi.translator.trans("core.forum.user.settings_link")),-100)),(n||Wi.forum.attribute("canModerateAccessTokens"))&&(n||t.add("security-separator",m(O,null),-90),t.add("security",m(le,{href:Wi.route("user.security",{username:e.slug()}),icon:"fas fa-shield-alt"},Wi.translator.trans("core.forum.user.security_link")),-100)),t},e}(S);const Ye={controls:function(t,e){var n=this,r=new B;return["user","moderation","destructive"].forEach((function(i){var o=n[i+"Controls"](t,e).toArray();o.length&&(o.forEach((function(t){return r.add(t.itemName,t)})),r.add(i+"Separator",m(O,null)))})),r},userControls:function(){return new B},moderationControls:function(t){var e=new B;return(t.canEdit()||t.canEditCredentials()||t.canEditGroups())&&e.add("edit",m(yt,{icon:"fas fa-pencil-alt",onclick:this.editAction.bind(this,t)},Wi.translator.trans("core.forum.user_controls.edit_button"))),e},destructiveControls:function(t){var e=new B;return"1"!==t.id()&&t.canDelete()&&e.add("delete",m(yt,{icon:"fas fa-times",onclick:this.deleteAction.bind(this,t)},Wi.translator.trans("core.forum.user_controls.delete_button"))),e},deleteAction:function(t){var e=this;confirm(Wi.translator.trans("core.forum.user_controls.delete_confirmation"))&&t.delete().then((function(){e.showDeletionAlert(t,"success"),Wi.current.matches(Ge,{user:t})?Wi.history.back():window.location.reload()})).catch((function(){return e.showDeletionAlert(t,"error")}))},showDeletionAlert:function(t,e){var n={success:"core.forum.user_controls.delete_success_message",error:"core.forum.user_controls.delete_error_message"}[e];Wi.alerts.show({type:e},Wi.translator.trans(n,{user:t,email:t.email()}))},editAction:function(t){Wi.modal.show(Ve,{user:t})}};var Ke=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.loading=!1,this.isDraggedOver=!1},n.view=function(){var t=this.attrs.user;return m("div",{className:M(["AvatarEditor","Dropdown",this.attrs.className,this.loading&&"loading",this.isDraggedOver&&"dragover"])},Y(t,{loading:"eager"}),m("a",{className:t.avatarUrl()?"Dropdown-toggle":"Dropdown-toggle AvatarEditor--noAvatar",title:Wi.translator.trans("core.forum.user.avatar_upload_tooltip"),"data-toggle":"dropdown",onclick:this.quickUpload.bind(this),ondragover:this.enableDragover.bind(this),ondragenter:this.enableDragover.bind(this),ondragleave:this.disableDragover.bind(this),ondragend:this.disableDragover.bind(this),ondrop:this.dropUpload.bind(this)},this.loading?m(ft,{display:"unset",size:"large"}):t.avatarUrl()?nt("fas fa-pencil-alt"):nt("fas fa-plus-circle")),m("ul",{className:"Dropdown-menu Menu"},H(this.controlItems().toArray())))},n.controlItems=function(){var t=new B;return t.add("upload",m(yt,{icon:"fas fa-upload",onclick:this.openPicker.bind(this)},Wi.translator.trans("core.forum.user.avatar_upload_button"))),t.add("remove",m(yt,{icon:"fas fa-times",onclick:this.remove.bind(this)},Wi.translator.trans("core.forum.user.avatar_remove_button"))),t},n.enableDragover=function(t){t.preventDefault(),t.stopPropagation(),this.isDraggedOver=!0},n.disableDragover=function(t){t.preventDefault(),t.stopPropagation(),this.isDraggedOver=!1},n.dropUpload=function(t){t.preventDefault(),t.stopPropagation(),this.isDraggedOver=!1,this.upload(t.dataTransfer.files[0])},n.quickUpload=function(t){this.attrs.user.avatarUrl()||(t.preventDefault(),t.stopPropagation(),this.openPicker())},n.openPicker=function(){var t=this;this.loading||$('<input type="file" accept=".jpg, .jpeg, .png, .bmp, .gif">').appendTo("body").hide().click().on("input",(function(e){t.upload($(e.target)[0].files[0])}))},n.upload=function(t){if(!this.loading){var e=this.attrs.user,n=new FormData;n.append("avatar",t),this.loading=!0,m.redraw(),Wi.request({method:"POST",url:Wi.forum.attribute("apiUrl")+"/users/"+e.id()+"/avatar",serialize:function(t){return t},body:n}).then(this.success.bind(this),this.failure.bind(this))}},n.remove=function(){var t=this.attrs.user;this.loading=!0,m.redraw(),Wi.request({method:"DELETE",url:Wi.forum.attribute("apiUrl")+"/users/"+t.id()+"/avatar"}).then(this.success.bind(this),this.failure.bind(this))},n.success=function(t){Wi.store.pushPayload(t),delete this.attrs.user.avatarColor,this.loading=!1,m.redraw()},n.failure=function(t){this.loading=!1,m.redraw()},e}(E),Xe=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=this.attrs.user,e=Ye.controls(t,this).toArray(),n=t.color(),r=t.badges().toArray();return m("div",{className:M("UserCard",this.attrs.className),style:n&&{"--usercard-bg":n}},m("div",{className:"darkenBackground"},m("div",{className:"container"},!!e.length&&m(ut,{className:"UserCard-controls App-primaryControl",menuClassName:"Dropdown-menu--right",buttonClassName:this.attrs.controlsButtonClassName,label:Wi.translator.trans("core.forum.user_controls.button"),accessibleToggleLabel:Wi.translator.trans("core.forum.user_controls.toggle_dropdown_accessible_label"),icon:"fas fa-ellipsis-v"},e),m("div",{className:"UserCard-profile"},m("h1",{className:"UserCard-identity"},this.attrs.editable?m("[",null,m(Ke,{user:t,className:"UserCard-avatar"})," ",Xt(t)):m(G,{href:Wi.route.user(t)},m("div",{className:"UserCard-avatar"},Y(t,{loading:"eager"})),Xt(t))),!!r.length&&m("ul",{className:"UserCard-badges badges"},H(r)),m("ul",{className:"UserCard-info"},H(this.infoItems().toArray()))))))},n.infoItems=function(){var t=new B,e=this.attrs.user,n=e.lastSeenAt();if(n){var r=e.isOnline();t.add("lastSeen",m("span",{className:M("UserCard-lastSeen",{online:r})},r?[nt("fas fa-circle")," ",Wi.translator.trans("core.forum.user.online_text")]:[nt("far fa-clock")," ",st(n)]),100)}return t.add("joined",Wi.translator.trans("core.forum.user.joined_date_text",{ago:st(e.joinTime())}),90),t},e}(E);function Je(t){return t.lastSeenAt()&&t.isOnline()?m("span",{className:"UserOnline"},nt("fas fa-circle")):null}var Qe=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=this.attrs.post,e=t.user();return e?m("div",{className:"PostUser"},m("h3",{className:"PostUser-name"},m(G,{href:Wi.route.user(e)},Y(e,{className:"PostUser-avatar"}),Je(e),Xt(e))),m("ul",{className:"PostUser-badges badges"},H(e.badges().toArray())),!t.isHidden()&&this.attrs.cardVisible&&m(Xe,{user:e,className:"UserCard--popover",controlsButtonClassName:"Button Button--icon Button--flat"})):m("div",{className:"PostUser"},m("h3",{className:"PostUser-name"},Y(e,{className:"PostUser-avatar"})," ",Xt(e)))},n.oncreate=function(e){var n,r=this;t.prototype.oncreate.call(this,e),this.$().on("mouseover",".PostUser-name a, .UserCard",(function(){clearTimeout(n),n=setTimeout(r.showCard.bind(r),500)})).on("mouseout",".PostUser-name a, .UserCard",(function(){clearTimeout(n),n=setTimeout(r.hideCard.bind(r),250)}))},n.showCard=function(){var t=this;this.attrs.oncardshow(),setTimeout((function(){return t.$(".UserCard").addClass("in")}))},n.hideCard=function(){var t=this;this.$(".UserCard").removeClass("in").one("transitionend webkitTransitionEnd oTransitionEnd",(function(){t.attrs.oncardhide()}))},e}(E);function Ze(t){var e=it()(t),n=e.format(),r=e.format("LLLL");return m("time",{pubdate:!0,datetime:n},r)}var tn=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).ip=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.ip=this.attrs.ip||""},n.view=function(){return m("span",{className:"IPAddress"},this.viewItems().toArray())},n.viewItems=function(){var t=new B;return t.add("ip",m("span",null,this.ip),100),t},e}(E),en=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){return m("div",{className:"Dropdown PostMeta"},this.viewItems().toArray())},n.getPermalink=function(t){return Wi.forum.attribute("baseOrigin")+Wi.route.post(t)},n.selectPermalink=function(t){var e=this;setTimeout((function(){return $(e.element).parent().find(".PostMeta-permalink").select()})),t.redraw=!1},n.viewItems=function(){var t=this,e=new B,n=this.attrs.post.createdAt();return e.add("time",m("a",{className:"Dropdown-toggle",onclick:function(e){return t.selectPermalink(e)},"data-toggle":"dropdown"},lt(n)),100),e.add("meta-dropdown",m("div",{className:"Dropdown-menu dropdown-menu"},this.metaItems().toArray()),90),e},n.metaItems=function(){var t=new B,e=this.attrs.post,n="ontouchstart"in document.documentElement,r=this.getPermalink(e),i=e.createdAt();return t.add("post-number",m("span",{className:"PostMeta-number"},Wi.translator.trans("core.forum.post.number_tooltip",{number:e.number()})," "),100),t.add("post-time",m("span",{className:"PostMeta-time"},Ze(i)),90),t.add("post-ip",m("span",{className:"PostMeta-ip"},m(tn,{ip:e.data.attributes.ipAddress})),80),t.add("permalink",n?m("a",{className:"Button PostMeta-permalink",href:r},r):m("input",{className:"FormControl PostMeta-permalink",value:r,onclick:function(t){return t.stopPropagation()}}),0),t},e}(E),nn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e)},n.view=function(){var t=this.attrs.post,e=t.editedUser(),n=Wi.translator.trans("core.forum.post.edited_tooltip",{user:e,ago:st(t.editedAt())});return m(Et,{text:n},m("span",{className:"PostEdited"},Wi.translator.trans("core.forum.post.edited_text")))},n.oncreate=function(e){t.prototype.oncreate.call(this,e)},e}(E),rn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){var n=this;t.prototype.oninit.call(this,e),this.revealContent=!1,this.cardVisible=!1,this.subtree.check((function(){return n.cardVisible}),(function(){return n.isEditing()}),(function(){return n.revealContent}))},n.content=function(){return t.prototype.content.call(this).concat(this.contentItems().toArray())},n.contentItems=function(){var t=new B;return t.add("header",m("header",{className:"Post-header"},m("ul",null,H(this.headerItems().toArray()))),100),t.add("body",m("div",{className:"Post-body"},this.bodyItems().toArray()),90),t},n.bodyItems=function(){var t=new B;return t.add("content",this.isEditing()?m(Jt,{className:"Post-preview",composer:Wi.composer}):m.trust(this.attrs.post.contentHtml()),100),t},n.refreshContent=function(){var t=this.isEditing()?"":this.attrs.post.contentHtml();this.contentHtml!==t&&this.$(".Post-body script").each((function(){var t=document.createElement("script");t.textContent=this.textContent,Array.from(this.attributes).forEach((function(e){return t.setAttribute(e.name,e.value)})),this.parentNode.replaceChild(t,this)})),this.contentHtml=t},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.refreshContent()},n.onupdate=function(e){t.prototype.onupdate.call(this,e),this.refreshContent()},n.isEditing=function(){return Wi.composer.bodyMatches(Re,{post:this.attrs.post})},n.elementAttrs=function(){var e=this.attrs.post,n=t.prototype.elementAttrs.call(this);return n.className=M(n.className,"CommentPost",{"Post--renderFailed":e.renderFailed(),"Post--hidden":e.isHidden(),"Post--edited":e.isEdited(),revealContent:this.revealContent,editing:this.isEditing()}),this.isEditing()&&(n["aria-busy"]="true"),n},n.toggleContent=function(){this.revealContent=!this.revealContent},n.headerItems=function(){var t=this,e=new B,n=this.attrs.post;return e.add("user",m(Qe,{post:n,cardVisible:this.cardVisible,oncardshow:function(){t.cardVisible=!0,m.redraw()},oncardhide:function(){t.cardVisible=!1,m.redraw()}}),100),e.add("meta",m(en,{post:n})),n.isEdited()&&!n.isHidden()&&e.add("edited",m(nn,{post:n})),n.isHidden()&&e.add("toggle",m(yt,{className:"Button Button--default Button--more",icon:"fas fa-ellipsis-h",onclick:this.toggleContent.bind(this)})),e},e}(Ue),on=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.elementAttrs=function(){var e=t.prototype.elementAttrs.call(this);return e.className=M(e.className,"EventPost",Q(this.attrs.post.contentType())+"Post"),e},n.content=function(){var e=this.attrs.post.user(),n=Xt(e),r=Object.assign(this.descriptionData(),{user:e,username:e?m(G,{className:"EventPost-user",href:Wi.route.user(e)},n):n,time:lt(this.attrs.post.createdAt())});return t.prototype.content.call(this).concat([nt(this.icon(),{className:"EventPost-icon"}),m("div",{className:"EventPost-info"},this.description(r))])},n.icon=function(){return""},n.description=function(t){return Wi.translator.trans(this.descriptionKey(),t)},n.descriptionKey=function(){return""},n.descriptionData=function(){return{}},e}(Ue),sn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.icon=function(){return"fas fa-pencil-alt"},n.description=function(t){var e=Wi.translator.trans("core.forum.post_stream.discussion_renamed_text",t);return m("span",null,e)},n.descriptionData=function(){var t=this.attrs.post,e=t.content()[0],n=t.content()[1];return{new:m(Et,{text:ot(Wi.translator.trans("core.forum.post_stream.discussion_renamed_old_tooltip",{old:e}))},m("strong",{className:"DiscussionRenamedPost-new"},n))}},e}(on),an="welcomeHidden",un=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).hidden=!1,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e)},n.view=function(t){return this.isHidden()?null:m("header",{className:"Hero WelcomeHero"},m("div",{className:"container"},this.viewItems().toArray()))},n.hide=function(){localStorage.setItem(an,"true")},n.isHidden=function(){var t;return null==(t=Wi.forum.attribute("welcomeTitle"))||!t.trim()||!!localStorage.getItem(an)||!!this.hidden},n.viewItems=function(){var t=this,e=new B;return e.add("dismiss-button",m(yt,{icon:"fas fa-times",onclick:function(){t.$().slideUp(t.hide.bind(t))},className:"Hero-close Button Button--icon Button--link","aria-label":Wi.translator.trans("core.forum.welcome_hero.hide")}),100),e.add("content",m("div",{className:"containerNarrow"},this.contentItems().toArray()),80),e},n.contentItems=function(){var t=new B;return t.add("title",m("h1",{className:"Hero-title"},Wi.forum.attribute("welcomeTitle")),100),t.add("subtitle",m("div",{className:"Hero-subtitle"},m.trust(Wi.forum.attribute("welcomeMessage")))),t},e}(E),ln=function(t){function e(){return t.apply(this,arguments)||this}w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.placeholder=e.placeholder||ot(Wi.translator.trans("core.forum.composer_discussion.body_placeholder")),e.submitLabel=e.submitLabel||Wi.translator.trans("core.forum.composer_discussion.submit_button"),e.confirmExit=e.confirmExit||ot(Wi.translator.trans("core.forum.composer_discussion.discard_confirmation")),e.titlePlaceholder=e.titlePlaceholder||ot(Wi.translator.trans("core.forum.composer_discussion.title_placeholder")),e.className="ComposerBody--discussion"};var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.composer.fields.title=this.composer.fields.title||Bt(""),this.title=this.composer.fields.title},n.headerItems=function(){var e=t.prototype.headerItems.call(this);return e.add("title",m("h3",null,Wi.translator.trans("core.forum.composer_discussion.title")),100),e.add("discussionTitle",m("h3",null,m("input",{className:"FormControl",bidi:this.title,placeholder:this.attrs.titlePlaceholder,disabled:!!this.attrs.disabled,onkeydown:this.onkeydown.bind(this)}))),e},n.onkeydown=function(t){13===t.which&&(t.preventDefault(),this.composer.editor.moveCursorTo(0)),t.redraw=!1},n.hasChanges=function(){return this.title()||this.composer.fields.content()},n.data=function(){return{title:this.title(),content:this.composer.fields.content()}},n.onsubmit=function(){var t=this;this.loading=!0;var e=this.data();Wi.store.createRecord("discussions").save(e).then((function(e){t.composer.hide(),Wi.discussions.refresh(),m.route.set(Wi.route.discussion(e))}),this.loaded.bind(this))},e}(kt),cn=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).lastDiscussion=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(n){t.prototype.oninit.call(this,n),Wi.previous.matches(ae)&&(this.lastDiscussion=Wi.previous.get("discussion")),Wi.previous.matches(e)&&Wi.discussions.clear(),Wi.discussions.refreshParams(Wi.search.params(),m.route.param("page")&&Number(m.route.param("page"))||1),Wi.history.push("index",ot(Wi.translator.trans("core.forum.header.back_to_index_tooltip"))),this.bodyClass="App--index",this.scrollTopOnCreate=!1},n.view=function(){return m("div",{className:"IndexPage"},this.hero(),m("div",{className:"container"},m("div",{className:"sideNavContainer"},m("nav",{className:"IndexPage-nav sideNav"},m("ul",null,H(this.sidebarItems().toArray()))),m("div",{className:"IndexPage-results sideNavOffset"},this.contentItems().toArray()))))},n.contentItems=function(){var t=new B;return t.add("toolbar",m("div",{className:"IndexPage-toolbar"},this.toolbarItems().toArray()),100),t.add("discussionList",m(Vt,{state:Wi.discussions}),90),t},n.toolbarItems=function(){var t=new B;return t.add("view",m("ul",{className:"IndexPage-toolbar-view"},H(this.viewItems().toArray())),100),t.add("action",m("ul",{className:"IndexPage-toolbar-action"},H(this.actionItems().toArray())),90),t},n.setTitle=function(){Wi.setTitle(ot(Wi.translator.trans("core.forum.index.meta_title_text"))),Wi.setTitleCount(0)},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.setTitle();var n=Wi.cache.heroHeight,r=Wi.cache.heroHeight=this.$(".Hero").outerHeight()||0,i=Wi.cache.scrollTop;if($("#app").css("min-height",($(window).height()||0)+r),null!=Wi.previous.type&&(this.lastDiscussion?$(window).scrollTop(i-n+r):$(window).scrollTop(0),this.lastDiscussion)){var o=this.$('li[data-id="'+this.lastDiscussion.id()+'"] .DiscussionListItem');if(o.length){var s=$("#header").outerHeight()||0,a=$(window).height()||0,u=o.offset(),l=u&&u.top||0,c=l+(o.outerHeight()||0);(l<i+s||c>i+a)&&$(window).scrollTop(l-s)}}},n.onbeforeremove=function(e){t.prototype.onbeforeremove.call(this,e),Wi.cache.scrollTop=$(window).scrollTop()},n.onremove=function(e){t.prototype.onremove.call(this,e),$("#app").css("min-height","")},n.hero=function(){return m(un,null)},n.sidebarItems=function(){var t=this,e=new B,n=Wi.forum.attribute("canStartDiscussion")||!Wi.session.user;return e.add("newDiscussion",m(yt,{icon:"fas fa-edit",className:"Button Button--primary IndexPage-newDiscussion",itemClassName:"App-primaryControl",onclick:function(){return t.newDiscussionAction().catch((function(){}))},disabled:!n},Wi.translator.trans("core.forum.index."+(n?"start_discussion_button":"cannot_start_discussion_button")))),e.add("nav",m(he,{buttonClassName:"Button",className:"App-titleControl",accessibleToggleLabel:Wi.translator.trans("core.forum.index.toggle_sidenav_dropdown_accessible_label")},this.navItems().toArray())),e},n.navItems=function(){var t=new B,e=Wi.search.stickyParams();return t.add("allDiscussions",m(le,{href:Wi.route("index",e),icon:"far fa-comments"},Wi.translator.trans("core.forum.index.all_discussions_link")),100),t},n.viewItems=function(){var t=new B,e=Wi.discussions.sortMap(),n=Object.keys(e).reduce((function(t,e){return t[e]=Wi.translator.trans("core.forum.index_sort."+e+"_button"),t}),{});return t.add("sort",m(ut,{buttonClassName:"Button",label:n[Wi.search.params().sort]||Object.keys(e).map((function(t){return n[t]}))[0],accessibleToggleLabel:Wi.translator.trans("core.forum.index_sort.toggle_dropdown_accessible_label")},Object.keys(n).map((function(t){var r=n[t],i=(Wi.search.params().sort||Object.keys(e)[0])===t;return m(yt,{icon:!i||"fas fa-check",onclick:Wi.search.changeSort.bind(Wi.search,t),active:i},r)})))),t},n.actionItems=function(){var t=new B;return t.add("refresh",m(yt,{title:Wi.translator.trans("core.forum.index.refresh_tooltip"),icon:"fas fa-sync",className:"Button Button--icon",onclick:function(){Wi.discussions.refresh(),Wi.session.user&&(Wi.store.find("users",Wi.session.user.id()),m.redraw())}})),Wi.session.user&&t.add("markAllAsRead",m(yt,{title:Wi.translator.trans("core.forum.index.mark_all_as_read_tooltip"),icon:"fas fa-check",className:"Button Button--icon",onclick:this.markAllAsRead.bind(this)})),t},n.newDiscussionAction=function(){return new Promise((function(t,e){return Wi.session.user?(Wi.composer.load(ln,{user:Wi.session.user}),Wi.composer.show(),t(Wi.composer)):(Wi.modal.show(Mt),e())}))},n.markAllAsRead=function(){var t;confirm(ot(Wi.translator.trans("core.forum.index.mark_all_as_read_confirmation")))&&(null==(t=Wi.session.user)||t.save({markedAllAsReadAt:new Date}))},e}(S);cn.providesInitialSearch=!0;var dn=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).loading=!0,e.moreResults=!1,e.posts=[],e.loadLimit=20,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.loadUser(m.route.param("username"))},n.content=function(){var t,e=this;return 0!==this.posts.length||this.loading?(this.loading?t=m(ft,null):this.moreResults&&(t=m("div",{className:"PostsUserPage-loadMore"},m(yt,{className:"Button",onclick:this.loadMore.bind(this)},Wi.translator.trans("core.forum.user.posts_load_more_button")))),m("div",{className:"PostsUserPage"},m("ul",{className:"PostsUserPage-list"},this.posts.map((function(t){return m("li",null,m("div",{className:"PostsUserPage-discussion"},Wi.translator.trans("core.forum.user.in_discussion_text",{discussion:m(G,{href:Wi.route.post(t)},e.discussionTitle(t))})),m(rn,{post:t}))}))),m("div",{className:"PostsUserPage-loadMore"},t))):m("div",{className:"PostsUserPage"},m(zt,{text:Wi.translator.trans("core.forum.user.posts_empty_text")}))},n.show=function(e){t.prototype.show.call(this,e),this.refresh()},n.refresh=function(){this.loading=!0,this.posts=[],m.redraw(),this.loadResults().then(this.parseResults.bind(this))},n.loadResults=function(t){return void 0===t&&(t=0),Wi.store.find("posts",{filter:{author:this.user.username(),type:"comment"},page:{offset:t,limit:this.loadLimit},sort:"-createdAt"})},n.loadMore=function(){this.loading=!0,this.loadResults(this.posts.length).then(this.parseResults.bind(this))},n.parseResults=function(t){var e;return this.loading=!1,(e=this.posts).push.apply(e,t),this.moreResults=t.length>=this.loadLimit,m.redraw(),t},n.discussionTitle=function(t){return t.discussion().title()},e}(Ge),hn=function(){function t(t,e,n){void 0===t&&(t={}),void 0===e&&(e=1),void 0===n&&(n=20),this.location=void 0,this.pageSize=void 0,this.pages=[],this.params={},this.initialLoading=!1,this.loadingPrev=!1,this.loadingNext=!1,this.params=t,this.location={page:e},this.pageSize=n}var e=t.prototype;return e.clear=function(){this.pages=[],m.redraw()},e.loadPrev=function(){var t=this;if(this.loadingPrev||1===this.getLocation().page)return Promise.resolve();this.loadingPrev=!0;var e=this.getPrevPageNumber();return this.loadPage(e).then(this.parseResults.bind(this,e)).finally((function(){return t.loadingPrev=!1}))},e.loadNext=function(){var t=this;if(this.loadingNext)return Promise.resolve();this.loadingNext=!0;var e=this.getNextPageNumber();return this.loadPage(e).then(this.parseResults.bind(this,e)).finally((function(){return t.loadingNext=!1}))},e.parseResults=function(t,e){var n,r=Number(t),i=null==(n=e.payload)?void 0:n.links,o={number:r,items:e,hasNext:!(null==i||!i.next),hasPrev:!(null==i||!i.prev)};this.isEmpty()||r>this.getNextPageNumber()-1?this.pages.push(o):this.pages.unshift(o),this.location={page:r},m.redraw()},e.loadPage=function(t){void 0===t&&(t=1);var e=this.requestParams(),n=Array.isArray(e.include)?e.include.join(","):e.include,r=D({},e,{page:D({},e.page,{offset:this.pageSize*(t-1)}),include:n});return v.store.find(this.type,r)},e.requestParams=function(){return this.params},e.refreshParams=function(t,e){return this.isEmpty()||this.paramsChanged(t)?(this.params=t,this.refresh(e)):Promise.resolve()},e.refresh=function(t){var e=this;return void 0===t&&(t=1),this.initialLoading=!0,this.loadingPrev=!1,this.loadingNext=!1,this.clear(),this.location={page:t},this.loadPage().then((function(t){e.pages=[],e.parseResults(e.location.page,t)})).finally((function(){return e.initialLoading=!1}))},e.getPages=function(){return this.pages},e.getLocation=function(){return this.location},e.isLoading=function(){return this.initialLoading||this.loadingNext||this.loadingPrev},e.isInitialLoading=function(){return this.initialLoading},e.isLoadingPrev=function(){return this.loadingPrev},e.isLoadingNext=function(){return this.loadingNext},e.hasItems=function(){return!!this.getAllItems().length},e.isEmpty=function(){return!this.isInitialLoading()&&!this.hasItems()},e.hasPrev=function(){var t;return!(null==(t=this.pages[0])||!t.hasPrev)},e.hasNext=function(){var t;return!(null==(t=this.pages[this.pages.length-1])||!t.hasNext)},e.getParams=function(){return this.params},e.getNextPageNumber=function(){var t,e=null==(t=this.pages[this.pages.length-1])?void 0:t.number;return e&&!isNaN(e)?e+1:this.location.page},e.getPrevPageNumber=function(){var t,e=null==(t=this.pages[0])?void 0:t.number;return e&&!isNaN(e)?Math.max(e-1,1):this.location.page},e.paramsChanged=function(t){var e=this;return Object.keys(t).some((function(n){return e.getParams()[n]!==t[n]}))},e.getAllItems=function(){return this.getPages().map((function(t){return t.items})).flat()},t}(),fn=function(){function t(){this.events={},this.events={}}var e=t.prototype;return e.on=function(t,e){return this.events[t]||(this.events[t]=[]),this.events[t].push(e),this},e.emit=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];this.events[t]&&this.events[t].forEach((function(t){return t.apply(void 0,n)}))},t}();function pn(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var mn=new fn,vn=function(t){function e(e,n){var r;return void 0===n&&(n=1),(r=t.call(this,e,n,20)||this).extraDiscussions=[],r.eventEmitter=void 0,r.eventEmitter=mn.on("discussion.deleted",r.deleteDiscussion.bind(y(r))),r}w(e,t);var n=e.prototype;return n.requestParams=function(){var t,e={include:["user","lastPostedUser"],filter:this.params.filter||{},sort:this.sortMap()[null!=(t=this.params.sort)?t:""]};return this.params.q&&(e.filter.q=this.params.q,e.include.push("mostRelevantPost","mostRelevantPost.user")),e},n.loadPage=function(e){void 0===e&&(e=1);var n=Wi.preloadedApiDocument();return n?(this.initialLoading=!1,Promise.resolve(n)):t.prototype.loadPage.call(this,e)},n.clear=function(){t.prototype.clear.call(this),this.extraDiscussions=[]},n.sortMap=function(){var t={};return this.params.q&&(t.relevance=""),t.latest="-lastPostedAt",t.top="-commentCount",t.newest="-createdAt",t.oldest="createdAt",t},n.isSearchResults=function(){return!!this.params.q},n.removeDiscussion=function(t){this.eventEmitter.emit("discussion.deleted",t)},n.deleteDiscussion=function(t){for(var e,n=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return pn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pn(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.pages);!(e=n()).done;){var r=e.value,i=r.items.indexOf(t);if(-1!==i){r.items.splice(i,1);break}}var o=this.extraDiscussions.indexOf(t);-1!==o&&this.extraDiscussions.splice(o),m.redraw()},n.addDiscussion=function(t){this.removeDiscussion(t),this.extraDiscussions.unshift(t),m.redraw()},n.getAllItems=function(){return this.extraDiscussions.concat(t.prototype.getAllItems.call(this))},n.getPages=function(){var e=t.prototype.getPages.call(this);return this.extraDiscussions.length?[{number:-1,items:this.extraDiscussions}].concat(e):e},F(e,[{key:"type",get:function(){return"discussions"}}]),e}(hn),gn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.loadUser(m.route.param("username"))},n.show=function(e){t.prototype.show.call(this,e),this.state=new vn({filter:{author:e.username()},sort:"newest"}),this.state.refresh()},n.content=function(){return m("div",{className:"DiscussionsUserPage"},m(Vt,{state:this.state}))},e}(Ge);const yn=function(t,e){return function(){e(this.getAttribute(t)||this[t])}};var bn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(t){var e=M(["Checkbox",this.attrs.state?"on":"off",this.attrs.className,this.attrs.loading&&"loading",this.attrs.disabled&&"disabled"]);return m("label",{className:e},m("input",{type:"checkbox",checked:this.attrs.state,disabled:this.attrs.disabled,onchange:yn("checked",this.onchange.bind(this))}),m("div",{className:"Checkbox-display","aria-hidden":"true"},this.getDisplay()),t.children)},n.getDisplay=function(){return this.attrs.loading?m(ft,{display:"unset",size:"small"}):nt(this.attrs.state?"fas fa-check":"fas fa-times")},n.onchange=function(t){this.attrs.onchange&&this.attrs.onchange(t,this)},e}(E),wn=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){t.initAttrs.call(this,e),e.className=M(e.className,"Checkbox--switch")},e.prototype.getDisplay=function(){return!!this.attrs.loading&&t.prototype.getDisplay.call(this)},e}(bn),Dn=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(t){return m("fieldset",{className:this.attrs.className},m("legend",null,this.attrs.label),m("ul",null,H(t.children)))},e}(E),xn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.methods=this.notificationMethods().toArray(),this.loading={},this.types=this.notificationTypes().toArray()},n.view=function(){var t=this,e=this.attrs.user.preferences();return m("table",{className:"NotificationGrid"},m("thead",null,m("tr",null,m("td",null),this.methods.map((function(e){return m("th",{className:"NotificationGrid-groupToggle",onclick:t.toggleMethod.bind(t,e.name)},nt(e.icon)," ",e.label)})))),m("tbody",null,this.types.map((function(n){return m("tr",null,m("td",{className:"NotificationGrid-groupToggle",onclick:t.toggleType.bind(t,n.name)},nt(n.icon)," ",n.label),t.methods.map((function(r){var i=t.preferenceKey(n.name,r.name);return m("td",{className:"NotificationGrid-checkbox"},m(bn,{state:!!e[i],loading:t.loading[i],disabled:!(i in e),onchange:t.toggle.bind(t,[i])},m("span",{className:"sr-only"},Wi.translator.trans("core.forum.settings.notification_checkbox_a11y_label_template",{description:n.label,method:r.label}))))})))}))))},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.$("thead .NotificationGrid-groupToggle").bind("mouseenter mouseleave",(function(t){var e=parseInt($(this).index(),10)+1;$(this).parents("table").find("td:nth-child("+e+")").toggleClass("highlighted","mouseenter"===t.type)})),this.$("tbody .NotificationGrid-groupToggle").bind("mouseenter mouseleave",(function(t){$(this).parent().find("td").toggleClass("highlighted","mouseenter"===t.type)}))},n.toggle=function(t){var e=this,n=this.attrs.user,r=n.preferences(),i=!r[t[0]];t.forEach((function(t){e.loading[t]=!0,r[t]=i})),m.redraw(),n.save({preferences:r}).then((function(){t.forEach((function(t){return e.loading[t]=!1})),m.redraw()}))},n.toggleMethod=function(t){var e=this,n=this.types.map((function(n){return e.preferenceKey(n.name,t)})).filter((function(t){return t in e.attrs.user.preferences()}));this.toggle(n)},n.toggleType=function(t){var e=this,n=this.methods.map((function(n){return e.preferenceKey(t,n.name)})).filter((function(t){return t in e.attrs.user.preferences()}));this.toggle(n)},n.preferenceKey=function(t,e){return"notify_"+t+"_"+e},n.notificationMethods=function(){var t=new B;return t.add("alert",{name:"alert",icon:"fas fa-bell",label:Wi.translator.trans("core.forum.settings.notify_by_web_heading")}),t.add("email",{name:"email",icon:"far fa-envelope",label:Wi.translator.trans("core.forum.settings.notify_by_email_heading")}),t},n.notificationTypes=function(){var t=new B;return t.add("discussionRenamed",{name:"discussionRenamed",icon:"fas fa-pencil-alt",label:Wi.translator.trans("core.forum.settings.notify_discussion_renamed_label")}),t},e}(E),Cn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.className=function(){return"ChangePasswordModal Modal--small"},n.title=function(){return Wi.translator.trans("core.forum.change_password.title")},n.content=function(){return m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},this.fields().toArray()))},n.fields=function(){var t=new B;return t.add("help",m("p",{className:"helpText"},Wi.translator.trans("core.forum.change_password.text"))),t.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.change_password.send_button")))),t},n.onsubmit=function(t){t.preventDefault(),this.loading=!0,Wi.request({method:"POST",url:Wi.forum.attribute("apiUrl")+"/forgot",body:this.requestBody()}).then(this.hide.bind(this),this.loaded.bind(this))},n.requestBody=function(){return{email:Wi.session.user.email()}},e}(_t),An=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).email=void 0,e.password=void 0,e.success=!1,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.email=Bt(Wi.session.user.email()||""),this.password=Bt("")},n.className=function(){return"ChangeEmailModal Modal--small"},n.title=function(){return Wi.translator.trans("core.forum.change_email.title")},n.content=function(){return m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},this.fields().toArray()))},n.fields=function(){var t=new B;return this.success?(t.add("help",m("p",{className:"helpText"},Wi.translator.trans("core.forum.change_email.confirmation_message",{email:m("strong",null,this.email())}))),t.add("dismiss",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",onclick:this.hide.bind(this)},Wi.translator.trans("core.forum.change_email.dismiss_button"))))):(t.add("email",m("div",{className:"Form-group"},m("input",{type:"email",name:"email",className:"FormControl",placeholder:Wi.session.user.email(),bidi:this.email,disabled:this.loading}))),t.add("password",m("div",{className:"Form-group"},m("input",{type:"password",name:"password",className:"FormControl",autocomplete:"current-password",placeholder:Wi.translator.trans("core.forum.change_email.confirm_password_placeholder"),bidi:this.password,disabled:this.loading}))),t.add("submit",m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.change_email.submit_button"))))),t},n.onsubmit=function(t){var e=this;t.preventDefault(),this.email()!==Wi.session.user.email()?(this.loading=!0,this.alertAttrs=null,Wi.session.user.save(this.requestAttributes(),{errorHandler:this.onerror.bind(this),meta:{password:this.password()}}).then((function(){e.success=!0})).catch((function(){})).then(this.loaded.bind(this))):this.hide()},n.requestAttributes=function(){return{email:this.email()}},n.onerror=function(e){401===e.status&&e.alert&&(e.alert.content=Wi.translator.trans("core.forum.change_email.incorrect_password_message")),t.prototype.onerror.call(this,e)},e}(_t),En=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).discloseOnlineLoading=void 0,e}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.show(Wi.session.user),Wi.setTitle(ot(Wi.translator.trans("core.forum.settings.title")))},n.content=function(){return m("div",{className:"SettingsPage"},m("ul",null,H(this.settingsItems().toArray())))},n.settingsItems=function(){var t=this,e=new B;return["account","notifications","privacy"].forEach((function(n,r){var i=n+"Items";e.add(n,m(Dn,{className:"Settings-"+n,label:Wi.translator.trans("core.forum.settings."+n+"_heading")},t[i]().toArray()),100-10*r)})),e},n.accountItems=function(){var t=new B;return t.add("changePassword",m(yt,{className:"Button",onclick:function(){return Wi.modal.show(Cn)}},Wi.translator.trans("core.forum.settings.change_password_button")),100),t.add("changeEmail",m(yt,{className:"Button",onclick:function(){return Wi.modal.show(An)}},Wi.translator.trans("core.forum.settings.change_email_button")),90),t},n.notificationsItems=function(){var t=new B;return t.add("notificationGrid",m(xn,{user:this.user}),100),t},n.privacyItems=function(){var t,e=this,n=new B;return n.add("discloseOnline",m(wn,{state:null==(t=this.user.preferences())?void 0:t.discloseOnline,onchange:function(t){e.discloseOnlineLoading=!0,e.user.savePreferences({discloseOnline:t}).then((function(){e.discloseOnlineLoading=!1,m.redraw()}))},loading:this.discloseOnlineLoading},Wi.translator.trans("core.forum.settings.privacy_disclose_online_label")),100),n},e}(Ge),Nn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),Wi.history.push("notifications",ot(Wi.translator.trans("core.forum.notifications.title"))),Wi.notifications.load(),this.bodyClass="App--notifications"},n.view=function(){return m("div",{className:"NotificationsPage"},m(De,{state:Wi.notifications}))},e}(S),kn=function(){function t(t,e){this.component=void 0,this.routeName=void 0,this.component=t,this.routeName=e}var e=t.prototype;return e.makeKey=function(){return this.routeName+JSON.stringify(m.route.param())},e.makeAttrs=function(t){return D({},t.attrs,{routeName:this.routeName})},e.onmatch=function(t,e,n){return this.component},e.render=function(t){return[D({},t,{attrs:this.makeAttrs(t),key:this.makeKey()})]},t}(),Sn=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.canonicalizeDiscussionSlug=function(t){if(t)return t.split("-")[0]},n.makeKey=function(){var t=D({},m.route.param());return"near"in t&&delete t.near,t.id=this.canonicalizeDiscussionSlug(t.id),this.routeName.replace(".near","")+JSON.stringify(t)},n.onmatch=function(n,r,i){return Wi.current.matches(ae)&&this.canonicalizeDiscussionSlug(n.id)===this.canonicalizeDiscussionSlug(m.route.param("id"))&&(e.scrollToPostNumber=n.near||1),t.prototype.onmatch.call(this,n,r,i)},n.render=function(n){if(null!==e.scrollToPostNumber){var r=e.scrollToPostNumber;setTimeout((function(){return Wi.current.get("stream").goToNumber(r)})),e.scrollToPostNumber=null}return t.prototype.render.call(this,n)},e}(kn);Sn.scrollToPostNumber=null;var Tn=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(t){return m("div",{className:"LabelValue"},m("div",{className:"LabelValue-label"},v.translator.trans("core.lib.data_segment.label",{label:this.attrs.label})),m("div",{className:"LabelValue-value"},this.attrs.value))},e}(E),Fn=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).loading={},e.showingTokens={},e}w(e,t);var n=e.prototype;return n.view=function(t){return m("div",{className:"AccessTokensList"},this.attrs.tokens.length?this.attrs.tokens.map(this.tokenView.bind(this)):m("div",{className:"AccessTokensList--empty"},Wi.translator.trans("core.forum.security.empty_text")))},n.tokenView=function(t){return m("div",{className:M("AccessTokensList-item",{"AccessTokensList-item--active":t.isCurrent()}),key:t.id()},this.tokenViewItems(t).toArray())},n.tokenViewItems=function(t){var e=new B;return e.add("icon",m("div",{className:"AccessTokensList-item-icon"},nt(this.attrs.icon||"fas fa-key")),50),e.add("info",m("div",{className:"AccessTokensList-item-info"},this.tokenInfoItems(t).toArray()),40),e.add("actions",m("div",{className:"AccessTokensList-item-actions"},this.tokenActionItems(t).toArray()),30),e},n.tokenInfoItems=function(t){var e=new B;return"session"===this.attrs.type?e.add("title",m("div",{className:"AccessTokensList-item-title"},m("span",{className:"AccessTokensList-item-title-main"},t.device()),t.isCurrent()&&[" — ",m("span",{className:"AccessTokensList-item-title-sub"},Wi.translator.trans("core.forum.security.current_active_session"))])):e.add("title",m("div",{className:"AccessTokensList-item-title"},m("span",{className:"AccessTokensList-item-title-main"},this.generateTokenTitle(t)))),e.add("createdAt",m("div",{className:"AccessTokensList-item-createdAt"},m(Tn,{label:Wi.translator.trans("core.forum.security.created"),value:lt(t.createdAt())}))),e.add("lastActivityAt",m("div",{className:"AccessTokensList-item-lastActivityAt"},m(Tn,{label:Wi.translator.trans("core.forum.security.last_activity"),value:t.lastActivityAt()?m("[",null,lt(t.lastActivityAt()),t.lastIpAddress()&&m("span",null," ","— ",m(tn,{ip:t.lastIpAddress()})),"developer_token"===this.attrs.type&&t.device()&&m("[",null," ","— ",m("span",{className:"AccessTokensList-item-title-sub"},t.device()))):Wi.translator.trans("core.forum.security.never")}))),e},n.tokenActionItems=function(t){var e=this,n=new B,r={session:"terminate_session",developer_token:"revoke_access_token"}[this.attrs.type];if("developer_token"===this.attrs.type){var i=!this.showingTokens[t.id()],o=i?"show_access_token":"hide_access_token";n.add("toggleDisplay",m(yt,{className:"Button Button--inverted",icon:i?"fas fa-eye":"fas fa-eye-slash",onclick:function(){e.showingTokens[t.id()]=i,m.redraw()}},Wi.translator.trans("core.forum.security."+o)))}var s=m(yt,{className:"Button Button--danger",disabled:t.isCurrent(),loading:!!this.loading[t.id()],onclick:function(){return e.revoke(t)}},Wi.translator.trans("core.forum.security."+r));return t.isCurrent()&&(s=m(Et,{text:Wi.translator.trans("core.forum.security.cannot_terminate_current_session")},m("div",{tabindex:"0"},s))),n.add("revoke",s),n},n.revoke=function(){var t=Ee(ke().mark((function t(e){var n,r,i;return ke().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(confirm(ot(Wi.translator.trans("core.forum.security.revoke_access_token_confirmation")))){t.next=2;break}return t.abrupt("return");case 2:return this.loading[e.id()]=!0,t.next=5,e.delete();case 5:this.loading[e.id()]=!1,null==(n=(r=this.attrs).ondelete)||n.call(r,e),i="session"===this.attrs.type?"session_terminated":"token_revoked",Wi.alerts.show({type:"success"},Wi.translator.trans("core.forum.security."+i,{count:1})),m.redraw();case 10:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),n.generateTokenTitle=function(t){var e=t.title()||Wi.translator.trans("core.forum.security.token_title_placeholder"),n=this.tokenValueDisplay(t);return Wi.translator.trans("core.forum.security.token_item_title",{title:e,token:n})},n.tokenValueDisplay=function(t){var e=Array(12).fill("*").join(""),n=this.showingTokens[t.id()]?t.token():e;return m("code",{className:"AccessTokensList-item-token"},n)},e}(E),_n=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).titleInput=Bt(""),e}w(e,t);var n=e.prototype;return n.className=function(){return"Modal--small NewAccessTokenModal"},n.title=function(){return Wi.translator.trans("core.forum.security.new_access_token_modal.title")},n.content=function(){var t=Wi.translator.trans("core.forum.security.new_access_token_modal.title_placeholder");return m("div",{className:"Modal-body"},m("div",{className:"Form Form--centered"},m("div",{className:"Form-group"},m("input",{type:"text",className:"FormControl",bidi:this.titleInput,placeholder:t,"aria-label":t})),m("div",{className:"Form-group"},m(yt,{className:"Button Button--primary Button--block",type:"submit",loading:this.loading},Wi.translator.trans("core.forum.security.new_access_token_modal.submit_button")))))},n.submitData=function(){return{title:this.titleInput()}},n.onsubmit=function(e){var n=this;t.prototype.onsubmit.call(this,e),e.preventDefault(),this.loading=!0,Wi.store.createRecord("access-tokens").save(this.submitData()).then((function(t){n.attrs.onsuccess(t),Wi.modal.close()})).finally(this.loaded.bind(this))},e}(_t),Pn=function(){function t(){this.tokens=null,this.loadingTerminateSessions=!1,this.loadingGlobalLogout=!1}var e=t.prototype;return e.hasLoadedTokens=function(){return null!==this.tokens},e.getTokens=function(){return this.tokens},e.setTokens=function(t){this.tokens=t},e.pushToken=function(t){var e;null==(e=this.tokens)||e.push(t)},e.removeToken=function(t){this.tokens=this.tokens.filter((function(e){return e!==t}))},e.getSessionTokens=function(){var t;return(null==(t=this.tokens)?void 0:t.filter((function(t){return t.isSessionToken()})).sort((function(t,e){return e.isCurrent()?1:-1})))||[]},e.getDeveloperTokens=function(){var t;return(null==(t=this.tokens)?void 0:t.filter((function(t){return!t.isSessionToken()})))||null},e.getOtherSessionTokens=function(){var t;return(null==(t=this.tokens)?void 0:t.filter((function(t){return t.isSessionToken()&&!t.isCurrent()})))||[]},e.hasOtherActiveSessions=function(){return(this.getOtherSessionTokens()||[]).length>0},e.removeOtherSessionTokens=function(){this.tokens=this.tokens.filter((function(t){return!t.isSessionToken()||t.isCurrent()}))},t}(),Bn=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).state=new Pn,e}w(e,t);var n=e.prototype;return n.oninit=function(e){var n;t.prototype.oninit.call(this,e);var r=m.route.param("username");r===(null==(n=Wi.session.user)?void 0:n.slug())||Wi.forum.attribute("canModerateAccessTokens")||m.route.set("/"),this.loadUser(r),Wi.setTitle(ot(Wi.translator.trans("core.forum.security.title"))),this.loadTokens()},n.content=function(){return m("div",{className:"UserSecurityPage"},m("ul",null,H(this.settingsItems().toArray())))},n.settingsItems=function(){var t,e=new B;return Wi.forum.attribute("canCreateAccessToken")||Wi.forum.attribute("canModerateAccessTokens")||this.state.hasLoadedTokens()&&null!=(t=this.state.getDeveloperTokens())&&t.length?e.add("developerTokens",m(Dn,{className:"UserSecurityPage-developerTokens",label:Wi.translator.trans("core.forum.security.developer_tokens_heading")},this.developerTokensItems().toArray())):this.state.hasLoadedTokens()||e.add("developerTokens",m(ft,null)),e.add("sessions",m(Dn,{className:"UserSecurityPage-sessions",label:Wi.translator.trans("core.forum.security.sessions_heading")},this.sessionsItems().toArray())),this.user.id()===Wi.session.user.id()&&e.add("globalLogout",m(Dn,{className:"UserSecurityPage-globalLogout",label:Wi.translator.trans("core.forum.security.global_logout.heading")},m("span",{className:"helpText"},Wi.translator.trans("core.forum.security.global_logout.help_text")),m(yt,{className:"Button",icon:"fas fa-sign-out-alt",onclick:this.globalLogout.bind(this),loading:this.state.loadingGlobalLogout,disabled:this.state.loadingTerminateSessions},Wi.translator.trans("core.forum.security.global_logout.log_out_button")))),e},n.developerTokensItems=function(){var t=this,e=new B;return e.add("accessTokenList",this.state.hasLoadedTokens()?m(Fn,{type:"developer_token",ondelete:function(e){t.state.removeToken(e),m.redraw()},tokens:this.state.getDeveloperTokens(),icon:"fas fa-key",hideTokens:!1}):m(ft,null)),this.user.id()===Wi.session.user.id()&&e.add("newAccessToken",m(yt,{className:"Button",disabled:!Wi.forum.attribute("canCreateAccessToken"),onclick:function(){return Wi.modal.show(_n,{onsuccess:function(e){t.state.pushToken(e),m.redraw()}})}},Wi.translator.trans("core.forum.security.new_access_token_button"))),e},n.sessionsItems=function(){var t=this,e=new B;if(e.add("sessionsList",this.state.hasLoadedTokens()?m(Fn,{type:"session",ondelete:function(e){t.state.removeToken(e),m.redraw()},tokens:this.state.getSessionTokens(),icon:"fas fa-laptop",hideTokens:!0}):m(ft,null)),this.user.id()===Wi.session.user.id()){var n=!this.state.hasOtherActiveSessions(),r=m(yt,{className:"Button",onclick:this.terminateAllOtherSessions.bind(this),loading:this.state.loadingTerminateSessions,disabled:this.state.loadingGlobalLogout||n},Wi.translator.trans("core.forum.security.terminate_all_other_sessions"));n&&(r=m(Et,{text:Wi.translator.trans("core.forum.security.cannot_terminate_current_session")},m("span",{tabindex:"0"},r))),e.add("terminateAllOtherSessions",r)}return e},n.loadTokens=function(){var t=this;return Wi.store.find("access-tokens",{filter:{user:this.user.id()}}).then((function(e){t.state.setTokens(e),m.redraw()}))},n.terminateAllOtherSessions=function(){var t=this;if(confirm(ot(Wi.translator.trans("core.forum.security.terminate_all_other_sessions_confirmation"))))return this.state.loadingTerminateSessions=!0,Wi.request({method:"DELETE",url:Wi.forum.attribute("apiUrl")+"/sessions"}).then((function(){var e=t.state.getOtherSessionTokens().length;t.state.removeOtherSessionTokens(),Wi.alerts.show({type:"success"},Wi.translator.trans("core.forum.security.session_terminated",{count:e}))})).catch((function(){Wi.alerts.show({type:"error"},Wi.translator.trans("core.forum.security.session_termination_failed"))})).finally((function(){t.state.loadingTerminateSessions=!1,m.redraw()}))},n.globalLogout=function(){var t=this;return this.state.loadingGlobalLogout=!0,Wi.request({method:"POST",url:Wi.forum.attribute("baseUrl")+"/global-logout"}).then((function(){return window.location.reload()})).finally((function(){t.state.loadingGlobalLogout=!1,m.redraw()}))},e}(Ge);function In(t){t.routes={index:{path:"/all",component:cn},discussion:{path:"/d/:id",component:ae,resolverClass:Sn},"discussion.near":{path:"/d/:id/:near",component:ae,resolverClass:Sn},user:{path:"/u/:username",component:dn},"user.posts":{path:"/u/:username",component:dn},"user.discussions":{path:"/u/:username/discussions",component:gn},settings:{path:"/settings",component:En},"user.security":{path:"/u/:username/security",component:Bn},notifications:{path:"/notifications",component:Nn}}}function On(t){var e=t.session.user;if(e&&!e.isEmailConfirmed()){var n=function(n){function r(){return n.apply(this,arguments)||this}w(r,n);var i=r.prototype;return i.oninit=function(t){n.prototype.oninit.call(this,t),this.loading=!1,this.sent=!1},i.view=function(){return m(yt,{className:"Button Button--link",onclick:this.onclick.bind(this),loading:this.loading,disabled:this.sent},this.sent?[nt("fas fa-check")," ",t.translator.trans("core.forum.user_email_confirmation.sent_message")]:t.translator.trans("core.forum.user_email_confirmation.resend_button"))},i.onclick=function(){var n=this;this.loading=!0,m.redraw(),t.request({method:"POST",url:t.forum.attribute("apiUrl")+"/users/"+e.id()+"/send-confirmation"}).then((function(){n.loading=!1,n.sent=!0,m.redraw()})).catch((function(){n.loading=!1,m.redraw()}))},r}(E),r=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(e){var n=t.prototype.view.call(this,e);return D({},n,{children:[m("div",{className:"container"},n.children)]})},e}(Ft);m.mount($('<div className="App-notices"/>').insertBefore("#content")[0],{view:function(){return m(r,{dismissible:!1,controls:[m(n,null)],className:"Alert--emailConfirmation"},t.translator.trans("core.forum.user_email_confirmation.alert_message",{email:m("strong",null,e.email())}))}})}}var Ln=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],Mn=Ln.join(","),$n="undefined"==typeof Element,jn=$n?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Rn=!$n&&Element.prototype.getRootNode?function(t){return t.getRootNode()}:function(t){return t.ownerDocument},Hn=function(t,e,n){var r=Array.prototype.slice.apply(t.querySelectorAll(Mn));return e&&jn.call(t,Mn)&&r.unshift(t),r.filter(n)},Un=function t(e,n,r){for(var i=[],o=Array.from(e);o.length;){var s=o.shift();if("SLOT"===s.tagName){var a=s.assignedElements(),u=t(a.length?a:s.children,!0,r);r.flatten?i.push.apply(i,u):i.push({scope:s,candidates:u})}else{jn.call(s,Mn)&&r.filter(s)&&(n||!e.includes(s))&&i.push(s);var l=s.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(s),c=!r.shadowRootFilter||r.shadowRootFilter(s);if(l&&c){var d=t(!0===l?s.children:l.children,!0,r);r.flatten?i.push.apply(i,d):i.push({scope:s,candidates:d})}else o.unshift.apply(o,s.children)}}return i},qn=function(t,e){return t.tabIndex<0&&(e||/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||t.isContentEditable)&&isNaN(parseInt(t.getAttribute("tabindex"),10))?0:t.tabIndex},zn=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},Vn=function(t){return"INPUT"===t.tagName},Wn=function(t){var e=t.getBoundingClientRect(),n=e.width,r=e.height;return 0===n&&0===r},Gn=function(t,e){return!(e.disabled||function(t){return Vn(t)&&"hidden"===t.type}(e)||function(t,e){var n=e.displayCheck,r=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var i=jn.call(t,"details>summary:first-of-type")?t.parentElement:t;if(jn.call(i,"details:not([open]) *"))return!0;var o=Rn(t).host,s=(null==o?void 0:o.ownerDocument.contains(o))||t.ownerDocument.contains(t);if(n&&"full"!==n){if("non-zero-area"===n)return Wn(t)}else{if("function"==typeof r){for(var a=t;t;){var u=t.parentElement,l=Rn(t);if(u&&!u.shadowRoot&&!0===r(u))return Wn(t);t=t.assignedSlot?t.assignedSlot:u||l===t.ownerDocument?u:l.host}t=a}if(s)return!t.getClientRects().length}return!1}(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var n=0;n<e.children.length;n++){var r=e.children.item(n);if("LEGEND"===r.tagName)return!!jn.call(e,"fieldset[disabled] *")||!r.contains(t)}return!0}e=e.parentElement}return!1}(e))},Yn=function(t,e){return!(function(t){return function(t){return Vn(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,n=t.form||Rn(t),r=function(t){return n.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=r(window.CSS.escape(t.name));else try{e=r(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var i=function(t,e){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===e)return t[n]}(e,t.form);return!i||i===t}(t)}(e)||qn(e)<0||!Gn(t,e))},Kn=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},Xn=function t(e){var n=[],r=[];return e.forEach((function(e,i){var o=!!e.scope,s=o?e.scope:e,a=qn(s,o),u=o?t(e.candidates):s;0===a?o?n.push.apply(n,u):n.push(s):r.push({documentOrder:i,tabIndex:a,item:e,isScope:o,content:u})})),r.sort(zn).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(n)},Jn=function(t,e){var n;return n=(e=e||{}).getShadowRoot?Un([t],e.includeContainer,{filter:Yn.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:Kn}):Hn(t,e.includeContainer,Yn.bind(null,e)),Xn(n)},Qn=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==jn.call(t,Mn)&&Yn(e,t)},Zn=Ln.concat("iframe").join(","),tr=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==jn.call(t,Zn)&&Gn(e,t)};function er(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function nr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?er(Object(n),!0).forEach((function(e){rr(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):er(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function rr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ir,or=(ir=[],{activateTrap:function(t){if(ir.length>0){var e=ir[ir.length-1];e!==t&&e.pause()}var n=ir.indexOf(t);-1===n||ir.splice(n,1),ir.push(t)},deactivateTrap:function(t){var e=ir.indexOf(t);-1!==e&&ir.splice(e,1),ir.length>0&&ir[ir.length-1].unpause()}}),sr=function(t){return setTimeout(t,0)},ar=function(t,e){var n=-1;return t.every((function(t,r){return!e(t)||(n=r,!1)})),n},ur=function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return"function"==typeof t?t.apply(void 0,n):t},lr=function(t){return t.target.shadowRoot&&"function"==typeof t.composedPath?t.composedPath()[0]:t.target},cr=function(t,e){var n,r=(null==e?void 0:e.document)||document,i=nr({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},e),o={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},s=function(t,e,n){return t&&void 0!==t[e]?t[e]:i[n||e]},a=function(t){return o.containerGroups.findIndex((function(e){var n=e.container,r=e.tabbableNodes;return n.contains(t)||r.find((function(e){return e===t}))}))},u=function(t){var e=i[t];if("function"==typeof e){for(var n=arguments.length,o=new Array(n>1?n-1:0),s=1;s<n;s++)o[s-1]=arguments[s];e=e.apply(void 0,o)}if(!0===e&&(e=void 0),!e){if(void 0===e||!1===e)return e;throw new Error("`".concat(t,"` was specified but was not a node, or did not return a node"))}var a=e;if("string"==typeof e&&!(a=r.querySelector(e)))throw new Error("`".concat(t,"` as selector refers to no known node"));return a},l=function(){var t=u("initialFocus");if(!1===t)return!1;if(void 0===t)if(a(r.activeElement)>=0)t=r.activeElement;else{var e=o.tabbableGroups[0];t=e&&e.firstTabbableNode||u("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},c=function(){if(o.containerGroups=o.containers.map((function(t){var e,n,r=Jn(t,i.tabbableOptions),o=(e=t,(n=(n=i.tabbableOptions)||{}).getShadowRoot?Un([e],n.includeContainer,{filter:Gn.bind(null,n),flatten:!0,getShadowRoot:n.getShadowRoot}):Hn(e,n.includeContainer,Gn.bind(null,n)));return{container:t,tabbableNodes:r,focusableNodes:o,firstTabbableNode:r.length>0?r[0]:null,lastTabbableNode:r.length>0?r[r.length-1]:null,nextTabbableNode:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=o.findIndex((function(e){return e===t}));if(!(n<0))return e?o.slice(n+1).find((function(t){return Qn(t,i.tabbableOptions)})):o.slice(0,n).reverse().find((function(t){return Qn(t,i.tabbableOptions)}))}}})),o.tabbableGroups=o.containerGroups.filter((function(t){return t.tabbableNodes.length>0})),o.tabbableGroups.length<=0&&!u("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},d=function t(e){!1!==e&&e!==r.activeElement&&(e&&e.focus?(e.focus({preventScroll:!!i.preventScroll}),o.mostRecentlyFocusedNode=e,function(t){return t.tagName&&"input"===t.tagName.toLowerCase()&&"function"==typeof t.select}(e)&&e.select()):t(l()))},h=function(t){var e=u("setReturnFocus",t);return e||!1!==e&&t},f=function(t){var e=lr(t);a(e)>=0||(ur(i.clickOutsideDeactivates,t)?n.deactivate({returnFocus:i.returnFocusOnDeactivate&&!tr(e,i.tabbableOptions)}):ur(i.allowOutsideClick,t)||t.preventDefault())},p=function(t){var e=lr(t),n=a(e)>=0;n||e instanceof Document?n&&(o.mostRecentlyFocusedNode=e):(t.stopImmediatePropagation(),d(o.mostRecentlyFocusedNode||l()))},m=function(t){if(function(t){return"Escape"===t.key||"Esc"===t.key||27===t.keyCode}(t)&&!1!==ur(i.escapeDeactivates,t))return t.preventDefault(),void n.deactivate();(function(t){return"Tab"===t.key||9===t.keyCode})(t)&&function(t){var e=lr(t);c();var n=null;if(o.tabbableGroups.length>0){var r=a(e),s=r>=0?o.containerGroups[r]:void 0;if(r<0)n=t.shiftKey?o.tabbableGroups[o.tabbableGroups.length-1].lastTabbableNode:o.tabbableGroups[0].firstTabbableNode;else if(t.shiftKey){var l=ar(o.tabbableGroups,(function(t){var n=t.firstTabbableNode;return e===n}));if(l<0&&(s.container===e||tr(e,i.tabbableOptions)&&!Qn(e,i.tabbableOptions)&&!s.nextTabbableNode(e,!1))&&(l=r),l>=0){var h=0===l?o.tabbableGroups.length-1:l-1;n=o.tabbableGroups[h].lastTabbableNode}}else{var f=ar(o.tabbableGroups,(function(t){var n=t.lastTabbableNode;return e===n}));if(f<0&&(s.container===e||tr(e,i.tabbableOptions)&&!Qn(e,i.tabbableOptions)&&!s.nextTabbableNode(e))&&(f=r),f>=0){var p=f===o.tabbableGroups.length-1?0:f+1;n=o.tabbableGroups[p].firstTabbableNode}}}else n=u("fallbackFocus");n&&(t.preventDefault(),d(n))}(t)},v=function(t){var e=lr(t);a(e)>=0||ur(i.clickOutsideDeactivates,t)||ur(i.allowOutsideClick,t)||(t.preventDefault(),t.stopImmediatePropagation())},g=function(){if(o.active)return or.activateTrap(n),o.delayInitialFocusTimer=i.delayInitialFocus?sr((function(){d(l())})):d(l()),r.addEventListener("focusin",p,!0),r.addEventListener("mousedown",f,{capture:!0,passive:!1}),r.addEventListener("touchstart",f,{capture:!0,passive:!1}),r.addEventListener("click",v,{capture:!0,passive:!1}),r.addEventListener("keydown",m,{capture:!0,passive:!1}),n},y=function(){if(o.active)return r.removeEventListener("focusin",p,!0),r.removeEventListener("mousedown",f,!0),r.removeEventListener("touchstart",f,!0),r.removeEventListener("click",v,!0),r.removeEventListener("keydown",m,!0),n};return(n={get active(){return o.active},get paused(){return o.paused},activate:function(t){if(o.active)return this;var e=s(t,"onActivate"),n=s(t,"onPostActivate"),i=s(t,"checkCanFocusTrap");i||c(),o.active=!0,o.paused=!1,o.nodeFocusedBeforeActivation=r.activeElement,e&&e();var a=function(){i&&c(),g(),n&&n()};return i?(i(o.containers.concat()).then(a,a),this):(a(),this)},deactivate:function(t){if(!o.active)return this;var e=nr({onDeactivate:i.onDeactivate,onPostDeactivate:i.onPostDeactivate,checkCanReturnFocus:i.checkCanReturnFocus},t);clearTimeout(o.delayInitialFocusTimer),o.delayInitialFocusTimer=void 0,y(),o.active=!1,o.paused=!1,or.deactivateTrap(n);var r=s(e,"onDeactivate"),a=s(e,"onPostDeactivate"),u=s(e,"checkCanReturnFocus"),l=s(e,"returnFocus","returnFocusOnDeactivate");r&&r();var c=function(){sr((function(){l&&d(h(o.nodeFocusedBeforeActivation)),a&&a()}))};return l&&u?(u(h(o.nodeFocusedBeforeActivation)).then(c,c),this):(c(),this)},pause:function(){return o.paused||!o.active||(o.paused=!0,y()),this},unpause:function(){return o.paused&&o.active?(o.paused=!1,c(),g(),this):this},updateContainerElements:function(t){var e=[].concat(t).filter(Boolean);return o.containers=e.map((function(t){return"string"==typeof t?r.querySelector(t):t})),o.active&&c(),this}}).updateContainerElements(t),n};function dr(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e[1]=D({escapeDeactivates:!1},e[1]),cr.apply(void 0,e)}var hr=!1;if("undefined"!=typeof window){var fr={get passive(){hr=!0}};window.addEventListener("testPassive",null,fr),window.removeEventListener("testPassive",null,fr)}var pr,mr="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&window.navigator.maxTouchPoints>1),vr=[],gr=!1,yr=-1,br=void 0,wr=void 0,Dr=void 0,xr=function(t){return vr.some((function(e){return!(!e.options.allowTouchMove||!e.options.allowTouchMove(t))}))},Cr=function(t){var e=t||window.event;return!!xr(e.target)||e.touches.length>1||(e.preventDefault&&e.preventDefault(),!1)},Ar=function(){mr&&(vr.forEach((function(t){t.targetElement.ontouchstart=null,t.targetElement.ontouchmove=null})),gr&&(document.removeEventListener("touchmove",Cr,hr?{passive:!1}:void 0),gr=!1),yr=-1),mr?function(){if(void 0!==wr){var t=-parseInt(document.body.style.top,10),e=-parseInt(document.body.style.left,10);document.body.style.position=wr.position,document.body.style.top=wr.top,document.body.style.left=wr.left,window.scrollTo(e,t),wr=void 0}}():(void 0!==Dr&&(document.body.style.paddingRight=Dr,Dr=void 0),void 0!==br&&(document.body.style.overflow=br,br=void 0)),vr=[]},Er=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).focusTrap=void 0,e.lastSetFocusTrap=void 0,e.modalClosing=!1,e.keyUpListener=null,e}w(e,t);var n=e.prototype;return n.view=function(t){var e=this;return m("[",null,this.attrs.state.modalList.map((function(t,n){var r=null==t?void 0:t.componentClass;return m("div",{key:t.key,className:"ModalManager modal","data-modal-key":t.key,"data-modal-number":n,role:"dialog","aria-modal":"true",style:{"--modal-number":n},"aria-hidden":e.attrs.state.modal!==t&&"true"},!!r&&[m(r,Object.assign({key:t.key},t.attrs,{animateShow:e.animateShow.bind(e),animateHide:e.animateHide.bind(e),state:e.attrs.state})),m("div",{key:t.key,className:"ModalManager-invisibleBackdrop",onclick:e.handlePossibleBackdropClick.bind(e)})])})),this.attrs.state.backdropShown&&m("div",{className:"Modal-backdrop backdrop",ontransitionend:this.onBackdropTransitionEnd.bind(this),"data-showing":!!this.attrs.state.modalList.length,style:{"--modal-count":this.attrs.state.modalList.length}}))},n.oncreate=function(e){t.prototype.oncreate.call(this,e),this.keyUpListener=this.handleEscPress.bind(this),document.body.addEventListener("keyup",this.keyUpListener)},n.onbeforeremove=function(e){t.prototype.onbeforeremove.call(this,e),this.keyUpListener&&document.body.removeEventListener("keyup",this.keyUpListener),this.keyUpListener=null},n.onupdate=function(e){var n=this;t.prototype.onupdate.call(this,e),requestAnimationFrame((function(){try{var t,e;if(!n.attrs.state.isModalOpen())return null==(e=document.getElementById("app"))||e.setAttribute("aria-hidden","false"),null==n.focusTrap.deactivate||n.focusTrap.deactivate(),void Ar();null==(t=document.getElementById("app"))||t.setAttribute("aria-hidden","true");var r=n.attrs.state.modal.key;n.focusTrap&&n.lastSetFocusTrap!==r&&(null==n.focusTrap.deactivate||n.focusTrap.deactivate(),Ar()),n.activeDialogElement&&n.lastSetFocusTrap!==r&&(n.focusTrap=dr(n.activeDialogElement,{allowOutsideClick:!0}),null==n.focusTrap.activate||n.focusTrap.activate(),function(t,e){if(t){if(!vr.some((function(e){return e.targetElement===t}))){var n={targetElement:t,options:e||{}};vr=[].concat(function(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}(vr),[n]),mr?window.requestAnimationFrame((function(){if(void 0===wr){wr={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left};var t=window,e=t.scrollY,n=t.scrollX,r=t.innerHeight;document.body.style.position="fixed",document.body.style.top=-e,document.body.style.left=-n,setTimeout((function(){return window.requestAnimationFrame((function(){var t=r-window.innerHeight;t&&e>=r&&(document.body.style.top=-(e+t))}))}),300)}})):function(t){if(void 0===Dr){var e=!!t&&!0===t.reserveScrollBarGap,n=window.innerWidth-document.documentElement.clientWidth;if(e&&n>0){var r=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right"),10);Dr=document.body.style.paddingRight,document.body.style.paddingRight=r+n+"px"}}void 0===br&&(br=document.body.style.overflow,document.body.style.overflow="hidden")}(e),mr&&(t.ontouchstart=function(t){1===t.targetTouches.length&&(yr=t.targetTouches[0].clientY)},t.ontouchmove=function(e){1===e.targetTouches.length&&function(t,e){var n=t.targetTouches[0].clientY-yr;!xr(t.target)&&(e&&0===e.scrollTop&&n>0||function(t){return!!t&&t.scrollHeight-t.scrollTop<=t.clientHeight}(e)&&n<0?Cr(t):t.stopPropagation())}(e,t)},gr||(document.addEventListener("touchmove",Cr,hr?{passive:!1}:void 0),gr=!0))}}else console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.")}(n.activeDialogManagerElement,{reserveScrollBarGap:!0})),n.lastSetFocusTrap=r}catch(t){}}))},n.animateShow=function(t){var e=this;void 0===t&&(t=function(){}),this.attrs.state.modal&&(this.activeDialogElement.addEventListener("transitionend",(function(){t()}),{once:!0}),requestAnimationFrame((function(){e.activeDialogElement.classList.add("in")})))},n.animateHide=function(t){var e=this;void 0===t&&(t=function(){}),this.modalClosing||(this.modalClosing=!0,this.activeDialogElement.addEventListener("transitionend",(function(){e.modalClosing=!1,e.attrs.state.close(),t()}),{once:!0}),this.activeDialogElement.classList.remove("in"),this.activeDialogElement.classList.add("out"))},n.handleEscPress=function(t){if(this.attrs.state.modal){var e=this.attrs.state.modal.componentClass.dismissibleOptions;"Escape"===t.key&&e.viaEscKey&&(t.preventDefault(),this.animateHide())}},n.handlePossibleBackdropClick=function(t){this.attrs.state.modal&&this.attrs.state.modal.componentClass.dismissibleOptions.viaBackdropClick&&this.animateHide()},n.onBackdropTransitionEnd=function(t){"opacity"===t.propertyName&&null===t.currentTarget.getAttribute("data-showing")&&(this.attrs.state.backdropShown=!1,m.redraw())},F(e,[{key:"activeDialogElement",get:function(){var t;return document.body.querySelector('.ModalManager[data-modal-key="'+(null==(t=this.attrs.state.modal)?void 0:t.key)+'"] .Modal')}},{key:"activeDialogManagerElement",get:function(){var t;return document.body.querySelector('.ModalManager[data-modal-key="'+(null==(t=this.attrs.state.modal)?void 0:t.key)+'"]')}}]),e}(E),Nr=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.oninit=function(e){t.prototype.oninit.call(this,e),this.state=this.attrs.state},n.view=function(){var t=this,e=this.state.getActiveAlerts();return m("div",{className:"AlertManager"},Object.keys(e).map(Number).map((function(n){var r=e[n],i="error"===r.attrs.type;return m("div",{className:"AlertManager-alert",role:"alert","aria-live":i?"assertive":"polite"},m(r.componentClass,Object.assign({},r.attrs,{ondismiss:t.state.dismiss.bind(t.state,n)}),r.children))})))},e}(E),kr=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.className=function(){return"RequestErrorModal Modal--large"},n.title=function(){return!!this.attrs.error.xhr&&this.attrs.error.xhr.status+" "+this.attrs.error.xhr.statusText},n.content=function(){var t,e=this.attrs,n=e.error,r=e.formattedError;return t=r.length?r.join("\n\n"):n.response?JSON.stringify(n.response,null,2):n.responseText,m("div",{className:"Modal-body"},m("pre",null,this.attrs.error.options.method," ",this.attrs.error.options.url,m("br",null),m("br",null),t))},e}(_t),Sr=n(5387),Tr=n.n(Sr),Fr=n(1904),_r=n.n(Fr),Pr=n(8213),Br=n.n(Pr),Ir=n(2061),Or=n.n(Ir),Lr=n(8974),Mr=n.n(Lr),$r=n(9767),jr=n.n($r),Rr=n(402),Hr=n.n(Rr);function Ur(t){return t.reduce((function(t,e){return t.concat(Array.isArray(e)?Ur(e):e)}),[])}function qr(t){var e={};return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=r.length?r.map((function(t){return null===t?"null":void 0===t?"undefined":"function"==typeof t?t.toString():t instanceof Date?t.toISOString():JSON.stringify(t)})).join("|"):"_(no-args)_";if(Object.prototype.hasOwnProperty.call(e,o))return e[o];var s=t.apply(void 0,r);return e[o]=s,s}}function zr(t){for(var e=function(t){return/\s/.test(t)},n=[],r={},i=0,o=null,s=!1,a=0;a<t.length;){if(s&&(e(t[a])||"{"===t[a]))s=!1,o=t.slice(i,a),"{"===t[a]&&a--;else if(!s&&!e(t[a])){var u="{"===t[a];if(o&&u){var l=Vr(t,a);if(-1===l)throw new Error('Unbalanced curly braces in string: "'.concat(t,'"'));r[o]=t.slice(a+1,l),a=l,o=null}else o&&(n.push(o),o=null),s=!0,i=a}a++}return s&&(o=t.slice(i)),o&&n.push(o),{args:n,cases:r}}function Vr(t,e){for(var n=0,r=e+1;r<t.length;r++){var i=t.charAt(r);if("}"===i){if(0===n)return r;n--}else"{"===i&&n++}return-1}function Wr(t){return Gr(t.slice(1,-1),",",3)}function Gr(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];if(!t)return r;if(1===n)return r.push(t),r;var i=t.indexOf(e);if(-1===i)return r.push(t),r;var o=t.substring(0,i).trim(),s=t.substring(i+e.length+1).trim();return r.push(o),Gr(s,e,n-1,r)}function Yr(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Kr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Yr(Object(n),!0).forEach((function(e){Hr()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Yr(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Xr=0,Jr="other";function Qr(t,e){for(var n=0,r="",i=0,o={};n<t.length;){if("#"!==t[n]||i)r+=t[n];else{var s="__hashToken".concat(Xr++);r+="{".concat(s,", number}"),o[s]=e}"{"===t[n]?i++:"}"===t[n]&&i--,n++}return{caseBody:r,numberValues:o}}function Zr(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,o=zr(e),s=o.args,a=o.cases,u=parseInt(t);s.forEach((function(t){t.startsWith("offset:")&&(u-=parseInt(t.slice("offset:".length)))}));var l=[];if("PluralRules"in Intl){void 0!==pr&&pr.resolvedOptions().locale===n||(pr=new Intl.PluralRules(n));var c=pr.select(u);c!==Jr&&l.push(c)}1===u&&l.push("one"),l.push("=".concat(u),Jr);for(var d=0;d<l.length;d++){var h=l[d];if(h in a){var f=Qr(a[h],u),p=f.caseBody,m=f.numberValues;return i(p,Kr(Kr({},r),m))}}return t}var ti="other";function ei(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0,i=zr(e),o=i.cases;return t in o?r(o[t],n):ti in o?r(o.other,n):t}function ni(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],i=[],o=function(r,o,s,a,u,l){if(o)return{break:!0};var c=ri(t,s,a,u);if(!c)return i;var d=c.segmentIndex===a,h=t[c.segmentIndex],f=[];if(d)f.push(r.slice(u+1,c.segmentStart));else{f.push(r.slice(u+1));for(var p=a+1;p<c.segmentIndex;p++)f.push(t[p]);f.push(h.slice(0,c.segmentStart))}return i.push(r.slice(0,l)),i.push(n(s,e,ni(f.filter((function(t){return""!==t})),e,n))),t.splice(c.segmentIndex+1,0,h.slice(c.segmentEnd+1)),{processedSegment:!0,newSegmentIndex:c.segmentIndex,break:!0}};return ii(t,0,0,i,o),i.filter((function(t){return""!==t})).map((function(t,e){return r.includes(e)?t:si(t)}))}function ri(t,e,n,r){var i,o=1;return ii(t,n,r,[],(function(t,n,r,s,a,u){return r===e&&(n?o--:o++,0===o)?(i={segmentIndex:s,segmentStart:u,segmentEnd:a},{exit:!0}):{exit:!1}})),i}function ii(t,e,n,r,i){for(var o,s=e;s<t.length;s++){var a=t[s];if("string"==typeof a){for(var u=!1,l=null,c=!1,d=!1,h=s===e?n:0;h<a.length;h++)if(c||"<"!==a[h])if(c&&">"===a[h]){var f=a.slice(l+1+u,h),p=i(a,u,f,s,h,l);if(p.exit)return;if(p.newSegmentIndex&&(s=p.newSegmentIndex),p.processedSegment&&(d=!0),p.break)break;u=!1,l=null,c=!1}else c&&(o=a[h],!/[a-zA-Z-_]/.test(o))&&(u=!1,l=null,c=!1);else l=h,c=!0,"/"===a[h+1]&&(u=!0,h++);d||r.push(a)}else r.push(a)}}function oi(t){return Object.keys(t).reduce((function(e,n){return e[n]=ai(t[n]),e}),{})}function si(t){return"string"!=typeof t?t:t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#039;/g,"'").replace(/&amp;/g,"&")}function ai(t){return"string"==typeof t||t instanceof String?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"):Array.isArray(t)?t.map(ai):t}function ui(t,e,n){return"<".concat(t,">").concat(si(n),"</").concat(t,">")}var li=function(t){Or()(i,t);var e,n,r=(e=i,n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,r=jr()(e);if(n){var i=jr()(this).constructor;t=Reflect.construct(r,arguments,i)}else t=r.apply(this,arguments);return Mr()(this,t)});function i(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return _r()(this,i),(e=r.call(this,t,n)).richHandler=o||ui,e}return Br()(i,[{key:"rich",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=oi(e),r=Ur(this.process(t,n)),i=Math.random().toString(36)+Math.random().toString(36)+Math.random().toString(36),o=Object.fromEntries(Object.entries(e).map((function(t){var e=Tr()(t,2),n=e[0];return e[1],[n,i]}))),s=Ur(this.process(t,o)),a=ni(s,o,(function(){return i})),u=a.map((function(t,e){return t===i?null:e})).filter((function(t){return null!==t}));return ni(r,n,this.richHandler,u)}}]),i}(function(){function t(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};_r()(this,t),Hr()(this,"format",qr((function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Ur(n.process(t,e)).join("")}))),this.locale=e,this.typeHandlers=r}return Br()(t,[{key:"process",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)return[];var n=t.indexOf("{");if(-1!==n){var r=Vr(t,n);if(-1===r)throw new Error('Unbalanced curly braces in string: "'.concat(t,'"'));var i=t.substring(n,r+1);if(i){var o=[],s=t.substring(0,n);s&&o.push(s);var a=Wr(i),u=Tr()(a,3),l=u[0],c=u[1],d=u[2],h=e[l];null==h&&(h="");var f=c&&this.typeHandlers[c];o.push(f?f(h,d,this.locale,e,this.process.bind(this)):h);var p=t.substring(r+1);return p&&o.push(this.process(p,e)),o}}return[t]}}]),t}());function ci(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function di(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ci(Object(n),!0).forEach((function(e){Hr()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ci(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function hi(t,e,n){var r=e[t]||m(t),i=si(m.fragment(n).children);return di(di({},r),{},{children:i})}var fi=function(){function t(){this.translations={},this.formatter=new li(null,this.formatterTypeHandlers(),hi)}var e=t.prototype;return e.setLocale=function(t){this.formatter.locale=t},e.getLocale=function(){return this.formatter.locale},e.addTranslations=function(t){Object.assign(this.translations,t)},e.formatterTypeHandlers=function(){return{plural:Zr,select:ei}},e.preprocessParameters=function(t){if("user"in t){var e=z(t,"user");t.username||(t.username=Xt(e))}return t},e.trans=function(t,e){void 0===e&&(e={});var n=this.translations[t];return n?(e=this.preprocessParameters(e),this.formatter.rich(n,e)):t},t}();function pi(t){return Array.isArray(t.data)}var mi=function(){function t(t){this.data={},this.models=void 0,this.models=t}var e=t.prototype;return e.pushPayload=function(t){var e=this;t.included&&t.included.map(this.pushObject.bind(this));var n=t.data instanceof Array?t.data.map((function(t){return e.pushObject(t,!1)})):this.pushObject(t.data,!1);return n.payload=t,n},e.pushObject=function(t,e){if(void 0===e&&(e=!0),!this.models[t.type])return e||setTimeout((function(){return vt("Pushing object of type `"+t.type+"` not allowed, as type not yet registered in the store.","3206")})),null;var n=this.data[t.type]=this.data[t.type]||{},r=n[t.id],i=r?r.pushData(t):this.createRecord(t.type,t);return n[t.id]=i,i.exists=!0,i},e.find=function(){var t=Ee(ke().mark((function t(e,n,r,i){var o,s,a=this;return ke().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===r&&(r={}),void 0===i&&(i={}),o=r,s=v.forum.attribute("apiUrl")+"/"+e,n instanceof Array?s+="?filter[id]="+n.join(","):"object"==typeof n?o=n:n&&(s+="/"+n),t.abrupt("return",v.request(D({method:"GET",url:s,params:o},i)).then((function(t){return pi(t),a.pushPayload(t)})));case 6:case"end":return t.stop()}}),t)})));return function(e,n,r,i){return t.apply(this,arguments)}}(),e.getById=function(t,e){var n,r;return null==(n=this.data)||null==(r=n[t])?void 0:r[e]},e.getBy=function(t,e,n){return this.all(t).filter((function(t){return t[e]()===n}))[0]},e.all=function(t){var e=this.data[t];return e?Object.values(e):[]},e.remove=function(t){delete this.data[t.data.type][t.id()]},e.createRecord=function(t,e){return void 0===e&&(e={}),e.type=e.type||t,new this.models[t](e,this)},t}(),vi=function(){function t(t,e){this.user=void 0,this.csrfToken=void 0,this.user=t,this.csrfToken=e}var e=t.prototype;return e.login=function(t,e){return void 0===e&&(e={}),v.request(D({method:"POST",url:v.forum.attribute("baseUrl")+"/login",body:t},e))},e.logout=function(){window.location.href=v.forum.attribute("baseUrl")+"/logout?token="+this.csrfToken},t}(),gi=function(){function t(){var t=this;this.focusTrap=void 0,this.appElement=void 0,this.resizeHandler=function(e){!e.matches&&t.isOpen()&&t.hide()}.bind(this),this.drawerAvailableMediaQuery=void 0,document.getElementById("content").addEventListener("click",(function(e){t.isOpen()&&(e.preventDefault(),t.hide())})),this.appElement=document.getElementById("app"),this.focusTrap=dr("#drawer",{allowOutsideClick:!0,clickOutsideDeactivates:!0}),this.drawerAvailableMediaQuery=window.matchMedia("(max-width: "+getComputedStyle(document.documentElement).getPropertyValue("--screen-phone-max")+")")}var e=t.prototype;return e.isOpen=function(){return this.appElement.classList.contains("drawerOpen")},e.hide=function(){var t;if(this.focusTrap.deactivate(),this.drawerAvailableMediaQuery.removeListener(this.resizeHandler),this.isOpen()){var e=$("#drawer");e.css("visibility","visible").one("transitionend",(function(){return e.css("visibility","")})),this.appElement.classList.remove("drawerOpen"),null==(t=this.$backdrop)||null==t.remove||t.remove()}},e.show=function(){var t=this;this.appElement.classList.add("drawerOpen"),this.drawerAvailableMediaQuery.addListener(this.resizeHandler),this.$backdrop=$("<div/>").addClass("drawer-backdrop fade").appendTo("body").on("click",this.hide.bind(this)),requestAnimationFrame((function(){t.$backdrop.addClass("in"),t.focusTrap.activate()}))},t}();function yi(t,e){void 0===e&&(e="");var n={};for(var r in t){var i=t[r];if("resolver"in i)n[e+i.path]=i.resolver;else{if(!("component"in i))throw new Error("Either a resolver or a component must be provided for the route ["+r+"]");var o="resolverClass"in i?i.resolverClass:kn;n[e+i.path]=new o(i.component,r)}}return n}var bi=function(t,e,n,r){this.status=void 0,this.options=void 0,this.xhr=void 0,this.responseText=void 0,this.response=void 0,this.alert=void 0,this.status=t,this.responseText=e,this.options=n,this.xhr=r;try{this.response=JSON.parse(null!=e?e:"null")}catch(t){this.response=null}this.alert=null};function wi(){$("[data-humantime]").each((function(){var t=$(this),e=st(t.attr("datetime"));t.html(e)}))}function Di(){setInterval(wi,1e4)}function xi(t,e,n){(Array.isArray(e)?e:[e]).forEach((function(e){var r=t[e];t[e]=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];var o=r?r.apply(this,e):void 0;return n.apply(this,[o].concat(e)),o},Object.assign(t[e],r)}))}function Ci(t,e,n){(Array.isArray(e)?e:[e]).forEach((function(e){var r=t[e];t[e]=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return n.apply(this,[r.bind(this)].concat(e))},Object.assign(t[e],r)}))}var Ai=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.apiEndpoint=function(){return"/"},e}(fe),Ei=n(2898),Ni=n.n(Ei);function ki(t){for(var e=0,n=0;n<t.length;n++)e+=t.charCodeAt(n);var r=function(t,e,n){var r,i,o,s=Math.floor(6*t),a=6*t-s,u=.63,l=n*(1-.3*a),c=n*(1-.3*(1-a));switch(s%6){case 0:r=n,i=c,o=u;break;case 1:r=l,i=n,o=u;break;case 2:r=u,i=n,o=c;break;case 3:r=u,i=l,o=n;break;case 4:r=c,i=u,o=n;break;case 5:r=n,i=u,o=l}return{r:Math.floor(255*r),g:Math.floor(255*i),b:Math.floor(255*o)}}(e%360/360,0,.9);return""+r.r.toString(16)+r.g.toString(16)+r.b.toString(16)}var Si=function(t){function e(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(e=t.call.apply(t,[this].concat(r))||this).avatarColor=null,e}w(e,t);var n=e.prototype;return n.username=function(){return fe.attribute("username").call(this)},n.slug=function(){return fe.attribute("slug").call(this)},n.displayName=function(){return fe.attribute("displayName").call(this)},n.email=function(){return fe.attribute("email").call(this)},n.isEmailConfirmed=function(){return fe.attribute("isEmailConfirmed").call(this)},n.password=function(){return fe.attribute("password").call(this)},n.avatarUrl=function(){return fe.attribute("avatarUrl").call(this)},n.preferences=function(){return fe.attribute("preferences").call(this)},n.groups=function(){return fe.hasMany("groups").call(this)},n.isAdmin=function(){return fe.attribute("isAdmin").call(this)},n.joinTime=function(){return fe.attribute("joinTime",fe.transformDate).call(this)},n.lastSeenAt=function(){return fe.attribute("lastSeenAt",fe.transformDate).call(this)},n.markedAllAsReadAt=function(){return fe.attribute("markedAllAsReadAt",fe.transformDate).call(this)},n.unreadNotificationCount=function(){return fe.attribute("unreadNotificationCount").call(this)},n.newNotificationCount=function(){return fe.attribute("newNotificationCount").call(this)},n.discussionCount=function(){return fe.attribute("discussionCount").call(this)},n.commentCount=function(){return fe.attribute("commentCount").call(this)},n.canEdit=function(){return fe.attribute("canEdit").call(this)},n.canEditCredentials=function(){return fe.attribute("canEditCredentials").call(this)},n.canEditGroups=function(){return fe.attribute("canEditGroups").call(this)},n.canDelete=function(){return fe.attribute("canDelete").call(this)},n.color=function(){var t=this;return pe("displayName","avatarUrl","avatarColor",(function(e,n,r){return r?"rgb("+r.join(", ")+")":n?(t.calculateAvatarColor(),""):"#"+ki(e)})).call(this)},n.isOnline=function(){return dayjs().subtract(5,"minutes").isBefore(this.lastSeenAt())},n.badges=function(){var t=new B,e=this.groups();return e&&e.forEach((function(e){t.add("group"+(null==e?void 0:e.id()),m(qe,{group:e}))})),t},n.calculateAvatarColor=function(){var t,e=new Image,n=this;e.addEventListener("load",(function(){try{var t=new(Ni());n.avatarColor=t.getColor(this)}catch(t){if(!(t instanceof TypeError))throw t;n.avatarColor=[255,255,255]}n.freshness=new Date,m.redraw()})),e.crossOrigin="anonymous",e.src=null!=(t=this.avatarUrl())?t:""},n.savePreferences=function(t){var e=this.preferences();return Object.assign(null!=e?e:{},t),this.save({preferences:e})},e}(fe),Ti=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.number=function(){return fe.attribute("number").call(this)},n.discussion=function(){return fe.hasOne("discussion").call(this)},n.createdAt=function(){return fe.attribute("createdAt",fe.transformDate).call(this)},n.user=function(){return fe.hasOne("user").call(this)},n.contentType=function(){return fe.attribute("contentType").call(this)},n.content=function(){return fe.attribute("content").call(this)},n.contentHtml=function(){return fe.attribute("contentHtml").call(this)},n.renderFailed=function(){return fe.attribute("renderFailed").call(this)},n.contentPlain=function(){return pe("contentHtml",(function(t){return"string"==typeof t?J(t):t})).call(this)},n.editedAt=function(){return fe.attribute("editedAt",fe.transformDate).call(this)},n.editedUser=function(){return fe.hasOne("editedUser").call(this)},n.isEdited=function(){return pe("editedAt",(function(t){return!!t})).call(this)},n.hiddenAt=function(){return fe.attribute("hiddenAt",fe.transformDate).call(this)},n.hiddenUser=function(){return fe.hasOne("hiddenUser").call(this)},n.isHidden=function(){return pe("hiddenAt",(function(t){return!!t})).call(this)},n.canEdit=function(){return fe.attribute("canEdit").call(this)},n.canHide=function(){return fe.attribute("canHide").call(this)},n.canDelete=function(){return fe.attribute("canDelete").call(this)},e}(fe),Fi=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.contentType=function(){return fe.attribute("contentType").call(this)},n.content=function(){return fe.attribute("content").call(this)},n.createdAt=function(){return fe.attribute("createdAt",fe.transformDate).call(this)},n.isRead=function(){return fe.attribute("isRead").call(this)},n.user=function(){return fe.hasOne("user").call(this)},n.fromUser=function(){return fe.hasOne("fromUser").call(this)},n.subject=function(){return fe.hasOne("subject").call(this)},e}(fe),_i=function(){function t(){this.modal=null,this.modalList=[],this.backdropShown=!1,this.key=0}var e=t.prototype;return e.show=function(t,e,n){var r=this;if(void 0===e&&(e={}),void 0===n&&(n=!1),!(t.prototype instanceof _t)){var i="The ModalManager can only show Modals.";throw console.error(i),new Error(i)}this.backdropShown=!0,m.redraw.sync(),requestAnimationFrame((function(){r.modal={componentClass:t,attrs:e,key:r.key++},n?r.modalList.push(r.modal):r.modalList=[r.modal],m.redraw()}))},e.close=function(){this.modal&&(this.modalList.length>1?(this.modalList.pop(),this.modal=this.modalList[this.modalList.length-1]):(this.modal=null,this.modalList=[]),m.redraw())},e.isModalOpen=function(){return!!this.modal},t}(),Pi=function(){function t(){this.activeAlerts={},this.alertId=0}var e=t.prototype;return e.getActiveAlerts=function(){return this.activeAlerts},e.show=function(t,e,n){var r,i=Ft,o={};return 1==arguments.length?r=t:2==arguments.length?(o=t,r=e):3==arguments.length&&(i=t,o=e,r=n),this.activeAlerts[++this.alertId]={children:r,attrs:o,componentClass:i},m.redraw(),this.alertId},e.dismiss=function(t){t&&t in this.activeAlerts&&(delete this.activeAlerts[t],m.redraw())},e.clear=function(){this.activeAlerts={},m.redraw()},t}();function Bi(t,e,n){var r,i;console.group("%c"+e,"background-color: #d83e3e; color: #ffffff; font-weight: bold;"),console.error(n),console.groupEnd(),null!=(r=v.session)&&null!=(i=r.user)&&i.isAdmin()&&v.alerts.show({type:"error"},""+t)}var Ii=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.token=function(){return fe.attribute("token").call(this)},n.userId=function(){return fe.attribute("userId").call(this)},n.title=function(){return fe.attribute("title").call(this)},n.type=function(){return fe.attribute("type").call(this)},n.createdAt=function(){return fe.attribute("createdAt",fe.transformDate).call(this)},n.lastActivityAt=function(){return fe.attribute("lastActivityAt",fe.transformDate).call(this)},n.lastIpAddress=function(){return fe.attribute("lastIpAddress").call(this)},n.device=function(){return fe.attribute("device").call(this)},n.isCurrent=function(){return fe.attribute("isCurrent").call(this)},n.isSessionToken=function(){return fe.attribute("isSessionToken").call(this)},e}(fe),Oi=["background","deserialize","extract","modifyText"];function Li(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Mi=function(){function t(){this.forum=void 0,this.routes={},this.initializers=new B,this.session=void 0,this.translator=new fi,this.store=new mi({"access-tokens":Ii,forums:Ai,users:Si,discussions:we,posts:Ti,groups:ze,notifications:Fi}),this.cache={},this.booted=!1,this.current=new k(null),this.previous=new k(null),this.modal=new _i,this.alerts=new Pi,this.drawer=void 0,this.history=null,this.pane=null,this.data=void 0,this.refs={fontawesome:"https://fontawesome.com/v5/search?o=r&m=free"},this._title="",this._titleCount=0,this.requestErrorAlert=null,this.initialRoute=void 0}var e=t.prototype;return e.load=function(t){this.data=t,this.translator.setLocale(t.locale)},e.boot=function(){var t,e=this,n=[];this.initializers.toArray().forEach((function(t){try{t(e)}catch(e){var r=t.itemName.includes("/")?t.itemName.replace(/(\/flarum-ext-)|(\/flarum-)/g,"-"):t.itemName;n.push((function(){return Bi(ot(v.translator.trans("core.lib.error.extension_initialiation_failed_message",{extension:r})),r+" failed to initialize",e)}))}})),this.store.pushPayload({data:this.data.resources}),this.forum=this.store.getById("forums","1"),this.session=new vi(null!=(t=this.store.getById("users",String(this.data.session.userId)))?t:null,this.data.session.csrfToken),this.mount(),this.initialRoute=window.location.href,n.forEach((function(t){return t()}))},e.bootExtensions=function(t){var e=this;Object.keys(t).forEach((function(n){var r=t[n];if(r.extend)for(var i,o=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Li(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Li(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(r.extend.flat(1/0));!(i=o()).done;)i.value.extend(e,{name:n,exports:r})}))},e.mount=function(t){var e=this;void 0===t&&(t=""),m.mount(document.getElementById("modal"),{view:function(){return m(Er,{state:e.modal})}}),m.mount(document.getElementById("alerts"),{view:function(){return m(Nr,{state:e.alerts})}}),this.drawer=new gi,m.route(document.getElementById("content"),t+"/",yi(this.routes,t));var n=document.getElementById("app"),r=document.querySelector(".App-header"),i=new Yt((function(t){var e=n.getBoundingClientRect().top+document.body.scrollTop;n.classList.toggle("affix",t>=e),n.classList.toggle("scrolled",t>e),r.classList.toggle("navbar-fixed-top",t>=e)}));i.start(),i.update(),document.body.classList.add("ontouchstart"in window?"touch":"no-touch"),Di()},e.preloadedApiDocument=function(){if(this.data.apiDocument&&window.location.href===this.initialRoute){var t=(pi(this.data.apiDocument),this.store.pushPayload(this.data.apiDocument));return this.data.apiDocument=null,t}return null},e.screen=function(){return getComputedStyle(document.documentElement).getPropertyValue("--flarum-screen")},e.setTitle=function(t){this.title=t,this.updateTitle()},e.setTitleCount=function(t){this.titleCount=t,this.updateTitle()},e.updateTitle=function(){var t=this.titleCount?"("+this.titleCount+") ":"",e=m.route.get()===this.forum.attribute("basePath")+"/",n={pageTitle:this.title,forumName:this.forum.attribute("title"),pageNumber:1},r=e||!this.title?ot(v.translator.trans("core.lib.meta_titles.without_page_title",n)):ot(v.translator.trans("core.lib.meta_titles.with_page_title",n));r=t+r;var i=new DOMParser;document.title=i.parseFromString(r,"text/html").body.innerText},e.transformRequestOptions=function(t){var e=this,n=D({},t),r=n.background,i=n.deserialize,o=n.extract,s=n.modifyText,a=q(n,Oi),u=s||o,l=D({background:null==r||r,deserialize:null!=i?i:function(t){return t}},a);if(xi(l,"config",(function(t,n){n.setRequestHeader("X-CSRF-Token",e.session.csrfToken)})),l.method&&!["GET","POST"].includes(l.method)){var c=l.method;xi(l,"config",(function(t,e){e.setRequestHeader("X-HTTP-Method-Override",c)})),l.method="POST"}return l.extract=function(t){var e;e=u?u(t.responseText):t.responseText;var n=t.status;if(n<200||n>299)throw new bi(n,""+e,l,t);if(t.getResponseHeader){var r=t.getResponseHeader("X-CSRF-Token");r&&(v.session.csrfToken=r)}try{return""===e?null:JSON.parse(e)}catch(n){throw new bi(500,""+e,l,t)}},l},e.request=function(t){var e=this,n=this.transformRequestOptions(t);return this.requestErrorAlert&&this.alerts.dismiss(this.requestErrorAlert),m.request(n).catch((function(n){return e.requestErrorCatch(n,t.errorHandler)}))},e.requestErrorCatch=function(t,e){var n,r,i,o,s=null!=(n=null==(r=t.response)||null==(i=r.errors)?void 0:i.map((function(t){var e;return decodeURI(null!=(e=t.detail)?e:"")})))?n:[];switch(t.status){case 422:o=s.map((function(t){return[t,m("br",null)]})).flat().slice(0,-1);break;case 401:case 403:o=v.translator.trans("core.lib.error.permission_denied_message");break;case 404:case 410:o=v.translator.trans("core.lib.error.not_found_message");break;case 413:o=v.translator.trans("core.lib.error.payload_too_large_message");break;case 429:o=v.translator.trans("core.lib.error.rate_limit_exceeded_message");break;default:o=this.requestWasCrossOrigin(t)?v.translator.trans("core.lib.error.generic_cross_origin_message"):v.translator.trans("core.lib.error.generic_message")}var a=v.forum.attribute("debug");return t.alert={type:"error",content:o,controls:a&&[m(yt,{className:"Button Button--link",onclick:this.showDebug.bind(this,t,s)},v.translator.trans("core.lib.debug_button"))]},e?e(t):this.requestErrorDefaultHandler(t,a,s),Promise.reject(t)},e.requestWasCrossOrigin=function(t){return new URL(t.options.url,document.baseURI).origin!==window.location.origin},e.requestErrorDefaultHandler=function(t,e,n){if(!(t instanceof bi))throw t;if(e&&t.xhr){var r,i=t.options,o=i.method,s=i.url,a=t.xhr.status,u=void 0===a?"":a;console.group(o+" "+s+" "+u),n.length?(r=console).error.apply(r,n):console.error(t),console.groupEnd()}t.alert&&(this.requestErrorAlert=this.alerts.show(t.alert,t.alert.content))},e.showDebug=function(t,e){null!==this.requestErrorAlert&&this.alerts.dismiss(this.requestErrorAlert),this.modal.show(kr,{error:t,formattedError:e})},e.route=function(t,e){void 0===e&&(e={});var n=this.routes[t];if(!n)throw new Error("Route '"+t+"' does not exist");var r=n.path.replace(/:([^\/]+)/g,(function(t,n){return""+z(e,n)}));for(var i in e)e.hasOwnProperty(i)&&!e[i]&&delete e[i];var o=m.buildQueryString(e);return(""===m.route.prefix?this.forum.attribute("basePath"):"")+r+(o?"?"+o:"")},F(t,[{key:"title",get:function(){return this._title},set:function(t){this._title=t}},{key:"titleCount",get:function(){return this._titleCount},set:function(t){this._titleCount=t}}]),t}(),$i=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=v.history,e=v.pane;return m("div",{className:M("Navigation ButtonGroup",this.attrs.className),onmouseenter:e&&e.show.bind(e),onmouseleave:e&&e.onmouseleave.bind(e)},null!=t&&t.canGoBack()?[this.getBackButton(),this.getPaneButton()]:this.getDrawerButton())},n.getBackButton=function(){var t=v.history,e=null==t?void 0:t.getPrevious();return m(le,{className:"Button Navigation-back Button--icon",href:null==t?void 0:t.backUrl(),icon:"fas fa-chevron-left","aria-label":null==e?void 0:e.title,onclick:function(e){e.shiftKey||e.ctrlKey||e.metaKey||2===e.which||(e.preventDefault(),null==t||t.back())}})},n.getPaneButton=function(){var t=v.pane;return t&&t.active?m(yt,{className:M("Button Button--icon Navigation-pin",{active:t.pinned}),onclick:t.togglePinned.bind(t),icon:"fas fa-thumbtack"}):null},n.getDrawerButton=function(){if(!this.attrs.drawer)return null;var t=v.drawer,e=v.session.user;return m(yt,{className:M("Button Button--icon Navigation-drawer",{new:null==e?void 0:e.newNotificationCount()}),onclick:function(e){e.stopPropagation(),t.show()},icon:"fas fa-bars","aria-label":v.translator.trans("core.lib.nav.drawer_button")})},e}(E),ji=function(t){function e(){return t.call(this,{},1,20)||this}w(e,t);var n=e.prototype;return n.load=function(){var e,n;return null!=(e=Wi.session.user)&&e.newNotificationCount()&&(this.pages=[],this.location={page:1}),this.pages.length>0?Promise.resolve():(null==(n=Wi.session.user)||n.pushAttributes({newNotificationCount:0}),t.prototype.loadNext.call(this))},n.markAllAsRead=function(){var t;if(0!==this.pages.length)return null==(t=Wi.session.user)||t.pushAttributes({unreadNotificationCount:0}),this.pages.forEach((function(t){t.items.forEach((function(t){return t.pushAttributes({isRead:!0})}))})),Wi.request({url:Wi.forum.attribute("apiUrl")+"/notifications/read",method:"POST"})},n.deleteAll=function(){var t;if(0!==this.pages.length)return null==(t=Wi.session.user)||t.pushAttributes({unreadNotificationCount:0}),this.pages=[],Wi.request({url:Wi.forum.attribute("apiUrl")+"/notifications",method:"DELETE"})},F(e,[{key:"type",get:function(){return"notifications"}}]),e}(hn),Ri=function(){function t(t){void 0===t&&(t=[]),this.cachedSearches=void 0,this.value="",this.cachedSearches=new Set(t)}var e=t.prototype;return e.getInitialSearch=function(){return""},e.getValue=function(){return this.value},e.setValue=function(t){this.value=t},e.clear=function(){this.setValue("")},e.cache=function(t){this.cachedSearches.add(t)},e.isCached=function(t){return this.cachedSearches.has(t)},t}(),Hi=["q"],Ui=function(t){function e(e){var n;return void 0===e&&(e=[]),(n=t.call(this,e)||this).initialValueSet=!1,n}w(e,t);var n=e.prototype;return n.getValue=function(){return!this.initialValueSet&&this.currPageProvidesSearch()&&this.intializeValue(),t.prototype.getValue.call(this)},n.intializeValue=function(){this.setValue(this.getInitialSearch()),this.initialValueSet=!0},n.currPageProvidesSearch=function(){return Wi.current.type&&Wi.current.type.providesInitialSearch},n.getInitialSearch=function(){return this.currPageProvidesSearch()?this.params().q:""},n.clear=function(){t.prototype.clear.call(this),this.getInitialSearch()?this.clearInitialSearch():m.redraw()},n.clearInitialSearch=function(){var t=this.params(),e=(t.q,q(t,Hi));x(Wi.route(Wi.current.get("routeName"),e))},n.stickyParams=function(){return{sort:m.route.param("sort"),q:m.route.param("q")}},n.params=function(){var t=this.stickyParams();return t.filter=m.route.param("filter"),t},n.changeSort=function(t){var e=this.params();t===Object.keys(Wi.discussions.sortMap())[0]?delete e.sort:e.sort=t,x(Wi.route(Wi.current.get("routeName"),e))},e}(Ri);function qi(){return"ontouchstart"in window&&null!=navigator.vendor&&navigator.vendor.includes("Apple")&&null!=navigator.userAgent&&!navigator.userAgent.includes("CriOS")&&!navigator.userAgent.includes("FxiOS")}var zi=function(t){function e(){var e;return(e=t.call(this)||this).notificationComponents={discussionRenamed:$e},e.postComponents={comment:rn,discussionRenamed:sn},e.pane=null,e.history=new C,e.notifications=new ji,e.search=new Ui,e.composer=new Oe,e.discussions=new vn({}),e.route=void 0,e.data=void 0,In(y(e)),e.route=Object.assign(Object.getPrototypeOf(Object.getPrototypeOf(y(e))).route.bind(y(e)),function(t){return{discussion:function(t){function e(e,n){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(e,n){return t.route(n&&1!==n?"discussion.near":"discussion",{id:e.slug(),near:n&&1!==n?n:void 0})})),post:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(e){return t.route.discussion(e.discussion(),e.number())})),user:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(e){return t.route("user",{username:e.slug()})}))}}(y(e))),e}w(e,t);var n=e.prototype;return n.mount=function(){var e=this,n=this.forum.attribute("defaultRoute"),r="index";for(var i in this.routes)this.routes[i].path===n&&(r=i);this.routes[r].path="/",this.history.push(r,ot(this.translator.trans("core.forum.header.back_to_index_tooltip")),"/"),this.pane=new A(document.getElementById("app")),m.route.prefix="",t.prototype.mount.call(this,this.forum.attribute("basePath")),m.mount(document.getElementById("app-navigation"),{view:function(){return m($i,{className:"App-backControl",drawer:!0})}}),m.mount(document.getElementById("header-navigation"),$i),m.mount(document.getElementById("header-primary"),ue),m.mount(document.getElementById("header-secondary"),Pe),m.mount(document.getElementById("composer"),{view:function(){return m(Le,{state:e.composer})}}),On(this),document.getElementById("home-link").addEventListener("click",(function(t){var e;if(!t.ctrlKey&&!t.metaKey&&1!==t.button){t.preventDefault(),Wi.history.home();var n=null==(e=Wi.session.user)?void 0:e.id();n&&(Wi.store.find("users",n),m.redraw())}})),qi()&&$((function(){$(".App").addClass("mobile-safari")}))},n.viewingDiscussion=function(t){return this.current.matches(ae,{discussion:t})},n.authenticationComplete=function(t){t.loggedIn?window.location.reload():this.modal.show(Lt,t)},e}(Mi),Vi=new zi;window.app=Vi;const Wi=Vi;function Gi(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var Yi=function(){function t(t){this.model=void 0,this.callbacks=[],this.model=t}var e=t.prototype;return e.attribute=function(t,e){var n=this;return void 0===e&&(e=null),this.callbacks.push((function(){n.model.prototype[t]=e?fe.attribute(t,e):fe.attribute(t)})),this},e.hasOne=function(t){var e=this;return this.callbacks.push((function(){e.model.prototype[t]=fe.hasOne(t)})),this},e.hasMany=function(t){var e=this;return this.callbacks.push((function(){e.model.prototype[t]=fe.hasMany(t)})),this},e.extend=function(t,e){for(var n,r=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,e){if(t){if("string"==typeof t)return Gi(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Gi(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){n&&(t=n);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.callbacks);!(n=r()).done;)n.value.call(this)},t}(),Ki=function(){function t(){this.postComponents={}}var e=t.prototype;return e.add=function(t,e){return this.postComponents[t]=e,this},e.extend=function(t,e){Object.assign(t.postComponents,this.postComponents)},t}(),Xi=function(){function t(){this.routes={},this.helpers={}}var e=t.prototype;return e.add=function(t,e,n){return this.routes[t]={path:e,component:n},this},e.helper=function(t,e){return this.helpers[t]=e,this},e.extend=function(t,e){Object.assign(t.routes,this.routes),Object.assign(t.route,this.helpers)},t}(),Ji=function(){function t(){this.models={}}var e=t.prototype;return e.add=function(t,e){return this.models[t]=e,this},e.extend=function(t,e){for(var n in this.models){if(t.store.models[n])throw new Error('The model type "'+n+'" has already been registered with the class "'+t.store.models[n].name+'".');t.store.models[n]=this.models[n]}},t}();const Qi={Model:Yi,PostTypes:Ki,Routes:Xi,Store:Ji};var Zi="The `evented` util is deprecated and no longer supported.",to="2547";const eo={handlers:null,getHandlers:function(t){return vt(Zi,to),this.handlers=this.handlers||{},this.handlers[t]=this.handlers[t]||[],this.handlers[t]},trigger:function(t){for(var e=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];vt(Zi,to),this.getHandlers(t).forEach((function(t){return t.apply(e,r)}))},on:function(t,e){vt(Zi,to),this.getHandlers(t).push(e)},one:function(t,e){vt(Zi,to),this.getHandlers(t).push((function n(){e.apply(this,arguments),this.off(t,n)}))},off:function(t,e){vt(Zi,to);var n=this.getHandlers(t),r=n.indexOf(e);-1!==r&&n.splice(r,1)}};var no={prefix:"",suffix:"",blockPrefix:"",blockSuffix:"",multiline:!1,replaceNext:"",prefixSpace:!1,scanFor:"",surroundWithNewlines:!1,orderedList:!1,unorderedList:!1,trimFirst:!1};function ro(t){return t.trim().split("\n").length>1}function io(t,e){return Array(e+1).join(t)}function oo(t){var e,n,r=t.value.slice(0,t.selectionStart),i=t.value.slice(t.selectionEnd),o=r.match(/\n*$/),s=i.match(/^\n*/),a=o?o[0].length:0,u=s?s[0].length:0;return r.match(/\S/)&&a<2&&(e=io("\n",2-a)),i.match(/\S/)&&u<2&&(n=io("\n",2-u)),null==e&&(e=""),null==n&&(n=""),{newlinesToAppend:e,newlinesToPrepend:n}}function so(t){var e=t.split("\n"),n=/^\d+\.\s+/,r=e.every((function(t){return n.test(t)})),i=e;return r&&(i=e.map((function(t){return t.replace(n,"")}))),{text:i.join("\n"),processed:r}}function ao(t){var e=t.split("\n"),n=e.every((function(t){return t.startsWith("- ")})),r=e;return n&&(r=e.map((function(t){return t.slice("- ".length,t.length)}))),{text:r.join("\n"),processed:n}}function uo(t,e){return e?"- ":t+1+". "}var lo,co=n(8492);function ho(t,e){var n,r,i=new RegExp(String.raw(lo||(n=["(w+/)?(","|common)/"],(r=["(\\w+\\/)?(","|common)\\/"])||(r=n.slice(0)),n.raw=r,lo=n),e)),o=/(\.js|\.tsx?)$/;return new Proxy(t,{get:function(t,e){return t[e]||t[e.replace(i,"$1").replace(o,"")]}})}var fo=["className","class"],po=["options","onchange","value","disabled","className","class","wrapperAttrs"],mo=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(){var t=this.attrs,e=t.options,n=t.onchange,r=t.value,i=t.disabled,o=t.className,s=t.class,a=t.wrapperAttrs,u=(a=void 0===a?{}:a).className,l=a.class,c=q(a,fo),d=q(t,po);return m("span",Object.assign({className:M("Select",u,l)},c),m("select",Object.assign({className:M("Select-input FormControl",o,s),onchange:n?yn("value",n.bind(this)):void 0,value:r,disabled:i},d),Object.keys(e).map((function(t){return m("option",{value:t},e[t])}))),nt("fas fa-sort",{className:"Select-caret"}))},e}(E),vo=["className","id"],go=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(t){var e=this.attrs,n=e.className,r=e.id,i=q(e,vo);return i.type||(i.type="text"),4===i.value.length&&(i.value=i.value.replace(/#([a-f0-9])([a-f0-9])([a-f0-9])/,"#$1$1$2$2$3$3")),m("div",{className:"ColorInput"},m("input",Object.assign({className:M("FormControl",n),id:r},i)),m("span",{className:"ColorInput-icon",role:"presentation"},nt("fas fa-exclamation-circle")),m("input",Object.assign({className:"ColorInput-preview"},i,{type:"color"})))},e}(E),yo=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.prototype.view=function(e){var n=t.prototype.view.call(this,e);return m(Et,{text:this.attrs.tooltipText||ot(e.children)},n)},e.initAttrs=function(e){t.initAttrs.call(this,e),e.className=e.className||"Button Button--icon Button--link",e.tooltipText=e.title},e}(yt);const bo={extenders:Qi,extend:o,Session:vi,Store:mi,"utils/BasicEditorDriver":Ct,"utils/bidi":h,"utils/evented":eo,"utils/EventEmitter":fn,"utils/KeyboardNavigatable":Ce,"utils/liveHumanTimes":Di,"utils/ItemList":B,"utils/mixin":function(t){for(var e=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e}(t),n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return r.forEach((function(t){Object.assign(e.prototype,t)})),e},"utils/humanTime":st,"utils/computed":pe,"utils/insertText":xt,"utils/styleSelectedText":function(t,e){t.focus(),e=Object.assign({},no,e);var n,r=t.value.slice(t.selectionStart,t.selectionEnd);n=e.orderedList||e.unorderedList?function(t,e){var n=t.selectionStart===t.selectionEnd,r=t.selectionStart,i=t.selectionEnd;!function(t){for(var e=t.value.split("\n"),n=0,r=0;r<e.length;r++){var i=e[r].length+1;t.selectionStart>=n&&t.selectionStart<n+i&&(t.selectionStart=n),t.selectionEnd>=n&&t.selectionEnd<n+i&&(t.selectionEnd=n+i-1),n+=i}}(t);var o=t.value.slice(t.selectionStart,t.selectionEnd),s=function(t,e){var n,r,i;return i=t.orderedList?(n=ao((r=so(e)).text)).text:(n=so((r=ao(e)).text)).text,[r,n,i]}(e,o),a=s[0],u=s[1],l=s[2],c=l.split("\n").map((function(t,n){return""+uo(n,e.unorderedList)+t})),d=c.reduce((function(t,n,r){return t+uo(r,e.unorderedList).length}),0),h=c.reduce((function(t,n,r){return t+uo(r,!e.unorderedList).length}),0);if(a.processed)return n?i=r=Math.max(r-uo(0,e.unorderedList).length,0):(r=t.selectionStart,i=t.selectionEnd-d),{text:l,selectionStart:r,selectionEnd:i};var f=oo(t),p=f.newlinesToAppend,m=f.newlinesToPrepend,v=p+c.join("\n")+m;return n?i=r=Math.max(r+uo(0,e.unorderedList).length+p.length,0):u.processed?(r=Math.max(t.selectionStart+p.length,0),i=t.selectionEnd+p.length+d-h):(r=Math.max(t.selectionStart+p.length,0),i=t.selectionEnd+p.length+d),{text:v,selectionStart:r,selectionEnd:i}}(t,e):e.multiline&&ro(r)?function(t,e){var n=e.prefix,r=e.suffix,i=e.blockPrefix,o=e.blockSuffix,s=e.surroundWithNewlines,a=t.value.slice(t.selectionStart,t.selectionEnd),u=t.selectionStart,l=t.selectionEnd,c=a.split("\n"),d=i.length>0?i:n,h=o.length>0?o:d==n?r:"";if(c.every((function(t){return t.startsWith(d)&&t.endsWith(h)})))l=u+(a=c.map((function(t){return t.slice(d.length,t.length-h.length)})).join("\n")).length;else if(a=c.map((function(t){return d+t+h})).join("\n"),s||""===h){var f=oo(t),p=f.newlinesToAppend,m=f.newlinesToPrepend;l=(u+=p.length)+a.length,a=p+a+m}return{text:a,selectionStart:u,selectionEnd:l}}(t,e):function(t,e){var n=e.prefix,r=e.suffix,i=e.blockPrefix,o=e.blockSuffix,s=e.replaceNext,a=e.prefixSpace,u=e.scanFor,l=e.surroundWithNewlines,c=t.selectionStart,d=t.selectionEnd,h=t.value.slice(t.selectionStart,t.selectionEnd),f=ro(h)&&i.length>0?i+"\n":n,p=ro(h)&&o.length>0?"\n"+o:f===n?r:"";if(a){var m=t.value[t.selectionStart-1];0===t.selectionStart||null==m||m.match(/\s/)||(f=" "+f)}h=function(t,e,n,r){if(void 0===r&&(r=!1),t.selectionStart===t.selectionEnd)t.selectionStart=function(t,e){for(var n=e;t[n]&&null!=t[n-1]&&!t[n-1].match(/\s/);)n--;return n}(t.value,t.selectionStart),t.selectionEnd=function(t,e,n){for(var r=e,i=n?/\n/:/\s/;t[r]&&!t[r].match(i);)r++;return r}(t.value,t.selectionEnd,r);else{var i=t.selectionStart-e.length,o=t.selectionEnd+n.length,s=t.value.slice(i,t.selectionStart)===e,a=t.value.slice(t.selectionEnd,o)===n;s&&a&&(t.selectionStart=i,t.selectionEnd=o)}return t.value.slice(t.selectionStart,t.selectionEnd)}(t,f,p,e.multiline);var v=t.selectionStart,g=t.selectionEnd,y=s.length>0&&p.indexOf(s)>-1&&h.length>0;if(l){var b=oo(t);f=b.newlinesToAppend+n,p+=b.newlinesToPrepend}if(h.startsWith(f)&&h.endsWith(p)){var w=h.slice(f.length,h.length-p.length);if(c===d){var D=c-f.length;D=Math.max(D,v),v=g=D=Math.min(D,v+w.length)}else g=v+w.length;return{text:w,selectionStart:v,selectionEnd:g}}if(y)return u.length>0&&h.match(u)?{text:f+(p=p.replace(s,h)),selectionStart:v=g=v+f.length,selectionEnd:g}:{text:f+h+p,selectionStart:v=v+f.length+h.length+p.indexOf(s),selectionEnd:g=v+s.length};var x=f+h+p;v=c+f.length,g=d+f.length;var C=h.match(/^\s*|\s*$/g);if(e.trimFirst&&C){var A=C[0]||"",E=C[1]||"";x=A+f+h.trim()+p+E,v+=A.length,g-=E.length}return{text:x,selectionStart:v,selectionEnd:g}}(t,e),xt(t,n)},"utils/Drawer":gi,"utils/EditorDriverInterface":co,"utils/anchorScroll":oe,"utils/RequestError":bi,"utils/abbreviateNumber":at,"utils/string":t,"utils/SubtreeRetainer":dt,"utils/escapeRegExp":Ut,"utils/extract":z,"utils/ScrollListener":Yt,"utils/stringToColor":ki,"utils/Stream":Bt,"utils/subclassOf":N,"utils/setRouteWithForcedRefresh":x,"utils/patchMithril":f,"utils/proxifyCompat":ho,"utils/classList":M,"utils/extractText":ot,"utils/formatNumber":te,"utils/mapRoutes":yi,"utils/withAttr":yn,"utils/throttleDebounce":s,"utils/isObject":_,"utils/focusTrap":i,"utils/isDark":me,"models/AccessToken":Ii,"models/Notification":Fi,"models/User":Si,"models/Post":Ti,"models/Discussion":we,"models/Group":ze,"models/Forum":Ai,Component:E,Fragment:function(){function t(){this.element=void 0}var e=t.prototype;return e.$=function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){var e=$(this.element);return t?e.find(t):e})),e.render=function(){var t=this,e=this.view();e.attrs=e.attrs||{};var n=e.attrs.oncreate;return e.attrs.oncreate=function(e){t.element=e.dom,n&&n.apply(t,[e])},e},t}(),Translator:fi,"components/AlertManager":Nr,"components/Page":S,"components/Switch":wn,"components/Badge":be,"components/LoadingIndicator":ft,"components/Placeholder":zt,"components/Separator":O,"components/Dropdown":ut,"components/SplitDropdown":ne,"components/RequestErrorModal":kr,"components/FieldSet":Dn,"components/Select":mo,"components/Navigation":$i,"components/Alert":Ft,"components/Link":G,"components/LinkButton":le,"components/Checkbox":bn,"components/ColorPreviewInput":go,"components/ConfirmDocumentUnload":pt,"components/SelectDropdown":he,"components/ModalManager":Er,"components/Button":yt,"components/Modal":_t,"components/GroupBadge":qe,"components/TextEditor":Nt,"components/TextEditorButton":yo,"components/Tooltip":Et,"components/EditUserModal":Ve,"components/LabelValue":Tn,"components/IPAddress":tn,Model:fe,Application:Mi,"helpers/fullTime":Ze,"helpers/avatar":Y,"helpers/fireApplicationError":Bi,"helpers/fireDebugWarning":e,"helpers/icon":nt,"helpers/humanTime":lt,"helpers/punctuateSeries":function(t){if(2===t.length)return v.translator.trans("core.lib.series.two_text",{first:t[0],second:t[1]});if(t.length>=3){var e=t.slice(1,t.length-1).reduce((function(t,e){return t.concat([e,v.translator.trans("core.lib.series.glue_text")])}),[]).slice(0,-1);return v.translator.trans("core.lib.series.three_text",{first:t[0],second:e,third:t[t.length-1]})}return t},"helpers/highlight":et,"helpers/username":Xt,"helpers/userOnline":Je,"helpers/listItems":H,"helpers/textContrastClass":ve,"resolvers/DefaultResolver":kn,"states/PaginatedListState":hn,"states/AlertManagerState":Pi,"states/ModalManagerState":_i,"states/PageState":k};var wo=function(t){function e(){return t.apply(this,arguments)||this}return w(e,t),e.initAttrs=function(e){e.className=M(e.className,"LogInButton"),e.onclick=function(){var t=$(window);window.open(Wi.forum.attribute("baseUrl")+e.path,"logInPopup","width=580,height=400,top="+(t.height()/2-200)+",left="+(t.width()/2-290)+",status=no,scrollbars=yes,resizable=no")},t.initAttrs.call(this,e)},e}(yt),Do=function(t){function e(){return t.apply(this,arguments)||this}w(e,t);var n=e.prototype;return n.view=function(){var t=this.attrs.post,e=t.user();return m(G,{className:"PostPreview",href:Wi.route.post(t),onclick:this.attrs.onclick},m("span",{className:"PostPreview-content"},Y(e),Xt(e)," ",m("span",{className:"PostPreview-excerpt"},this.excerpt())))},n.content=function(){return"comment"===this.attrs.post.contentType()&&this.attrs.post.contentPlain()},n.excerpt=function(){return this.content()?et(this.content(),this.attrs.highlight,300):""},e}(E);const xo=Object.assign(bo,{"utils/PostControls":He,"utils/KeyboardNavigatable":Ce,"utils/slidable":Rt,"utils/History":C,"utils/DiscussionControls":jt,"utils/alertEmailConfirmation":On,"utils/UserControls":Ye,"utils/Pane":A,"utils/BasicEditorDriver":Ct,"utils/isSafariMobile":qi,"states/ComposerState":Oe,"states/DiscussionListState":vn,"states/GlobalSearchState":Ui,"states/NotificationListState":ji,"states/PostStreamState":se,"states/SearchState":Ri,"states/UserSecurityPageState":Pn,"components/AffixedSidebar":We,"components/DiscussionPage":ae,"components/DiscussionListPane":Gt,"components/LogInModal":Mt,"components/NewAccessTokenModal":_n,"components/ComposerBody":kt,"components/ForgotPasswordModal":It,"components/Notification":Me,"components/LogInButton":wo,"components/DiscussionsUserPage":gn,"components/Composer":Le,"components/SessionDropdown":ce,"components/HeaderPrimary":ue,"components/PostEdited":nn,"components/PostStream":Zt,"components/ChangePasswordModal":Cn,"components/IndexPage":cn,"components/DiscussionRenamedNotification":$e,"components/DiscussionsSearchSource":Te,"components/DiscussionsSearchItem":Se,"components/HeaderSecondary":Pe,"components/ComposerButton":Be,"components/DiscussionList":Vt,"components/ReplyPlaceholder":Qt,"components/AvatarEditor":Ke,"components/Post":Ue,"components/SettingsPage":En,"components/TerminalPost":ct,"components/ChangeEmailModal":An,"components/NotificationsDropdown":xe,"components/UserPage":Ge,"components/PostUser":Qe,"components/UserCard":Xe,"components/UsersSearchSource":Fe,"components/UserSecurityPage":Bn,"components/NotificationGrid":xn,"components/PostPreview":Do,"components/EventPost":on,"components/DiscussionHero":U,"components/PostMeta":en,"components/DiscussionRenamedPost":sn,"components/DiscussionComposer":ln,"components/LogInButtons":Ot,"components/NotificationList":De,"components/WelcomeHero":un,"components/SignUpModal":Lt,"components/CommentPost":rn,"components/ComposerPostPreview":Jt,"components/ReplyComposer":Tt,"components/NotificationsPage":Nn,"components/PostStreamScrubber":ee,"components/EditPostComposer":Re,"components/RenameDiscussionModal":$t,"components/Search":_e,"components/DiscussionListItem":qt,"components/LoadingPost":Kt,"components/PostsUserPage":dn,"components/AccessTokensList":Fn,"resolvers/DiscussionPageResolver":Sn,routes:In,ForumApplication:zi});xo.app=Wi;var Co=ho(xo,"forum")})(),flarum.core=r})();
//# sourceMappingURL=forum.js.map
<IfModule mod_rewrite.c>
  RewriteEngine on

  # Ensure the Authorization HTTP header is available to PHP
  RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

  # Uncomment the following lines if you are not using a `public` directory
  # to prevent sensitive resources from being exposed.
  # <!-- BEGIN EXPOSED RESOURCES PROTECTION -->
  # RewriteRule /\.git / [F,L]
  # RewriteRule ^auth\.json$ / [F,L]
  # RewriteRule ^composer\.(lock|json)$ / [F,L]
  # RewriteRule ^config.php$ / [F,L]
  # RewriteRule ^flarum$ / [F,L]
  # RewriteRule ^storage/(.*)?$ / [F,L]
  # RewriteRule ^vendor/(.*)?$ / [F,L]
  # <!-- END EXPOSED RESOURCES PROTECTION -->

  # Pass requests that don't refer directly to files in the filesystem to index.php
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule ^ index.php [QSA,L]
</IfModule>

# Disable directory listings
Options -Indexes

# MultiViews can mess up our rewriting scheme
Options -MultiViews

# The following directives are based on best practices from H5BP Apache Server Configs
# https://github.com/h5bp/server-configs-apache

# Expire rules for static content
<IfModule mod_expires.c>
  ExpiresActive on
  ExpiresDefault                                      "access plus 1 month"
  ExpiresByType text/css                              "access plus 1 year"
  ExpiresByType application/atom+xml                  "access plus 1 hour"
  ExpiresByType application/rdf+xml                   "access plus 1 hour"
  ExpiresByType application/rss+xml                   "access plus 1 hour"
  ExpiresByType application/json                      "access plus 0 seconds"
  ExpiresByType application/ld+json                   "access plus 0 seconds"
  ExpiresByType application/schema+json               "access plus 0 seconds"
  ExpiresByType application/vnd.geo+json              "access plus 0 seconds"
  ExpiresByType application/vnd.api+json              "access plus 0 seconds"
  ExpiresByType application/xml                       "access plus 0 seconds"
  ExpiresByType text/calendar                         "access plus 0 seconds"
  ExpiresByType text/xml                              "access plus 0 seconds"
  ExpiresByType image/vnd.microsoft.icon              "access plus 1 week"
  ExpiresByType image/x-icon                          "access plus 1 week"
  ExpiresByType text/html                             "access plus 0 seconds"
  ExpiresByType application/javascript                "access plus 1 year"
  ExpiresByType application/x-javascript              "access plus 1 year"
  ExpiresByType text/javascript                       "access plus 1 year"
  ExpiresByType application/manifest+json             "access plus 1 week"
  ExpiresByType application/x-web-app-manifest+json   "access plus 0 seconds"
  ExpiresByType text/cache-manifest                   "access plus 0 seconds"
  ExpiresByType text/markdown                         "access plus 0 seconds"
  ExpiresByType audio/ogg                             "access plus 1 month"
  ExpiresByType image/bmp                             "access plus 1 month"
  ExpiresByType image/gif                             "access plus 1 month"
  ExpiresByType image/jpeg                            "access plus 1 month"
  ExpiresByType image/png                             "access plus 1 month"
  ExpiresByType image/svg+xml                         "access plus 1 month"
  ExpiresByType image/webp                            "access plus 1 month"
  ExpiresByType video/mp4                             "access plus 1 month"
  ExpiresByType video/ogg                             "access plus 1 month"
  ExpiresByType video/webm                            "access plus 1 month"
  ExpiresByType application/wasm                      "access plus 1 year"
  ExpiresByType font/collection                       "access plus 1 month"
  ExpiresByType application/vnd.ms-fontobject         "access plus 1 month"
  ExpiresByType font/eot                              "access plus 1 month"
  ExpiresByType font/opentype                         "access plus 1 month"
  ExpiresByType font/otf                              "access plus 1 month"
  ExpiresByType application/x-font-ttf                "access plus 1 month"
  ExpiresByType font/ttf                              "access plus 1 month"
  ExpiresByType application/font-woff                 "access plus 1 month"
  ExpiresByType application/x-font-woff               "access plus 1 month"
  ExpiresByType font/woff                             "access plus 1 month"
  ExpiresByType application/font-woff2                "access plus 1 month"
  ExpiresByType font/woff2                            "access plus 1 month"
  ExpiresByType text/x-cross-domain-policy            "access plus 1 week"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
  <IfModule mod_filter.c>
    AddOutputFilterByType DEFLATE "application/atom+xml" \
                                  "application/javascript" \
                                  "application/json" \
                                  "application/ld+json" \
                                  "application/manifest+json" \
                                  "application/rdf+xml" \
                                  "application/rss+xml" \
                                  "application/schema+json" \
                                  "application/vnd.geo+json" \
                                  "application/vnd.ms-fontobject" \
                                  "application/wasm" \
                                  "application/x-font-ttf" \
                                  "application/x-javascript" \
                                  "application/x-web-app-manifest+json" \
                                  "application/xhtml+xml" \
                                  "application/xml" \
                                  "font/collection" \
                                  "font/eot" \
                                  "font/opentype" \
                                  "font/otf" \
                                  "font/ttf" \
                                  "image/bmp" \
                                  "image/svg+xml" \
                                  "image/vnd.microsoft.icon" \
                                  "image/x-icon" \
                                  "text/cache-manifest" \
                                  "text/calendar" \
                                  "text/css" \
                                  "text/html" \
                                  "text/javascript" \
                                  "text/plain" \
                                  "text/markdown" \
                                  "text/vcard" \
                                  "text/vnd.rim.location.xloc" \
                                  "text/vtt" \
                                  "text/x-component" \
                                  "text/x-cross-domain-policy" \
                                  "text/xml"
    </IfModule>
</IfModule>

# Fix for https://httpoxy.org vulnerability
<IfModule mod_headers.c>
  RequestHeader unset Proxy
</IfModule>

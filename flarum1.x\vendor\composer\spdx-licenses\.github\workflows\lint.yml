name: "PHP Lint"

on:
  - push
  - pull_request

jobs:
  tests:
    name: "<PERSON><PERSON>"

    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version:
          - "5.3"
          - "8.0"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"

      - name: "Lint PHP files"
        run: "find src/ -type f -name '*.php' -print0 | xargs -0 -L1 -P4 -- php -l -f"
